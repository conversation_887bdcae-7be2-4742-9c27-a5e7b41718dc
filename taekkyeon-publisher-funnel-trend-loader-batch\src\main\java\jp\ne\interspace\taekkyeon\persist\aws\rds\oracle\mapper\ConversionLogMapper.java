/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Options.FlushCachePolicy;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.ApprovedRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.ConversionTrackingFunnelDetails;
import jp.ne.interspace.taekkyeon.model.OccurredRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for the SALES_LOG table.
 *
 * <AUTHOR> Pachpind
 */
public interface ConversionLogMapper {

    String START_TAG_SCRIPT = "<script>";
    String END_TAG_SCRIPT = "</script>";
    String WHERE = "WHERE";
    String AND = "AND";

    /**
        SELECT
            partner_site_no AS siteId,
            SUM(NVL(sales_reward, 0)) AS salesReward,
            SUM(NVL(total_price_reward, 0)) AS transactionAmountReward,
            SUM(NVL(at_commission, 0)) AS atCommission,
            SUM(NVL(agent_commission, 0)) AS merchantAgentCommission,
            SUM(NVL(p_agent_commission, 0)) AS publisherAgentCommission
        FROM
            sales_log
    */
    @Multiline String SELECT_CONVERSION = "";

    /**
        SELECT
            partner_site_no AS siteId,
            min(sl.sales_date) AS firstConversionDate,
            min(CASE WHEN sl.sales_log_status = 1 THEN sl.confirmed_date END) AS firstApprovedConversionDate
        FROM
            sales_log sl
    */
    @Multiline String SELECT_FIRST_CONVERSION_DATE_AND_FIRST_APPROVED_CONVERSION_DATE = "";

    /**
        partner_site_no IN
                <foreach item="item" index="index" collection="targetSiteIds"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
    */
    @Multiline String PUBLISHER_SITE_ID_CONDITION = "";

    /**
       sales_log_status = 1
    */
    @Multiline String APPROVED_CONVERSION_STATUS_CONDITION = "";

    /**
        GROUP BY
            partner_site_no
     */
    @Multiline String GROUP_BY_PUBLISHER_ID = "";

    /**
            created_on > #{targetTimeFrom}
        AND
            #{targetTimeEnd} >= created_on
     */
    @Multiline String TIME_RANGE_CONDITION = "";

    /**
     * Returns the number of sites have approved conversion reward.
     *
     * @param targetSiteIds
     *          given target site IDs
     * @return the number of sites have approved conversion reward
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSION + WHERE
            + APPROVED_CONVERSION_STATUS_CONDITION + AND + PUBLISHER_SITE_ID_CONDITION
            + GROUP_BY_PUBLISHER_ID + END_TAG_SCRIPT)
    @Options(useCache = false, flushCache = FlushCachePolicy.TRUE)
    @ConstructorArgs({ @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "salesReward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmountReward", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class) })
    @MapKey("siteId")
    Map<Long, ApprovedRewardFunnelDetails> findApprovedRewardFunnelDetails(
            @Param("targetSiteIds") List<Long> targetSiteIds);

    /**
     * Returns the number of sites have conversion reward.
     *
     * @param targetSiteIds
     *          given target site IDs
     * @return the number of sites have conversion reward
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSION + WHERE + PUBLISHER_SITE_ID_CONDITION
            + GROUP_BY_PUBLISHER_ID + END_TAG_SCRIPT)
    @Options(useCache = false, flushCache = FlushCachePolicy.TRUE)
    @ConstructorArgs({ @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "salesReward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmountReward", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class) })
    @MapKey("siteId")
    Map<Long, OccurredRewardFunnelDetails> findOccurredRewardFunnelDetails(
            @Param("targetSiteIds") List<Long> targetSiteIds);

    /**
     * Returns the number of sites have first conversion and approved conversion.
     *
     * @param targetSiteIds
     *          given target site IDs
     * @return the number of sites have first conversion and approved conversion
     */
    @Select(START_TAG_SCRIPT
            + SELECT_FIRST_CONVERSION_DATE_AND_FIRST_APPROVED_CONVERSION_DATE + WHERE
            + PUBLISHER_SITE_ID_CONDITION + GROUP_BY_PUBLISHER_ID + END_TAG_SCRIPT)
    @Options(useCache = false, flushCache = FlushCachePolicy.TRUE)
    @ConstructorArgs({ @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "firstConversionDate", javaType = LocalDate.class),
            @Arg(column = "firstApprovedConversionDate", javaType = LocalDate.class) })
    @MapKey("siteId")
    Map<Long, ConversionTrackingFunnelDetails> findConversionTrackingFunnelDetails(
            @Param("targetSiteIds") List<Long> targetSiteIds);

    /**
     * Returns the number of sites have approved conversion reward with time range.
     *
     * @param targetSiteIds
     *          given target site IDs
     * @param targetTimeFrom
     *          start time for filtering
     * @param targetTimeEnd
     *          end time for filtering
     * @return the number of sites have approved conversion reward
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSION + WHERE
            + APPROVED_CONVERSION_STATUS_CONDITION + AND + PUBLISHER_SITE_ID_CONDITION
            + AND + TIME_RANGE_CONDITION + GROUP_BY_PUBLISHER_ID + END_TAG_SCRIPT)
    @Options(useCache = false, flushCache = FlushCachePolicy.TRUE)
    @ConstructorArgs({ @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "salesReward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmountReward", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class) })
    @MapKey("siteId")
    Map<Long, ApprovedRewardFunnelDetails> findApprovedRewardFunnelDetailsWithTimeRange(
            @Param("targetSiteIds") List<Long> targetSiteIds,
            @Param("targetTimeFrom") LocalDateTime targetTimeFrom,
            @Param("targetTimeEnd") LocalDateTime targetTimeEnd);

    /**
     * Returns the number of sites have conversion reward with time range.
     *
     * @param targetSiteIds
     *          given target site IDs
     * @param targetTimeFrom
     *          start time for filtering
     * @param targetTimeEnd
     *          end time for filtering
     * @return the number of sites have conversion reward
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSION + WHERE + PUBLISHER_SITE_ID_CONDITION
            + AND + TIME_RANGE_CONDITION + GROUP_BY_PUBLISHER_ID + END_TAG_SCRIPT)
    @Options(useCache = false, flushCache = FlushCachePolicy.TRUE)
    @ConstructorArgs({ @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "salesReward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmountReward", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class) })
    @MapKey("siteId")
    Map<Long, OccurredRewardFunnelDetails> findOccurredRewardFunnelDetailsWithTimeRange(
            @Param("targetSiteIds") List<Long> targetSiteIds,
            @Param("targetTimeFrom") LocalDateTime targetTimeFrom,
            @Param("targetTimeEnd") LocalDateTime targetTimeEnd);
}
