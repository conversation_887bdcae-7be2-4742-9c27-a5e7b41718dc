/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch.writer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;

import com.google.common.collect.ImmutableSet;

import org.easybatch.core.record.Batch;
import org.easybatch.core.record.Header;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.AdPlatformSite;
import jp.ne.interspace.taekkyeon.model.AffiliationMapRecord;
import jp.ne.interspace.taekkyeon.model.CjApiDetails;
import jp.ne.interspace.taekkyeon.model.CustomerSupport;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.AdPlatformPublisherConnectionMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.AffiliationMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CountryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.SiteMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.model.PersistentAffiliation;
import jp.ne.interspace.taekkyeon.persist.aws.ses.FailSafeEmailSender;
import jp.ne.interspace.taekkyeon.persist.aws.ses.model.AffiliationApprovalEmailParameters;
import jp.ne.interspace.taekkyeon.service.CjSynchronizationService;
import jp.ne.interspace.taekkyeon.service.CountryService;
import jp.ne.interspace.taekkyeon.validator.DatabaseOperationValidator;

import static jp.ne.interspace.taekkyeon.model.CjApiDetails.DEFAULT_CJ_API_DETAILS;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.INDIVIDUAL;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.INFLUENCER;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link AffiliationApprovalRecordWriter}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class AffiliationApprovalRecordWriterTest {

    private static final String KEY1 = "00000000010000000001";
    private static final Long PUBLISHER_SITE_ID1 = 1L;
    private static final String PUBLISHER_FIRST_NAME1 = "firstName1";
    private static final String PUBLISHER_LAST_NAME1 = "lastName1";
    private static final String PUBLISHER_EMAIL1 = "<EMAIL>";
    private static final Long CAMPAIGN_ID1 = 1L;
    private static final String CAMPAIGN_NAME1 = "test1";

    private static final String KEY2 = "00000000020000000002";
    private static final Long PUBLISHER_SITE_ID2 = 2L;
    private static final String PUBLISHER_FIRST_NAME2 = "firstName2";
    private static final String PUBLISHER_LAST_NAME2 = "lastName2";
    private static final String PUBLISHER_EMAIL2 = "<EMAIL>";
    private static final Long CAMPAIGN_ID2 = 2L;
    private static final String CAMPAIGN_NAME2 = "test2";

    private static final String KEY3 = "000000000300000000032";
    private static final Long PUBLISHER_SITE_ID3 = 3L;
    private static final String PUBLISHER_FIRST_NAME3 = "firstName3";
    private static final String PUBLISHER_LAST_NAME3 = "lastName3";
    private static final String PUBLISHER_EMAIL3 = "<EMAIL>";
    private static final Long CAMPAIGN_ID3 = 3L;
    private static final String CAMPAIGN_NAME3 = "test3";

    private static final String RAW_SENDER_EMAIL = "<EMAIL>";
    private static final String RAW_TEMPLATE_FILE = "test.ftl";
    private static final Locale RAW_LOCALE = Locale.JAPAN;
    private static final String RAW_ACCESSTRADE_URL = "http://accesstrade.test";
    private static final String RAW_ISTOOLS_URL = "http://istools.accesstrade.test";
    private static final String RAW_FACEBOOK_URL = "http://facebook.test";
    private static final String RAW_TWITTER_URL = "http://twitter.test";
    private static final String RAW_GOOGLE_PLUS_URL = "http://google.plus.test";

    private static final String EMAIL_SUBJECT_AFFILIATION_1 = " [Auto replied message] Your Partnership Request : Approved - Campaign: 1";
    private static final String EMAIL_SUBJECT_AFFILIATION_2 = " [Auto replied message] Your Partnership Request : Approved - Campaign: 2";
    private static final String INVALID_RECORD_PAYLOAD_MESSAGE = "Invalid record payload for Affiliation Approval Record Writer";

    private static final String NO_EXTERNAL_SITE_ID_ERROR_MESSAGE = "Failed to get CJ External Site ID for site ID:1 and campaign ID:1.";

    private static final String COUNTRY_CODE = "ID";

    private static final String CJ_API_KEY = "testCjApiKey";
    private static final String CJ_PUBLISHER_ID = "1234567890";

    private static final int AD_PLATFORM_ACCESSTRADE = 0;
    private static final int AD_PLATFORM_CJ_AFFILIATE = 1;
    private static final long EXTERNAL_SITE_ID = 111;

    @InjectMocks @Spy
    private AffiliationApprovalRecordWriter underTest;

    @Mock
    private AdPlatformPublisherConnectionMapper adPlatformConnectionMapper;

    @Mock
    private AffiliationMapper affiliationMapper;

    @Mock
    private CampaignMapper campaignMapper;

    @Mock
    private CountryMapper countryMapper;

    @Mock
    private SiteMapper siteMapper;

    @Mock
    private DatabaseOperationValidator validator;

    @Mock
    private FailSafeEmailSender emailSender;

    @Mock
    private Logger logger;

    @Mock
    private CjSynchronizationService cjSynchronizationService;

    @Mock
    private CountryService countryService;

    @Test
    public void testWriteRecordsShouldThrowIllegalArgumentExceptionWhenGivenInvalidPayload()
            throws Exception {
        // given
        Batch batch = createBatchInvalidPayload();

        // when
        try {
            underTest.writeRecords(batch);

            //then
            fail();
        } catch (IllegalArgumentException ex) {
            assertEquals(ex.getMessage(), INVALID_RECORD_PAYLOAD_MESSAGE);
        }
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testWriteRecordShouldCallUpdateAndSendEmailForByOnceWhenCalled()
            throws Exception {
        // given
        Batch batch = createBaseBatch();
        Map<String, PersistentAffiliation> affiliationMap =
                (Map<String, PersistentAffiliation>) batch.iterator().next().getPayload();

        doNothing().when(underTest).sendEmailFor(affiliationMap);
        doNothing().when(underTest).update(affiliationMap);
        doNothing().when(underTest).assignExternalSiteIdBy(affiliationMap);

        // when
        underTest.writeRecords(batch);

        // then
        verify(underTest, times(1)).update(affiliationMap);
        verify(underTest, times(1)).sendEmailFor(affiliationMap);
        verify(underTest, times(1)).assignExternalSiteIdBy(affiliationMap);
    }

    @Test
    public void testSendEmailForShouldCallSendTwiceWhenGivenOneOfTheThreeDataIsInfluencerType() {
        // given
        Map<String, PersistentAffiliation> affiliationMap = createBaseAffiliationMap();
        affiliationMap.put(KEY3,
                new PersistentAffiliation(PUBLISHER_SITE_ID3, PUBLISHER_FIRST_NAME3,
                        PUBLISHER_LAST_NAME3, PUBLISHER_EMAIL3, CAMPAIGN_ID3,
                        CAMPAIGN_NAME3, COUNTRY_CODE, INFLUENCER));
        List<PersistentAffiliation> affiliations
                = new ArrayList<>(affiliationMap.values());

        PersistentAffiliation affiliation1 = affiliations.get(0);
        AffiliationApprovalEmailParameters parameters1 =
                createAffiliationApprovalEmailParameters(affiliation1);

        PersistentAffiliation affiliation2 = affiliations.get(1);
        AffiliationApprovalEmailParameters parameters2 =
                createAffiliationApprovalEmailParameters(affiliation2);

        PersistentAffiliation affiliation3 = affiliations.get(2);

        CustomerSupport customerSupport = new CustomerSupport(RAW_SENDER_EMAIL,
                RAW_ACCESSTRADE_URL);
        doReturn(customerSupport).when(countryService)
                .findCustomerSupportBy(COUNTRY_CODE);
        doReturn(RAW_TEMPLATE_FILE).when(underTest).getTemplateFile();
        doReturn(RAW_LOCALE).when(countryService).findLocaleBy(COUNTRY_CODE);
        doReturn(RAW_ISTOOLS_URL).when(underTest).getIstoolsUrl();
        doReturn(RAW_FACEBOOK_URL).when(underTest).getFacebookUrl();
        doReturn(RAW_TWITTER_URL).when(underTest).getTwitterUrl();
        doReturn(RAW_GOOGLE_PLUS_URL).when(underTest).getGooglePlusUrl();
        doReturn(ImmutableSet.of(INFLUENCER)).when(underTest)
                .getEmailSendingDisabledPublisherTypes();
        doReturn(parameters1).when(underTest).createTemplateParameters(affiliation1,
                customerSupport);
        doReturn(parameters2).when(underTest).createTemplateParameters(affiliation2,
                customerSupport);

        // when
        underTest.sendEmailFor(affiliationMap);

        // then
        verify(emailSender).send(RAW_SENDER_EMAIL,
                affiliation1.getPublisherEmail(), EMAIL_SUBJECT_AFFILIATION_1,
                RAW_TEMPLATE_FILE, parameters1, RAW_LOCALE);
        verify(emailSender).send(RAW_SENDER_EMAIL,
                affiliation2.getPublisherEmail(), EMAIL_SUBJECT_AFFILIATION_2,
                RAW_TEMPLATE_FILE, parameters2, RAW_LOCALE);
        verify(emailSender, never()).send(anyString(),
                eq(affiliation3.getPublisherEmail()), anyString(), anyString(),
                any(AffiliationApprovalEmailParameters.class), any(Locale.class));
        verify(underTest, never()).createTemplateParameters(eq(affiliation3),
                any(CustomerSupport.class));
    }

    @Test
    public void testCreateTemplateParametersShouldReturnCorrectParametersWhenGivenCorrectData() {
        // given
        Map<String, PersistentAffiliation> affiliationMap = createBaseAffiliationMap();
        List<PersistentAffiliation> affiliations
                = new ArrayList<>(affiliationMap.values());
        PersistentAffiliation expected = affiliations.get(0);

        doReturn(RAW_TEMPLATE_FILE).when(underTest).getTemplateFile();
        doReturn(RAW_ISTOOLS_URL).when(underTest).getIstoolsUrl();
        doReturn(RAW_FACEBOOK_URL).when(underTest).getFacebookUrl();
        doReturn(RAW_TWITTER_URL).when(underTest).getTwitterUrl();
        doReturn(RAW_GOOGLE_PLUS_URL).when(underTest).getGooglePlusUrl();
        CustomerSupport customerSupport = new CustomerSupport(RAW_SENDER_EMAIL,
                RAW_ACCESSTRADE_URL);

        // when
        AffiliationApprovalEmailParameters actual = underTest
                .createTemplateParameters(expected, customerSupport);

        // then
        assertFields(expected, actual);
    }

    @Test
    public void testUpdateShouldCallInsertAffiliationRankHistoryOnceWhenCalled() {
        // given
        Map<String, PersistentAffiliation> affiliationMap = createBaseAffiliationMap();
        int resultSize = 2;
        int expectedResultSize = 2;

        when(affiliationMapper.updateAffiliationToApproval(affiliationMap.keySet()))
                .thenReturn(resultSize);

        // when
        underTest.update(affiliationMap);

        // then
        verify(validator).validateModifiedRowCount(resultSize, expectedResultSize);
        verify(affiliationMapper, times(1)).insertAffiliationRankHistory();
    }

    @Test
    public void testAssignExternalSiteIdByShouldDoNothingWhenAdPlatformIdIsZero() {
        // given
        Map<String, PersistentAffiliation> affiliationMap = createBaseAffiliationMap();

        when(campaignMapper.findAdPlatformIdBy(CAMPAIGN_ID1))
                .thenReturn(AD_PLATFORM_ACCESSTRADE);
        when(campaignMapper.findAdPlatformIdBy(CAMPAIGN_ID2))
                .thenReturn(AD_PLATFORM_ACCESSTRADE);

        // when
        underTest.assignExternalSiteIdBy(affiliationMap);

        // then
        verifyNoInteractions(adPlatformConnectionMapper);
        verifyNoInteractions(validator);
        verifyNoInteractions(countryMapper);
        verifyNoInteractions(siteMapper);
        verifyNoInteractions(cjSynchronizationService);
        verify(underTest, never()).insertCjExternalSite(anyLong(), anyLong(), anyLong(),
                anyString());
    }

    @Test
    public void testAssignExternalSiteIdByShouldUpdateExternalSiteIdsAndNotRequestToCjApiWhenAdPlatformIdIsGreaterThanZeroAndAdPlatformIsNotCj() {
        // given
        int adPlatformId = 2;
        Map<String, PersistentAffiliation> affiliationMap = createBaseAffiliationMap();

        when(campaignMapper.findAdPlatformIdBy(CAMPAIGN_ID1)).thenReturn(adPlatformId);
        when(adPlatformConnectionMapper.updateExternalSiteIdAssignmentWith(
                PUBLISHER_SITE_ID1, adPlatformId)).thenReturn(0);
        when(campaignMapper.findAdPlatformIdBy(CAMPAIGN_ID2)).thenReturn(adPlatformId);
        when(adPlatformConnectionMapper.updateExternalSiteIdAssignmentWith(
                PUBLISHER_SITE_ID2, adPlatformId)).thenReturn(1);

        // when
        underTest.assignExternalSiteIdBy(affiliationMap);

        // then
        verify(validator).validateModifiedSingleOrZeroRow(0);
        verify(validator).validateModifiedSingleOrZeroRow(1);
        verifyNoInteractions(siteMapper);
        verifyNoInteractions(countryMapper);
        verifyNoInteractions(cjSynchronizationService);
        verify(underTest, never()).insertCjExternalSite(anyLong(), anyLong(), anyLong(),
                anyString());
    }

    @Test
    public void testAssignExternalSiteIdByShouldRequestToCjApiWhenAdPlatformIsCjAndUpdateOfExternalSiteIdCountIsZeroAndExternalSiteIdIsNull() {
        // given
        Map<String, PersistentAffiliation> affiliationMap = new TreeMap<>();
        affiliationMap.put(KEY1,
                new PersistentAffiliation(PUBLISHER_SITE_ID1, PUBLISHER_FIRST_NAME1,
                        PUBLISHER_LAST_NAME1, PUBLISHER_EMAIL1, CAMPAIGN_ID1,
                        CAMPAIGN_NAME1, COUNTRY_CODE, INDIVIDUAL));
        CjApiDetails cjApiDetails = mock(CjApiDetails.class);

        when(campaignMapper.findAdPlatformIdBy(CAMPAIGN_ID1))
                .thenReturn(AD_PLATFORM_CJ_AFFILIATE);
        when(adPlatformConnectionMapper.updateExternalSiteIdAssignmentWith(
                PUBLISHER_SITE_ID1, AD_PLATFORM_CJ_AFFILIATE)).thenReturn(0);
        AdPlatformSite siteDetails1 = mock(AdPlatformSite.class);
        when(siteDetails1.getExternalSiteId()).thenReturn(null);
        when(siteDetails1.getCountryCode()).thenReturn(COUNTRY_CODE);
        when(siteMapper.findAdPlatformSiteBy(PUBLISHER_SITE_ID1,
                AD_PLATFORM_CJ_AFFILIATE)).thenReturn(siteDetails1);
        doReturn(cjApiDetails).when(underTest).findCjApiDetailsBy(COUNTRY_CODE);
        when(cjSynchronizationService.registerPublisherToCj(siteDetails1, cjApiDetails))
                .thenReturn(EXTERNAL_SITE_ID);

        // when
        underTest.assignExternalSiteIdBy(affiliationMap);

        // then
        verify(validator).validateModifiedSingleOrZeroRow(0);
        verify(cjSynchronizationService, never()).synchronizeWithCjSite(
                any(AdPlatformSite.class), any(CjApiDetails.class));
        verify(cjSynchronizationService).registerPublisherToCj(siteDetails1,
                cjApiDetails);
        verify(underTest).insertCjExternalSite(EXTERNAL_SITE_ID, PUBLISHER_SITE_ID1,
                CAMPAIGN_ID1, COUNTRY_CODE);
    }

    @Test
    public void testAssignExternalSiteIdByShouldNotRequestToCjApiWhenAdPlatformIsCjAndUpdateOfExternalSiteIdCountIsZeroAndExternalSiteIdIsNotNull() {
        // given
        Map<String, PersistentAffiliation> affiliationMap = new TreeMap<>();
        affiliationMap.put(KEY1,
                new PersistentAffiliation(PUBLISHER_SITE_ID1, PUBLISHER_FIRST_NAME1,
                        PUBLISHER_LAST_NAME1, PUBLISHER_EMAIL1, CAMPAIGN_ID1,
                        CAMPAIGN_NAME1, COUNTRY_CODE, INDIVIDUAL));
        CjApiDetails cjApiDetails = mock(CjApiDetails.class);

        when(campaignMapper.findAdPlatformIdBy(CAMPAIGN_ID1))
                .thenReturn(AD_PLATFORM_CJ_AFFILIATE);
        when(adPlatformConnectionMapper.updateExternalSiteIdAssignmentWith(
                PUBLISHER_SITE_ID1, AD_PLATFORM_CJ_AFFILIATE)).thenReturn(0);
        AdPlatformSite siteDetails1 = mock(AdPlatformSite.class);
        when(siteDetails1.getExternalSiteId()).thenReturn(EXTERNAL_SITE_ID);
        when(siteDetails1.getCountryCode()).thenReturn(COUNTRY_CODE);
        when(siteMapper.findAdPlatformSiteBy(PUBLISHER_SITE_ID1,
                AD_PLATFORM_CJ_AFFILIATE)).thenReturn(siteDetails1);
        doReturn(cjApiDetails).when(underTest).findCjApiDetailsBy(COUNTRY_CODE);

        // when
        underTest.assignExternalSiteIdBy(affiliationMap);

        // then
        verify(validator).validateModifiedSingleOrZeroRow(0);
        verifyNoInteractions(cjSynchronizationService);
        verify(underTest, never()).insertCjExternalSite(anyLong(), anyLong(), anyLong(),
                anyString());
    }

    @Test
    public void testAssignExternalSiteIdByShouldRequestToCjApiWhenAdPlatformIsCjAndUpdateOfExternalSiteIdCountIsOne() {
        // given
        Map<String, PersistentAffiliation> affiliationMap = new TreeMap<>();
        affiliationMap.put(KEY1,
                new PersistentAffiliation(PUBLISHER_SITE_ID1, PUBLISHER_FIRST_NAME1,
                        PUBLISHER_LAST_NAME1, PUBLISHER_EMAIL1, CAMPAIGN_ID1,
                        CAMPAIGN_NAME1, COUNTRY_CODE, INDIVIDUAL));
        CjApiDetails cjApiDetails = mock(CjApiDetails.class);

        when(campaignMapper.findAdPlatformIdBy(CAMPAIGN_ID1))
                .thenReturn(AD_PLATFORM_CJ_AFFILIATE);
        when(adPlatformConnectionMapper.updateExternalSiteIdAssignmentWith(
                PUBLISHER_SITE_ID1, AD_PLATFORM_CJ_AFFILIATE)).thenReturn(1);
        AdPlatformSite siteDetails1 = mock(AdPlatformSite.class);
        when(siteDetails1.getCountryCode()).thenReturn(COUNTRY_CODE);
        when(siteMapper.findAdPlatformSiteBy(PUBLISHER_SITE_ID1,
                AD_PLATFORM_CJ_AFFILIATE)).thenReturn(siteDetails1);
        doReturn(cjApiDetails).when(underTest).findCjApiDetailsBy(COUNTRY_CODE);
        doNothing().when(cjSynchronizationService).synchronizeWithCjSite(siteDetails1,
                cjApiDetails);

        // when
        underTest.assignExternalSiteIdBy(affiliationMap);

        // then
        verify(validator).validateModifiedSingleOrZeroRow(1);
        verify(cjSynchronizationService).synchronizeWithCjSite(siteDetails1,
                cjApiDetails);
        verify(underTest, never()).insertCjExternalSite(anyLong(), anyLong(), anyLong(),
                anyString());
        verify(cjSynchronizationService, never()).registerPublisherToCj(
                any(AdPlatformSite.class), any(CjApiDetails.class));
    }

    @Test
    public void testFindCjApiDetailsByShouldReturnCorrectDataWhenCjApiDetailsIsFound() {
        // given
        CjApiDetails expected = createCjApiDetails(CJ_API_KEY, CJ_PUBLISHER_ID);
        when(countryMapper.findCjApiDetailsBy(COUNTRY_CODE)).thenReturn(expected);

        // when
        CjApiDetails actual = underTest.findCjApiDetailsBy(COUNTRY_CODE);

        // then
        assertSame(expected, actual);
    }

    @Test
    public void testFindCjApiDetailsByShouldReturnDefaultCjApiDetailsWhenCjApiDetailsIsNotFound() {
        // given
        when(countryMapper.findCjApiDetailsBy(COUNTRY_CODE)).thenReturn(null);

        // when
        CjApiDetails actual = underTest.findCjApiDetailsBy(COUNTRY_CODE);

        // then
        assertEquals(DEFAULT_CJ_API_DETAILS, actual);
    }

    @Test
    public void testInsertCjExternalSiteShouldLogErrorAndNotThrowTaekkyeonExceptionWhenExternalSiteIdIsZeroAndExceptionThrowableIsFalse() {
        // given
        long externalSiteId = 0;
        doReturn(logger).when(underTest).getLogger();
        doReturn(false).when(underTest).isExceptionThrowable();

        // when
        underTest.insertCjExternalSite(externalSiteId, PUBLISHER_SITE_ID1, CAMPAIGN_ID1,
                COUNTRY_CODE);

        // then
        verify(logger).error(eq(NO_EXTERNAL_SITE_ID_ERROR_MESSAGE));
        verify(adPlatformConnectionMapper, never()).insert(anyInt(), anyLong(), anyLong(),
                anyString());
        verify(validator, never()).validateInsertedRowCount(anyInt());
    }

    @Test
    public void testInsertCjExternalSiteShouldLogErrorAndThrowTaekkyeonExceptionWhenExternalSiteIdIsZeroAndExceptionThrowableIsTrue() {
        // given
        long externalSiteId = 0;
        doReturn(logger).when(underTest).getLogger();
        doReturn(true).when(underTest).isExceptionThrowable();

        try {
            // when
            underTest.insertCjExternalSite(externalSiteId, PUBLISHER_SITE_ID1,
                    CAMPAIGN_ID1, COUNTRY_CODE);
        } catch (TaekkyeonException e) {
            // then
            assertEquals(e.getMessage(), NO_EXTERNAL_SITE_ID_ERROR_MESSAGE);
            verify(logger).error(eq(NO_EXTERNAL_SITE_ID_ERROR_MESSAGE));
            verify(adPlatformConnectionMapper, never()).insert(anyInt(), anyLong(),
                    anyLong(), anyString());
            verify(validator, never()).validateInsertedRowCount(anyInt());
        }
    }

    @Test
    public void testInsertCjExternalSiteShouldInsertWhenExternalSiteIdIsNotZero() {
        // given
        int expected = 1;
        when(adPlatformConnectionMapper.insert(AD_PLATFORM_CJ_AFFILIATE,
                EXTERNAL_SITE_ID, PUBLISHER_SITE_ID1, COUNTRY_CODE)).thenReturn(
                        expected);

        // when
        underTest.insertCjExternalSite(EXTERNAL_SITE_ID, PUBLISHER_SITE_ID1,
                CAMPAIGN_ID1, COUNTRY_CODE);

        // then
        verify(validator).validateInsertedRowCount(expected);
        verify(logger, never()).error(anyString());
    }

    private AffiliationApprovalEmailParameters createAffiliationApprovalEmailParameters(
            PersistentAffiliation affiliation) {
        AffiliationApprovalEmailParameters parameters =
                new AffiliationApprovalEmailParameters(
                        affiliation.getPublisherFirstName(),
                        affiliation.getPublisherLastName(),
                        affiliation.getCampaignName(),
                        RAW_ACCESSTRADE_URL,
                        RAW_ISTOOLS_URL,
                        RAW_SENDER_EMAIL,
                        RAW_FACEBOOK_URL,
                        RAW_TWITTER_URL,
                        RAW_GOOGLE_PLUS_URL);

        return parameters;
    }

    private Map<String, PersistentAffiliation> createBaseAffiliationMap() {
        Map<String, PersistentAffiliation> affiliationMap = new TreeMap<>();
        affiliationMap.put(KEY1,
                new PersistentAffiliation(PUBLISHER_SITE_ID1, PUBLISHER_FIRST_NAME1,
                        PUBLISHER_LAST_NAME1, PUBLISHER_EMAIL1, CAMPAIGN_ID1,
                        CAMPAIGN_NAME1, COUNTRY_CODE, INDIVIDUAL));
        affiliationMap.put(KEY2,
                new PersistentAffiliation(PUBLISHER_SITE_ID2, PUBLISHER_FIRST_NAME2,
                        PUBLISHER_LAST_NAME2, PUBLISHER_EMAIL2, CAMPAIGN_ID2,
                        CAMPAIGN_NAME2, COUNTRY_CODE, INDIVIDUAL));
        return affiliationMap;
    }

    private Batch createBaseBatch() {
        Header header = new Header(null, null, new Date());
        return new Batch(new AffiliationMapRecord(header, createBaseAffiliationMap()));
    }

    private Batch createBatchInvalidPayload() {
        Header header = new Header(null, null, new Date());
        return new Batch(new AffiliationMapRecord(header, null));
    }

    private CjApiDetails createCjApiDetails(String cjApiKey, String cjPublisherId) {
        return new CjApiDetails(cjApiKey, cjPublisherId);
    }

    private void assertFields(PersistentAffiliation expected,
            AffiliationApprovalEmailParameters actual) {
        assertNotNull(actual);
        assertEquals(expected.getPublisherFirstName(), actual.getPublisherFirstName());
        assertEquals(expected.getPublisherLastName(), actual.getPublisherLastName());
        assertEquals(expected.getCampaignName(), actual.getCampaignName());
        assertEquals(RAW_ACCESSTRADE_URL, actual.getAccesstradeUrl());
        assertEquals(RAW_ISTOOLS_URL, actual.getIstoolsUrl());
        assertEquals(RAW_SENDER_EMAIL, actual.getSupportTeamEmail());
        assertEquals(RAW_FACEBOOK_URL, actual.getFacebookUrl());
        assertEquals(RAW_TWITTER_URL, actual.getTwitterUrl());
        assertEquals(RAW_GOOGLE_PLUS_URL, actual.getGooglePlusUrl());
    }
}
