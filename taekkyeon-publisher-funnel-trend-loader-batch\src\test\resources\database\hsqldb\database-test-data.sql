INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE, PARTNER_TYPE_ID, FIRST_ACTIVATION_TIME, CREATED_ON, UPDATED_ON, REGISTRATION_REFERRAL_URL, MEDIA_SOURCE, UTM_MEDIUM, UTM_CONTENT, UTM_CAMPAIGN, UTM_TERM, EMAIL)
VALUES (8, 'ID', 0, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 'http://facebook.com/jarwadi', 'FB', 'medium1', 'content1', 'campaign1', 'term1', 'email8');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE, PARTNER_TYPE_ID, FIRST_ACTIVATION_TIME, CREATED_ON, UPDATED_ON, <PERSON>MAIL)
VALUES (9, 'ID', 1, TO_<PERSON>ATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 'email9');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE, PARTNER_TYPE_ID, FIRST_ACTIVATION_TIME, CREATED_ON, UPDATED_ON, EMAIL)
VALUES (10, 'MY', 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 'email10');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE, PARTNER_TYPE_ID, FIRST_ACTIVATION_TIME, CREATED_ON, UPDATED_ON, EMAIL)
VALUES (11, 'MY', 3, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 'email11');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE, PARTNER_TYPE_ID, FIRST_ACTIVATION_TIME, CREATED_ON, UPDATED_ON, EMAIL)
VALUES (12, 'SG', 0, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 'email12');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE, PARTNER_TYPE_ID, FIRST_ACTIVATION_TIME, CREATED_ON, UPDATED_ON, EMAIL)
VALUES (14, 'SG', 0, NULL, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 'email14');
INSERT INTO PARTNER_ACCOUNT (ACCOUNT_NO, COUNTRY_CODE, PARTNER_TYPE_ID, FIRST_ACTIVATION_TIME, CREATED_ON, UPDATED_ON, EMAIL)
VALUES (15, 'SG', 1, NULL, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 'email15');

INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, FIRST_APPROVED_TIME, SITE_STATE, CREATED_ON, UPDATED_ON)
VALUES (9, 9, TO_DATE('********', 'YYYYMMDD'), 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD') );
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, FIRST_APPROVED_TIME, SITE_STATE, CREATED_ON, UPDATED_ON)
VALUES (10, 10, NULL, 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD') );
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, FIRST_APPROVED_TIME, SITE_STATE, CREATED_ON, UPDATED_ON)
VALUES (11, 11, TO_DATE('********', 'YYYYMMDD'), 0, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD') );
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, FIRST_APPROVED_TIME, SITE_STATE, CREATED_ON, UPDATED_ON)
VALUES (12, 12, TO_DATE('********', 'YYYYMMDD'), 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD') );
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, FIRST_APPROVED_TIME, SITE_STATE, CREATED_ON, UPDATED_ON)
VALUES (15, 15, TO_DATE('********', 'YYYYMMDD'), 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD') );
INSERT INTO PARTNER_SITE (SITE_NO, ACCOUNT_NO, FIRST_APPROVED_TIME, SITE_STATE, CREATED_ON, UPDATED_ON)
VALUES (16, 15, TO_DATE('********', 'YYYYMMDD'), 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD') );

INSERT INTO AFFILIATION (PARTNER_SITE_NO, REGISTRATION_DATE, AFFILIATION_STATUS, APPROVE_DATE, CREATED_ON, UPDATED_ON)
VALUES (9, TO_DATE('********', 'YYYYMMDD'), 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));
INSERT INTO AFFILIATION (PARTNER_SITE_NO, REGISTRATION_DATE, AFFILIATION_STATUS, APPROVE_DATE, CREATED_ON, UPDATED_ON)
VALUES (11, TO_DATE('********', 'YYYYMMDD'), 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));
INSERT INTO AFFILIATION (PARTNER_SITE_NO, REGISTRATION_DATE, AFFILIATION_STATUS, APPROVE_DATE, CREATED_ON, UPDATED_ON)
VALUES (12, TO_DATE('********', 'YYYYMMDD'), 2, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));

INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY(ID, PUBLISHER_ID, PAY_DATE, REWARD_MONTH, AMOUNT, PAYMENT_STATE, CREATED_ON, UPDATED_ON)
VALUES (1, 9, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 123, 3, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY(ID, PUBLISHER_ID, PAY_DATE, REWARD_MONTH, AMOUNT, PAYMENT_STATE, CREATED_ON, UPDATED_ON)
VALUES (2, 10, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 123, 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY(ID, PUBLISHER_ID, PAY_DATE, REWARD_MONTH, AMOUNT, PAYMENT_STATE, CREATED_ON, UPDATED_ON)
VALUES (3, 12, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 123, 0, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY(ID, PUBLISHER_ID, PAY_DATE, REWARD_MONTH, AMOUNT, PAYMENT_STATE, CREATED_ON, UPDATED_ON)
VALUES (4, 14, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 123, 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));
INSERT INTO PUBLISHER_ACCOUNT_PAYMENT_HISTORY(ID, PUBLISHER_ID, PAY_DATE, REWARD_MONTH, AMOUNT, PAYMENT_STATE, CREATED_ON, UPDATED_ON)
VALUES (5, 15, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'), 123, 1, TO_DATE('********', 'YYYYMMDD'), TO_DATE('********', 'YYYYMMDD'));

INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, CREATED_ON, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, DEVICE_OS, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, GOODS_ID, P_AGENT_COMMISSION)
VALUES ('createdOnIs20171121', TO_DATE('2017/11/21', 'YYYY/MM/DD'), TO_DATE('2019/12/22', 'YYYY/MM/DD'), 29, 100, 200, 0, 0, 1, 11, 101.054, 0, 0, 0, 0, 1.01, 1.11, 'goodsId1', 1.12);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, CREATED_ON, UPDATED_ON, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, DEVICE_OS, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, GOODS_ID, P_AGENT_COMMISSION)
VALUES ('updatedOnIs20171122', TO_DATE('2017/11/20', 'YYYY/MM/DD'), TO_DATE('2019/12/23', 'YYYY/MM/DD'), TO_DATE('2019/12/23', 'YYYY/MM/DD'), 30, 100, 200, 1, 1, 2, 12, 102.057, 0, 0, 0, 0, 2.02, 2.22, 'goodsId2', 2.12);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, CREATED_ON, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, DEVICE_OS, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, GOODS_ID, P_AGENT_COMMISSION)
VALUES ('createdOnIs20171121', TO_DATE('2017/11/23', 'YYYY/MM/DD'), TO_DATE('2019/12/22', 'YYYY/MM/DD'), 30, 100, 200, 2, 2, 3, 13, 103.05, 0, 0, 0, 0, 3.03, 3.33, 'goodsId3', 3.12);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, CREATED_ON, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, DEVICE_OS, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, GOODS_ID, P_AGENT_COMMISSION)
VALUES ('createdOnIsTooEarly', TO_DATE('2017/11/20', 'YYYY/MM/DD'), TO_DATE('2019/12/25', 'YYYY/MM/DD'), 31, 101, 201, 3, 3, 4, 14, 104.05, 0, 0, 0, 0, 4.04, 4.44, 'goodsId4', 4.12);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, CREATED_ON, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, DEVICE_OS, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, GOODS_ID, P_AGENT_COMMISSION)
VALUES ('createdOnIsTooLate', TO_DATE('2017/11/23 12:25:11', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2019/12/24', 'YYYY/MM/DD'), 32, 102, 202, 4, 4, 5, 15, 105.05, 0, 0, 0, 0, 5.05, 5.55, 'goodsId5', 5.12);
INSERT INTO BANNER_ACCESS_LOG_SUMMARY (CREATED_BY, CREATED_ON, UPDATED_ON, LOG_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, BANNER_ID, DEVICE_TYPE, DEVICE_OS, IMPRESSION_COUNT, CLICK_COUNT, CLICK_REWARD, RANK, RESULT_ID, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, GOODS_ID, P_AGENT_COMMISSION)
VALUES ('updatedOnIsTooEarly', TO_DATE('2017/11/20', 'YYYY/MM/DD'), TO_DATE('2017/11/20', 'YYYY/MM/DD'), TO_DATE('2019/12/23', 'YYYY/MM/DD'), 33, 102, 202, 5, 5, 6, 16, 106.05, 0, 0, 0, 0, 6.06, 6.66, 'goodsId6', 6.12);

INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK, GOODS_ID, CATEGORY_ID, VERIFY, SESSION_ID, LATEST_POSTBACK_TIME, POINTBACK_ID, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT, CUSTOMER_TYPE, POSTBACK_URL, PUBLISHER_REWARD_IN_USD, AT_COMMISSION_IN_USD, MERCHANT_AGENT_COMMISSION_IN_USD, PUBLISHER_AGENT_COMMISSION_IN_USD, TRANSACTION_AMOUNT_IN_USD, DISCOUNT_AMOUNT_IN_USD, IP, CLICK_IP, LANGUAGE, UNIT_PRICE_IN_USD)
VALUES (9, 'createdOnIs20171121', TO_DATE('2017/11/21 00:00:01', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2017/11/21 12:45:01', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2017/11/21', 'YYYY/MM/DD'), 2, 3, 2, 'internalTransactionId1', 1, 100, 0, 1.1, 2.2, 300, SYSDATE, 'transactionId1', 2, 50, 0, 1, 1.1, 1.1, 1.1, 0, 0, 'uuid', NULL, 5, 'goodsId1', 'categoryId1', 'verify1', 'sessionId1', TO_DATE('2018/08/22', 'YYYY/MM/DD'), 'pointbackId1', 'clickReferer1', 'clickUrl1', 'clickUserAgent1', 'new', 'postbackUrl1', 60, 0.23, 0.14, 0.61, 60, 0, '***********', '***********', 'En', 1.1);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, UPDATED_ON, CLICK_DATE, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK, LATEST_POSTBACK_TIME, POINTBACK_ID, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT, CUSTOMER_TYPE, POSTBACK_URL, POSTBACK_ERROR_DETAILS, POSTBACK_ERROR_RESPONSE_CODE, PUBLISHER_REWARD_IN_USD, AT_COMMISSION_IN_USD, MERCHANT_AGENT_COMMISSION_IN_USD, PUBLISHER_AGENT_COMMISSION_IN_USD, TRANSACTION_AMOUNT_IN_USD, DISCOUNT_AMOUNT_IN_USD, IP, CLICK_IP, LANGUAGE, UNIT_PRICE_IN_USD)
VALUES (10, 'updatedOnIs20171122', TO_DATE('2017/11/21', 'YYYY/MM/DD'), TO_DATE('2017/11/22 12:45:01', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2017/11/22 10:59:59', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2017/11/22 11:59:59', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2017/11/22 12:59:59', 'YYYY/MM/DD HH:MI:SS'), 3, 30, 1, 'internalTransactionId2', 1, 200, 1, 1.1, 2.2, 300, SYSDATE, 'transactionId2', 3, 60, 1, 2, 1.1, 1.1, 1.1, 0, 0, 'uuid', NULL, 4, TO_DATE('2018/08/23', 'YYYY/MM/DD'), 'pointbackId2', 'clickReferer2', 'clickUrl2', 'clickUserAgent2', 'new', 'postbackUrl2', 'postbackErrorDetails', 404, 100, 0.81, 1.01, 1.21, 60, 0, '***********', '***********', 'En', 2.1);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK, LATEST_POSTBACK_TIME, POINTBACK_ID, CLICK_REFERER, CLICK_URL, CLICK_USER_AGENT, CUSTOMER_TYPE)
VALUES (11, 'createdOnIs20171123', TO_DATE('2017/11/23 23:59:59', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2, 1, 'internalTransactionId3', 2, 201, 1, 1.1, 2.2, 300.05, SYSDATE, 'transactionId3', 3, 0, 0, 0, 1.1, 1.1, 1.1, 0, 0, '', NULL, 5, TO_DATE('2018/08/24', 'YYYY/MM/DD'), 'pointbackId3', 'clickReferer3', 'clickUrl3', 'clickUserAgent3', 'new');
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (12, 'createdOnIsTooEarly', TO_DATE('2017/11/21', 'YYYY/MM/DD'), TO_DATE('2017/11/28', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2,0, 'internalTransactionId4', 3, 200, 1, 1.1, 2.2, 300, SYSDATE, '', 3, 0, 0, 0, 1.1, 1.1, 1.1, 0, 0, '', NULL, 5);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (14, 'createdOnIsTooLate', TO_DATE('2017/11/24', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2, 1, 'internalTransactionId5', 4, 200, 1, 1.2, 2.3, 300, SYSDATE, '', 3, 0, 0, 0, 1.1, 1.1, 1.1, 0, 0, '', NULL, 5);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, UPDATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (15, 'updatedOnIsTooEarly', TO_DATE('2017/11/20', 'YYYY/MM/DD'), TO_DATE('2017/11/20', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2, 1, 'internalTransactionId6', 5, 200, 1, 1.2, 2.3, 300, SYSDATE, '', 3, 0, 0, 0, 1.1, 1.1, 1.1, 0, 0, '', NULL, 5);
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, UPDATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (16, 'updatedOnIsTooLate', TO_DATE('2017/11/23 12:25:11', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2017/11/24', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), TO_DATE('2017/11/22', 'YYYY/MM/DD'), 3, 2, 1, 'internalTransactionId7', 6, 200, 1, 1.2, 2.3, 300, SYSDATE, '', 3, 0, 0, 0, 1.1, 1.1, 1.1, 0, 0, '', NULL, 5);

-- Additional test data for time range testing
INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (17, 'timeRangeTest1', TO_DATE('2023/01/15 10:00:00', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2023/01/15', 'YYYY/MM/DD'), TO_DATE('2023/01/15', 'YYYY/MM/DD'), 2, 3, 1, 'timeRangeTransactionId1', 1, 100, 0, 3.30, 6.60, 400, SYSDATE, 'timeRangeTransaction1', 2, 50, 0, 1, 2.20, 2.20, 2.20, 0, 0, 'timeRangeUuid1', NULL, 5);

INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (18, 'timeRangeTest2', TO_DATE('2023/01/16 14:30:00', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2023/01/16', 'YYYY/MM/DD'), TO_DATE('2023/01/16', 'YYYY/MM/DD'), 3, 30, 2, 'timeRangeTransactionId2', 3, 200, 1, 4.40, 8.80, 500, SYSDATE, 'timeRangeTransaction2', 3, 60, 1, 2, 3.30, 3.30, 3.30, 0, 0, 'timeRangeUuid2', NULL, 4);

INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (19, 'timeRangeTest3', TO_DATE('2023/01/17 09:15:00', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2023/01/17', 'YYYY/MM/DD'), TO_DATE('2023/01/17', 'YYYY/MM/DD'), 3, 2, 1, 'timeRangeTransactionId3', 3, 201, 1, 5.50, 11.00, 600, SYSDATE, 'timeRangeTransaction3', 3, 0, 0, 0, 4.40, 4.40, 4.40, 0, 0, 'timeRangeUuid3', NULL, 5);

INSERT INTO SALES_LOG (SEQ_NO, CREATED_BY, CREATED_ON, SALES_DATE, CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, RESULT_ID, SALES_LOG_STATUS, INTERNAL_TRANSACTION_ID, PARTNER_SITE_NO, BANNER_ID, DEVICE_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, TOTAL_PRICE, LOG_DATE, TRANSACTION_ID, SALES_COUNT, PRICE, REWARD_TYPE, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UUID, REWARD_EDIT_DATE, RANK)
VALUES (20, 'timeRangeTestOutside', TO_DATE('2023/01/20 16:00:00', 'YYYY/MM/DD HH:MI:SS'), TO_DATE('2023/01/20', 'YYYY/MM/DD'), TO_DATE('2023/01/20', 'YYYY/MM/DD'), 3, 2, 1, 'timeRangeTransactionIdOutside', 1, 200, 1, 7.70, 15.40, 700, SYSDATE, 'timeRangeTransactionOutside', 3, 0, 0, 0, 6.60, 6.60, 6.60, 0, 0, 'timeRangeUuidOutside', NULL, 5);

-- Test data for PUBLISHER_FUNNEL_TREND table
INSERT INTO PUBLISHER_FUNNEL_TREND (ACCOUNT_ID, ACCOUNT_TYPE, REGISTERED_DATE, ACTIVATED_DATE, FIRST_APPROVED_SITE_DATE, FIRST_AFFILIATION_DATE, FIRST_APPROVE_AFFILIATION_DATE, FIRST_IMPRESSION_OR_CLICK_DATE, FIRST_CONVERSION_DATE, FIRST_APPROVED_CONVERSION_DATE, FIRST_PAYMENT_DATE, REFERRER_ID, UTM_SOURCE, REGISTRATION_REFERRAL_URL, REGISTRATION_REFERRAL_DOMAIN, UTM_MEDIUM, UTM_CONTENT, UTM_CAMPAIGN, UTM_TERM, EMAIL, COUNTRY_CODE, OCCURRED_SALES_REWARD, OCCURRED_TRANSACTION_AMOUNT_REWARD, OCCURRED_AT_COMMISSION, OCCURRED_MERCHANT_AGENT_COMMISSION, OCCURRED_PUBLISHER_AGENT_COMMISSION, APPROVED_SALES_REWARD, APPROVED_TRANSACTION_AMOUNT_REWARD, APPROVED_AT_COMMISSION, APPROVED_MERCHANT_AGENT_COMMISSION, APPROVED_PUBLISHER_AGENT_COMMISSION, LATEST_UPDATED_TIME)
VALUES (8, 1, TO_DATE('2019/12/20', 'YYYY/MM/DD'), TO_DATE('2019/12/21', 'YYYY/MM/DD'), TO_DATE('2019/12/22', 'YYYY/MM/DD'), TO_DATE('2019/12/23', 'YYYY/MM/DD'), TO_DATE('2019/12/24', 'YYYY/MM/DD'), TO_DATE('2019/12/25', 'YYYY/MM/DD'), TO_DATE('2019/12/26', 'YYYY/MM/DD'), TO_DATE('2019/12/27', 'YYYY/MM/DD'), TO_DATE('2019/12/28', 'YYYY/MM/DD'), 100, 'google', 'http://example.com/ref1', 'example.com', 'cpc', 'content1', 'campaign1', 'term1', '<EMAIL>', 'MY', 10.50, 21.00, 5.25, 5.25, 5.25, 8.40, 16.80, 4.20, 4.20, 4.20, TO_DATE('2023/01/10 10:00:00', 'YYYY/MM/DD HH:MI:SS'));

INSERT INTO PUBLISHER_FUNNEL_TREND (ACCOUNT_ID, ACCOUNT_TYPE, REGISTERED_DATE, ACTIVATED_DATE, FIRST_APPROVED_SITE_DATE, FIRST_AFFILIATION_DATE, FIRST_APPROVE_AFFILIATION_DATE, FIRST_IMPRESSION_OR_CLICK_DATE, FIRST_CONVERSION_DATE, FIRST_APPROVED_CONVERSION_DATE, FIRST_PAYMENT_DATE, REFERRER_ID, UTM_SOURCE, REGISTRATION_REFERRAL_URL, REGISTRATION_REFERRAL_DOMAIN, UTM_MEDIUM, UTM_CONTENT, UTM_CAMPAIGN, UTM_TERM, EMAIL, COUNTRY_CODE, OCCURRED_SALES_REWARD, OCCURRED_TRANSACTION_AMOUNT_REWARD, OCCURRED_AT_COMMISSION, OCCURRED_MERCHANT_AGENT_COMMISSION, OCCURRED_PUBLISHER_AGENT_COMMISSION, APPROVED_SALES_REWARD, APPROVED_TRANSACTION_AMOUNT_REWARD, APPROVED_AT_COMMISSION, APPROVED_MERCHANT_AGENT_COMMISSION, APPROVED_PUBLISHER_AGENT_COMMISSION, LATEST_UPDATED_TIME)
VALUES (9, 2, TO_DATE('2019/12/15', 'YYYY/MM/DD'), TO_DATE('2019/12/16', 'YYYY/MM/DD'), TO_DATE('2019/12/17', 'YYYY/MM/DD'), TO_DATE('2019/12/18', 'YYYY/MM/DD'), TO_DATE('2019/12/19', 'YYYY/MM/DD'), TO_DATE('2019/12/20', 'YYYY/MM/DD'), TO_DATE('2019/12/21', 'YYYY/MM/DD'), TO_DATE('2019/12/22', 'YYYY/MM/DD'), TO_DATE('2019/12/23', 'YYYY/MM/DD'), 200, 'facebook', 'http://example.com/ref2', 'example.com', 'social', 'content2', 'campaign2', 'term2', '<EMAIL>', 'MY', 15.75, 31.50, 7.88, 7.88, 7.88, 12.60, 25.20, 6.30, 6.30, 6.30, TO_DATE('2023/01/11 14:30:00', 'YYYY/MM/DD HH:MI:SS'));

INSERT INTO PUBLISHER_FUNNEL_TREND (ACCOUNT_ID, ACCOUNT_TYPE, REGISTERED_DATE, ACTIVATED_DATE, FIRST_APPROVED_SITE_DATE, FIRST_AFFILIATION_DATE, FIRST_APPROVE_AFFILIATION_DATE, FIRST_IMPRESSION_OR_CLICK_DATE, FIRST_CONVERSION_DATE, FIRST_APPROVED_CONVERSION_DATE, FIRST_PAYMENT_DATE, REFERRER_ID, UTM_SOURCE, REGISTRATION_REFERRAL_URL, REGISTRATION_REFERRAL_DOMAIN, UTM_MEDIUM, UTM_CONTENT, UTM_CAMPAIGN, UTM_TERM, EMAIL, COUNTRY_CODE, OCCURRED_SALES_REWARD, OCCURRED_TRANSACTION_AMOUNT_REWARD, OCCURRED_AT_COMMISSION, OCCURRED_MERCHANT_AGENT_COMMISSION, OCCURRED_PUBLISHER_AGENT_COMMISSION, APPROVED_SALES_REWARD, APPROVED_TRANSACTION_AMOUNT_REWARD, APPROVED_AT_COMMISSION, APPROVED_MERCHANT_AGENT_COMMISSION, APPROVED_PUBLISHER_AGENT_COMMISSION, LATEST_UPDATED_TIME)
VALUES (10, 1, TO_DATE('2019/12/10', 'YYYY/MM/DD'), TO_DATE('2019/12/11', 'YYYY/MM/DD'), TO_DATE('2019/12/12', 'YYYY/MM/DD'), TO_DATE('2019/12/13', 'YYYY/MM/DD'), TO_DATE('2019/12/14', 'YYYY/MM/DD'), TO_DATE('2019/12/15', 'YYYY/MM/DD'), TO_DATE('2019/12/16', 'YYYY/MM/DD'), TO_DATE('2019/12/17', 'YYYY/MM/DD'), TO_DATE('2019/12/18', 'YYYY/MM/DD'), NULL, 'direct', 'http://example.com/ref3', 'example.com', 'organic', 'content3', 'campaign3', 'term3', '<EMAIL>', 'MY', 20.25, 40.50, 10.13, 10.13, 10.13, 16.20, 32.40, 8.10, 8.10, 8.10, TO_DATE('2023/01/12 09:15:00', 'YYYY/MM/DD HH:MI:SS'));