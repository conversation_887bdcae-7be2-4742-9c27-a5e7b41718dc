/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionBonusRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionDetails;
import jp.ne.interspace.taekkyeon.model.ConversionInsertRequest;
import jp.ne.interspace.taekkyeon.model.ConversionRank;
import jp.ne.interspace.taekkyeon.model.UpdateConversionByIdDetails;
import jp.ne.interspace.taekkyeon.model.UpdateConversionRankDetails;
import jp.ne.interspace.taekkyeon.model.UpdateConversionRewardEditDateDetails;

import static java.time.LocalDateTime.of;
import static java.util.Collections.emptySet;
import static jp.ne.interspace.taekkyeon.model.CommissionType.GROSS_FIXED_AMOUNT;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.APPROVED;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.PENDING;
import static jp.ne.interspace.taekkyeon.model.DeviceType.IPAD;
import static jp.ne.interspace.taekkyeon.model.PostbackStatus.NOT_NEEDED;
import static jp.ne.interspace.taekkyeon.model.RewardType.CPA_SALES;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link ConversionLogMapper}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ConversionLogMapperTest {

    @Inject
    private ConversionLogMapper underTest;

    @Inject
    private IntegrationTestMapper integrationTestMapper;

    @Test
    public void testFindConversionByShouldReturnCorrectConversionWhenConversionExists() {
        // given
        long conversionId = 1L;

        // when
        Conversion actual = underTest.findConversionBy(conversionId);

        // then
        assertNotNull(actual);
        assertEquals(conversionId, actual.getConversionId().longValue());
        assertEquals(of(2020, 1, 18, 0, 0, 0), actual.getConfirmationDate());
        assertEquals(PENDING, actual.getStatus());
        assertEquals("verify1", actual.getTransactionId());
        assertEquals(101L, actual.getCampaignId().longValue());
        assertEquals(of(2019, 12, 18, 0, 0, 0), actual.getConversionTime());
    }

    @Test
    public void testFindConversionByShouldReturnCorrectConversionWhenConversionDoesNotExist() {
        // given
        long conversionId = 6;

        // when
        Conversion actual = underTest.findConversionBy(conversionId);

        // then
        assertNull(actual);
    }

    @Test
    public void testFindConversionIdsShouldReturnCorrectConversionWhenConversionExists() {
        // given
        String transactionId = "verify2";
        long campaignId = 102;

        // when
        Set<Long> actual = underTest.findConversionIds(transactionId, campaignId);

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertTrue(actual.contains(2L));
        assertTrue(actual.contains(3L));
    }

    @Test
    public void testFindConversionIdsShouldReturnEmptySetWhenConversionDoesNotExist() {
        // given
        String transactionId = "verify2";
        long campaignId = 101;

        // when
        Set<Long> actual = underTest.findConversionIds(transactionId, campaignId);

        // then
        assertNotNull(actual);
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testUpdateStatusShouldReturnOneAndSetRedshiftSyncRequiredToOneWhenUpdateIsSuccessful() {
        // given
        long conversionId = 3;

        // when
        int actual = underTest.updateStatus(conversionId, APPROVED,
                of(2020, 1, 21, 0, 0, 0), "updater");

        // then
        assertEquals(1, actual);
        Integer redshiftSyncRequired = integrationTestMapper
                .getRedshiftSyncRequiredByConversionId(conversionId);
        assertNotNull(redshiftSyncRequired);
        assertEquals(Integer.valueOf(1), redshiftSyncRequired);
    }

    @Test
    public void testUpdateStatusShouldReturnZeroWhenNotUpdate() {
        // given
        long conversionId = 6;

        // when
        int actual = underTest.updateStatus(conversionId, APPROVED,
                of(2020, 1, 21, 0, 0, 0), "updater");

        // then
        assertEquals(0, actual);
    }

    @Test
    public void testInsertShouldReturnOneAndSettingInsertedConversionIdWhenCalled() {
        // given
        ConversionInsertRequest insertRequest = new ConversionInsertRequest(0L, 11L, 22L,
                LocalDateTime.MIN, LocalDateTime.MIN, LocalDateTime.MIN,
                "test transaction ID", "test internal Transaction ID", 33L, 6,
                "test verification Id", 30, "test product ID", APPROVED, 77,
                BigDecimal.ONE, BigDecimal.TEN, CPA_SALES, GROSS_FIXED_AMOUNT, IPAD,
                "test product category ID", BigDecimal.ONE, "creator",
                ZonedDateTime.now(), NOT_NEEDED, "test customer type", "127.0.0.1",
                "en-US1", "test uuid", false);

        // when
        int actual = underTest.insert(insertRequest);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testFindPostbackableConversionIdsShouldReturnCorrectDataWhenCalled() {
        // given
        List<Long> conversionIds = Arrays.asList(18L, 1L, 2L);
        List<Long> expected = Arrays.asList(18L);

        // when
        List<Long> actual = underTest.findPostbackableConversionIds(conversionIds);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testUpdateConversionByShouldReturnCorrectDataWhenCalled() {
        // given
        UpdateConversionByIdDetails updateDetail = new UpdateConversionByIdDetails(18L,
                new BigDecimal("1000.00"), 3, "category 1", "product 1", "empty", 1L,
                new BigDecimal("200.00"), new BigDecimal("200.00"),
                new BigDecimal("200.00"), new BigDecimal("200.00"),
                new BigDecimal("100.00"), new BigDecimal("200.00"),
                new BigDecimal("300.00"), new BigDecimal("400.00"));
        // when
        int actual = underTest.updateConversionBy(updateDetail, "test",
                ZonedDateTime.now(), new BigDecimal("200.00"));

        // then
        assertEquals(1, actual);

    }

    @Test
    public void testUpdatePostbackStatusShouldReturnCorrectDataWhenCalled() {
        // given
        List<Long> conversionIds = Arrays.asList(18L);
        // when
        int actual = underTest.updatePostbackStatus(conversionIds, "test",
                ZonedDateTime.now());

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testFindIdsAndRanksByShouldCorrectDataWhenCalled() {
        // given
        UpdateConversionRankDetails updateDetails = new UpdateConversionRankDetails(102,
                55, 5, LocalDateTime.of(2020, 1, 18, 0, 0),
                LocalDateTime.of(2020, 1, 18, 0, 0));
        YearMonth targetMonth = YearMonth.of(2020, 1);
        LocalDateTime maxCloseTo = LocalDateTime.of(2020, 1, 18, 0, 0);

        // when
        List<ConversionRank> actual = underTest.findIdsAndRanksBy(updateDetails,
                targetMonth, maxCloseTo);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(18, actual.get(0).getId());
        assertEquals(5, actual.get(0).getRank());
    }

    @Test
    public void testUpdateRankAndRewardEditDateNullByShouldReturnCorrectDataWhenCalled() {
        // given
        UpdateConversionRankDetails updateDetails = new UpdateConversionRankDetails(102,
                55, 5, LocalDateTime.of(2020, 1, 18, 0, 0),
                LocalDateTime.of(2020, 1, 18, 0, 0));
        YearMonth targetMonth = YearMonth.of(2020, 1);
        LocalDateTime maxCloseTo = LocalDateTime.of(2020, 1, 18, 0, 0);

        // when
        int actual = underTest.updateRankAndRewardEditDateNullBy(updateDetails, "test",
                ZonedDateTime.now(), targetMonth, maxCloseTo);

        // then
        assertEquals(1, actual);
    }

    @Test
    public void testUpdateRewardEditDateToNullByShouldNotUpdateConversionWhenTheGivenCampaignIsClosedInCampaignClosure() {
        // given
        UpdateConversionRewardEditDateDetails updateDetail =
                new UpdateConversionRewardEditDateDetails(9999L, 3, 5, emptySet(),
                        emptySet(), LocalDateTime.of(2022, 1, 1, 0, 0),
                        LocalDateTime.of(2022, 1, 30, 0, 0));
        String updater = "updater";
        YearMonth targetMonth = YearMonth.of(2022, 4);
        LocalDateTime maxClosedTo = LocalDateTime.of(2022, 4, 15, 0, 0);
        int expected = 0;

        // when
        int actual = underTest.updateRewardEditDateToNullBy(updateDetail, updater,
                targetMonth, maxClosedTo);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testUpdateRewardEditDateToNullByShouldUpdateConversionWhenTheGivenCampaignIsNotClosedInCampaignClosure() {
        // given
        UpdateConversionRewardEditDateDetails updateDetail =
                new UpdateConversionRewardEditDateDetails(1010L, 3, 5, emptySet(),
                        emptySet(), LocalDateTime.of(2022, 1, 1, 0, 0),
                        LocalDateTime.of(2022, 2, 28, 0, 0));
        String updater = "updater";
        YearMonth targetMonth = YearMonth.of(2022, 1);
        LocalDateTime maxClosedTo = LocalDateTime.of(2022, 2, 12, 0, 0);
        int expected = 2;

        // when
        int actual = underTest.updateRewardEditDateToNullBy(updateDetail, updater,
                targetMonth, maxClosedTo);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testUpdateConversionBonusShouldUpdateConversionBonusWhenCalled() {
        // given
        ConversionBonusRegistrationDetails updateDetail = new ConversionBonusRegistrationDetails(
                2L, BigDecimal.valueOf(100), BigDecimal.valueOf(80),
                BigDecimal.valueOf(40), BigDecimal.valueOf(20), 1L, BigDecimal.valueOf(50),
                BigDecimal.valueOf(40), BigDecimal.valueOf(20), BigDecimal.valueOf(10));
        String updater = "updater";
        int expected = 1;

        // when
        int actual = underTest.updateConversionBonus(updateDetail, updater,
                ZonedDateTime.now());

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testFindConversionsByIdsShouldReturnCorrectConversionsWhenConversionExists() {
        // given
        long conversionId = 22;

        // when
        List<ConversionDetails> actual = underTest.findConversionsByIds(
                Arrays.asList(conversionId));

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertEquals(conversionId, actual.get(0).getConversionId());
        assertEquals(of(2022, 2, 25, 0, 0, 0), actual.get(0).getConfirmationDate());
        assertEquals(4, actual.get(0).getSalesCount());
        assertEquals(3, actual.get(0).getResultId().intValue());
        assertEquals(1010, actual.get(0).getCampaignId());
        assertEquals("SG", actual.get(0).getCountryCode());
    }

    @Test
    public void testFindConversionsByIdsShouldReturnEmptyWhenConversionDoesNotExist() {
        // given
        long conversionId = 6;

        // when
        List<ConversionDetails> actual = underTest.findConversionsByIds(
                Arrays.asList(conversionId));

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testUpdateConversionBonusShouldSetRedshiftSyncRequiredToOneWhenBonusIsUpdated() {
        // given
        ConversionBonusRegistrationDetails updateDetail = new ConversionBonusRegistrationDetails(
                2L, BigDecimal.valueOf(200), BigDecimal.valueOf(160),
                BigDecimal.valueOf(80), BigDecimal.valueOf(40), 2L, BigDecimal.valueOf(100),
                BigDecimal.valueOf(80), BigDecimal.valueOf(40), BigDecimal.valueOf(20));
        String updater = "redshift_bonus_updater";
        ZonedDateTime updateTime = ZonedDateTime.now();

        // when
        int actual = underTest.updateConversionBonus(updateDetail, updater, updateTime);

        // then
        assertEquals(1, actual);

        Integer redshiftSyncRequired = integrationTestMapper
                .getRedshiftSyncRequiredByConversionId(updateDetail.getConversionId());
        assertNotNull(redshiftSyncRequired);
        assertEquals(Integer.valueOf(1), redshiftSyncRequired);
    }
}
