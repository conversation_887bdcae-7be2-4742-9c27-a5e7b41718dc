SET DATABASE SQL SYNTAX ORA TRUE;

DROP TABLE IF EXISTS BANNER_ACCESS_LOG_SUMMARY;
CREATE TABLE BANNER_ACCESS_LOG_SUMMARY (
    "BANNER_ID" NUMBER(10,0) NOT NULL, 
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
    "LOG_DATE" DATE NOT NULL, 
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
    "RANK" NUMBER(2,0), 
    "RESULT_ID" NUMBER(4,0) NOT NULL, 
    "TRACKING_SESSION_ID" VARCHAR2(40 BYTE), 
    "IMPRESSION_COUNT" NUMBER(10,0) NOT NULL, 
    "CLICK_COUNT" NUMBER(10,0) NOT NULL, 
    "REWARD_TYPE" NUMBER(1,0) NOT NULL, 
    "CLICK_REWARD" NUMBER(10,2) NOT NULL, 
    "COMMISSION_TYPE" NUMBER(1,0) NOT NULL, 
    "AT_COMMISSION" NUMBER(10,2) NOT NULL, 
    "AGENT_COMMISSION" NUMBER(10,2) NOT NULL, 
    "REWARD_EDIT_DATE" DATE, 
    "DEVICE_TYPE" NUMBER(2,0) NOT NULL,
    "DEVICE_OS" NUMBER(2,0) DEFAULT 0 NOT NULL,
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE, 
    "P_AGENT_COMMISSION" NUMBER(10,2) DEFAULT 0 NOT NULL, 
    "GOODS_ID" VARCHAR2(256 BYTE) DEFAULT ' ', 
    "CATEGORY_ID" VARCHAR2(250 BYTE),
    "CLICK_REWARD_IN_USD" NUMBER(12,2),
    "AT_COMMISSION_IN_USD" NUMBER(12,2),
    "PUBLISHER_AGENT_COMMISSION_IN_USD" NUMBER(12,2),
    "MERCHANT_AGENT_COMMISSION_IN_USD" NUMBER(12,2)
);

DROP TABLE IF EXISTS SALES_LOG;
CREATE TABLE SALES_LOG (
    "SEQ_NO" NUMBER(10,0) NOT NULL, 
    "BANNER_ID" NUMBER(10,0) NOT NULL, 
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "CLICK_DATE" DATE, 
    "SALES_DATE" DATE NOT NULL, 
    "LOG_DATE" DATE NOT NULL, 
    "CONFIRMED_DATE" DATE, 
    "TRANSACTION_ID" VARCHAR2(256 BYTE) NOT NULL, 
    "INTERNAL_TRANSACTION_ID" VARCHAR2(512 BYTE), 
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
    "RANK" NUMBER(2,0), 
    "VERIFY" VARCHAR2(256 BYTE), 
    "RESULT_ID" NUMBER(4,0), 
    "GOODS_ID" VARCHAR2(250 BYTE), 
    "SALES_LOG_STATUS" NUMBER(2,0) NOT NULL, 
    "SALES_COUNT" NUMBER(10,0) NOT NULL, 
    "PRICE" NUMBER(10,0) NOT NULL, 
    "TOTAL_PRICE" NUMBER(10,0) NOT NULL, 
    "REWARD_TYPE" NUMBER(1,0) NOT NULL, 
    "SALES_REWARD" NUMBER(12,2) NOT NULL, 
    "TOTAL_PRICE_REWARD" NUMBER(12,2) NOT NULL, 
    "COMMISSION_TYPE" NUMBER(1,0) NOT NULL, 
    "AT_COMMISSION" NUMBER(12,2) NOT NULL, 
    "AGENT_COMMISSION" NUMBER(12,2) NOT NULL, 
    "P_AGENT_COMMISSION" NUMBER(12,2) DEFAULT 0 NOT NULL, 
    "IP" VARCHAR2(256 BYTE), 
    "MEDIA_URL" VARCHAR2(512 BYTE), 
    "REFERER" VARCHAR2(2048 BYTE), 
    "REPEAT_COUNT" NUMBER(10,0), 
    "USER_AGENT" VARCHAR2(512 BYTE), 
    "REWARD_EDIT_DATE" DATE,
    "DEFAULT_SALES_COUNT" NUMBER(10,0) NOT NULL, 
    "DEFAULT_PRICE" NUMBER(10,0) NOT NULL, 
    "DEFAULT_RESULT_ID" NUMBER(4,0), 
    "LP_URL" VARCHAR2(512 BYTE), 
    "DEVICE_TYPE" NUMBER(2,0), 
    "POINTBACK_ID" VARCHAR2(64 BYTE), 
    "PB_ID_DUPLICATIVE_FLAG" NUMBER(1,0), 
    "PB_ID_OLDEST_SALES_DATE" DATE, 
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE, 
    "NEW_FLAG" NUMBER(1,0) DEFAULT 0, 
    "SESSION_ID" VARCHAR2(256 BYTE), 
    "CURRENCY_ID" NUMBER(4,0), 
    "UUID" VARCHAR2(64 BYTE) DEFAULT NULL, 
    "DEVICE_OS" NUMBER(2,0) DEFAULT 0 NOT NULL, 
    "CATEGORY_ID" VARCHAR2(250 BYTE), 
    "DISCOUNT" NUMBER(12,2) DEFAULT 0,
    "CUSTOMER_TYPE" VARCHAR2(64),
    "AT_COMMISSION_IN_USD" NUMBER(12,2),
    "PUBLISHER_REWARD_IN_USD" NUMBER(12,2),
    "PUBLISHER_AGENT_COMMISSION_IN_USD" NUMBER(12,2),
    "MERCHANT_AGENT_COMMISSION_IN_USD" NUMBER(12,2),
    "TRANSACTION_AMOUNT_IN_USD" NUMBER(12,2),
    "DISCOUNT_AMOUNT_IN_USD" NUMBER(12,2),
    "LANGUAGE" VARCHAR2(40),
    "UNIT_PRICE_IN_USD" NUMBER(12,2),
    "REDSHIFT_SYNC_REQUIRED" NUMBER(1,0) DEFAULT 0
);

DROP TABLE IF EXISTS PARTNER_SITE;
CREATE TABLE PARTNER_SITE (
	"SITE_NO" NUMBER(10,0),
	"ACCOUNT_NO" NUMBER(10,0),
	"SITE_NAME" VARCHAR2(1024) DEFAULT 0,
	"URL" VARCHAR2(1024) DEFAULT 0,
	"DESCRIPTION" VARCHAR2(2000) DEFAULT 0,
	"SITE_TYPE" NUMBER(2,0) DEFAULT 0,
	"SITE_STATE" NUMBER(1,0) DEFAULT 0,
	"CATEGORY_LOW_ID1" NUMBER(10,0),
	"CATEGORY_LOW_ID2" NUMBER(10,0),
	"CATEGORY_LOW_ID3" NUMBER(10,0),
	"MAIN_SITE_FLAG" NUMBER(1,0),
	"POINTBACK_FLAG" NUMBER(1,0),
	"ALL_BANNERS_FLG" NUMBER(1,0) DEFAULT 0,
	"CREATED_BY" VARCHAR2(256),
	"CREATED_ON" DATE,
	"UPDATED_BY" VARCHAR2(256),
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS PARTNER_ACCOUNT;
CREATE TABLE PARTNER_ACCOUNT (
	"ACCOUNT_NO" NUMBER(10,0) NOT NULL,
	"ACCOUNT_TYPE_ID" NUMBER(1,0) DEFAULT 0,
	"PARTNER_TYPE_ID" NUMBER(2,0),
	"CORPORATE_NAME" VARCHAR2(128),
	"SECTION_NAME" VARCHAR2(128),
	"POST_NAME" VARCHAR2(128),
	"LASTNAME" VARCHAR2(64),
	"FIRSTNAME" VARCHAR2(64),
	"EMAIL" VARCHAR2(64),
	"ZIP_CODE" VARCHAR2(8),
	"PREFECTURE" VARCHAR2(128),
	"CITY" VARCHAR2(128),
	"ADDRESS" VARCHAR2(512),
	"ADDRESS2" VARCHAR2(512),
	"PHONE" VARCHAR2(32),
	"BIRTHDAY" DATE,
	"SEX" NUMBER(1,0),
	"COMMERCIAL_REGISTRATION_NUMBER" VARCHAR2(128),
	"VAT_NUMBER" VARCHAR2(128),
	"BANK_ID" VARCHAR2(5),
	"BANK_NAME" VARCHAR2(128),
	"BANK_BRANCH_ID" VARCHAR2(8),
	"BANK_BRANCH_NAME" VARCHAR2(128),
	"BANK_ACCOUNT_TYPE_ID" NUMBER(2,0),
	"BANK_ACCOUNT_NUMBER" VARCHAR2(30),
	"BANK_ACCOUNT_OWNER_LASTNAME" VARCHAR2(128),
	"BANK_ACCOUNT_OWNER_FIRSTNAME" VARCHAR2(128),
	"URL" VARCHAR2(256),
	"LOGIN_NAME" VARCHAR2(64),
	"LOGIN_PASSWORD" VARCHAR2(32),
	"ACCOUNT_STATE" NUMBER(1,0) NOT NULL,
	"APPLIED_DATE" DATE NOT NULL,
	"QUIT_DATE" DATE,
	"ORIGIN_NO" NUMBER(10,0),
	"CREATED_BY" VARCHAR2(256),
	"CREATED_ON" DATE,
	"UPDATED_BY" VARCHAR2(256),
	"UPDATED_ON" DATE,
	"BLACKLIST_FLAG" NUMBER(1,0),
	"U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
	"AGENCY_ID" NUMBER(10,0) DEFAULT 0
);

DROP TABLE IF EXISTS PUBLISHER_AGENCY;
CREATE TABLE PUBLISHER_AGENCY (
	"ID" NUMBER(10,0),
	"NAME" VARCHAR2(128),
	"COMMISSION_POLICY" NUMBER(2,0) DEFAULT 0,
	"CREATED_BY" VARCHAR2(256),
	"CREATED_ON" DATE,
	"UPDATED_BY" VARCHAR2(256),
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN;
CREATE TABLE MERCHANT_CAMPAIGN (
	"CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
	"ACCOUNT_NO" NUMBER(10,0) NOT NULL, 
	"CAMPAIGN_STATE_ID" NUMBER(2,0) NOT NULL, 
	"CREATIVE_SYNC_STATUS" NUMBER(1,0), 
	"CAMPAIGN_NAME" VARCHAR2(512) NOT NULL,
	"IMAGE_URL" VARCHAR2(512), 
	"URL" VARCHAR2(512) NOT NULL, 
	"DESCRIPTION" VARCHAR2(4000), 
	"CATEGORY1" NUMBER(10,0) NOT NULL, 
	"CATEGORY2" NUMBER(10,0) NOT NULL, 
	"CATEGORY3" NUMBER(10,0) NOT NULL,
	"AUTO_AFF_LIMITATION_OPTION" NUMBER(1,0) NOT NULL, 
	"AUTO_AFF_LIMITATION_DIVISION" NUMBER(1,0) NOT NULL,
	"AFF_CONDITION_SPECIAL" VARCHAR2(2048), 
	"RESULT_APPROVAL_SPECIAL" VARCHAR2(2000), 
	"PR_FOR_PARTNER" VARCHAR2(4000), 
	"GET_PARAMETER_FLAG" NUMBER(1,0) NOT NULL, 
	"POINTBACK_PERMISSION" NUMBER(1,0) NOT NULL, 
	"SELF_CONVERSION_FLAG" NUMBER(1,0), 
	"CAMPAIGN_START_DATE" DATE, 
	"CAMPAIGN_END_DATE" DATE, 
	"AUTO_AFF_APPR_DURATION" NUMBER(2,0) DEFAULT 3, 
	"OEM_FLAG" NUMBER(1,0), 
	"AUTO_ACTION_APPR_DURATION" NUMBER(2,0), 
	"HIDDEN_FLAG" NUMBER(1,0), 
	"START_DATE" DATE, 
	"END_DATE" DATE,
	"OVERLAP_FLG" NUMBER(1,0) DEFAULT 0, 
	"OFFER_CODE" VARCHAR2(32), 
	"DESCRIPTION_EN" VARCHAR2(4000), 
	"CAMPAIGN_TYPE" NUMBER(2,0) DEFAULT 0,
	"CURRENCY" VARCHAR2(3) DEFAULT 'USD',
	"CREATED_BY" VARCHAR2(256), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256), 
	"UPDATED_ON" DATE, 
	"DEVICE_TYPES" VARCHAR2(128)
);

DROP TABLE IF EXISTS MERCHANT_ACCOUNT;
CREATE TABLE MERCHANT_ACCOUNT (
	"ACCOUNT_NO" NUMBER(10,0) NOT NULL,
	"MERCHANT_TYPE_ID" NUMBER(1,0),
	"CORPORATE_NAME" VARCHAR2(128),
	"CORPORATE_ZIP_CODE" VARCHAR2(10),
	"CORPORATE_PREFECTURE" VARCHAR2(128),
	"CORPORATE_CITY" VARCHAR2(128),
	"CORPORATE_ADDRESS" VARCHAR2(128),
	"CORPORATE_ADDRESS2" VARCHAR2(128),
	"CORPORATE_PHONE" VARCHAR2(32),
	"CORPORATE_FAX" VARCHAR2(32),
	"CORPORATE_DIRECTOR_NAME" VARCHAR2(128),
	"CORPORATE_REMARK" VARCHAR2(2000),
	"FOSTER_LASTNAME" VARCHAR2(64),
	"FOSTER_FIRSTNAME" VARCHAR2(64),
	"FOSTER_MIDDLENAME" VARCHAR2(64),
	"FOSTER_ZIP_CODE" VARCHAR2(10),
	"FOSTER_PREFECTURE" VARCHAR2(128),
	"FOSTER_CITY" VARCHAR2(128),
	"FOSTER_ADDRESS" VARCHAR2(128),
	"FOSTER_ADDRESS2" VARCHAR2(128),
	"FOSTER_SECTION_NAME" VARCHAR2(128),
	"FOSTER_POST_NAME" VARCHAR2(128),
	"FOSTER_EMAIL" VARCHAR2(64),
	"FOSTER_PHONE" VARCHAR2(32),
	"FOSTER_FAX" VARCHAR2(32),
	"FOSTER_REMARK" VARCHAR2(2000),
	"LOGIN_NAME" VARCHAR2(64),
	"LOGIN_PASSWORD" VARCHAR2(32),
	"ACCOUNT_STATE" NUMBER(1,0),
	"CREATED_BY" VARCHAR2(256),
	"CREATED_ON" DATE,
	"UPDATED_BY" VARCHAR2(256),
	"UPDATED_ON" DATE,
	"ACCOUNTANT_LASTNAME" VARCHAR2(64),
	"ACCOUNTANT_FIRSTNAME" VARCHAR2(64),
	"ACCOUNTANT_MIDDLENAME" VARCHAR2(64),
	"ACCOUNTANT_EMAIL" VARCHAR2(64),
	"ACCOUNTANT_PHONE" VARCHAR2(32),
	"U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
	"COUNTRY_CODE" VARCHAR2(2) NOT NULL
);