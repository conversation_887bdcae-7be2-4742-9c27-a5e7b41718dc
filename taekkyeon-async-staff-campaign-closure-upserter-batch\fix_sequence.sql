-- Script to fix campaign_closure_history_seq
-- Run this to check current state and fix sequence if needed

-- 1. Check current sequence value
SELECT 
    'Current sequence value: ' || campaign_closure_history_seq.CURRVAL as info
FROM dual;

-- 2. Check max ID in table
SELECT 
    'Max ID in table: ' || MAX(id) as max_id
FROM campaign_closure_history;

-- 3. Check if sequence is behind
SELECT 
    CASE 
        WHEN seq_val <= max_id THEN 'SEQUENCE IS BEHIND - NEEDS FIX'
        ELSE 'SEQUENCE IS OK'
    END as status,
    'Sequence: ' || seq_val || ', Max ID: ' || max_id as details
FROM (
    SELECT 
        campaign_closure_history_seq.CURRVAL as seq_val,
        NVL(MAX(id), 0) as max_id
    FROM campaign_closure_history
);

-- 4. Check constraint details
SELECT 
    ucc.constraint_name,
    ucc.table_name,
    ucc.column_name,
    uc.constraint_type
FROM user_cons_columns ucc
JOIN user_constraints uc ON ucc.constraint_name = uc.constraint_name
WHERE ucc.constraint_name = 'SYS_C00312473';

-- 5. Check for duplicate campaign_closure_id
SELECT 
    campaign_closure_id,
    COUNT(*) as duplicate_count
FROM campaign_closure_history 
GROUP BY campaign_closure_id 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 6. Fix sequence if needed (uncomment and run if sequence is behind)
/*
DECLARE
    max_id NUMBER;
    new_seq_val NUMBER;
BEGIN
    SELECT NVL(MAX(id), 0) + 1 INTO max_id FROM campaign_closure_history;
    
    EXECUTE IMMEDIATE 'ALTER SEQUENCE campaign_closure_history_seq INCREMENT BY ' || max_id;
    SELECT campaign_closure_history_seq.NEXTVAL INTO new_seq_val FROM dual;
    EXECUTE IMMEDIATE 'ALTER SEQUENCE campaign_closure_history_seq INCREMENT BY 1';
    
    DBMS_OUTPUT.PUT_LINE('Sequence fixed. New value: ' || new_seq_val);
END;
/
*/
