/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.FirstApprovedAffiliationDetails;
import jp.ne.interspace.taekkyeon.model.PublisherAccountFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherAccountType;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsByTime;
import jp.ne.interspace.taekkyeon.model.Site;

import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.INDIVIDUAL;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.LOCAL_COMPANY;
import static jp.ne.interspace.taekkyeon.model.PublisherAccountType.OVERSEAS_COMPANY;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * Integration test for {@link PublisherAccountFunnelMapper}.
 *
 * <AUTHOR> Van Nguyen
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class PublisherAccountFunnelMapperTest {

    @Inject
    private PublisherAccountFunnelMapper underTest;

    @Test
    public void testFindPublisherAccountFunnelDetailsByShouldReturnCorrectDataWhenSearchResultFound() {
        // given
        List<Long> publisherIds = Arrays.asList(8L, 9L, 10L, 100L);

        // when
        Map<Long, PublisherAccountFunnelDetails> actual = underTest
                .findPublisherAccountFunnelDetailsBy(publisherIds);

        // then
        assertNotNull(actual);
        assertEquals(3, actual.size());
        assertFields(actual.get(8L), "ID", 8L, INDIVIDUAL, LocalDate.of(2019, 12, 21),
                LocalDate.of(2019, 12, 24), null, null, null,
                "http://facebook.com/jarwadi", "FB", "medium1", "content1", "campaign1",
                "term1", "email8");
        assertFields(actual.get(9L), "ID", 9L, LOCAL_COMPANY, LocalDate.of(2019, 12, 23),
                LocalDate.of(2019, 12, 23), LocalDate.of(2019, 12, 23),
                LocalDate.of(2019, 12, 21), LocalDate.of(2019, 12, 24), null, null, null,
                null, null, null, "email9");
        assertFields(actual.get(10L), "MY", 10L, OVERSEAS_COMPANY,
                LocalDate.of(2019, 12, 25), LocalDate.of(2019, 12, 22), null, null,
                LocalDate.of(2019, 12, 23), null, null, null, null, null, null, "email10");
    }

    @Test
    public void testFindPublisherAccountFunnelDetailsByShouldReturnCorrectDataWhenSearchResultNotFound() {
        // given
        List<Long> publisherIds = Arrays.asList(800L, 900L, 100L);

        // when
        Map<Long, PublisherAccountFunnelDetails> actual = underTest
                .findPublisherAccountFunnelDetailsBy(publisherIds);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testFindFirstApprovedAffiliationDetailsByShouldReturnCorrectDataWhenSearchDataFound() {
        // given
        List<Long> publisherIds = Arrays.asList(8L, 9L, 10L, 100L);

        // when
        Map<Long, FirstApprovedAffiliationDetails> actual = underTest
                .findFirstApprovedAffiliationDetailsBy(publisherIds);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertFields(actual.get(9L), 9L, LocalDate.of(2019, 12, 23));
    }

    @Test
    public void testFindFirstApprovedAffiliationDetailsByShouldReturnEmptyWhenSearchDataNotFound() {
        // given
        List<Long> publisherIds = Arrays.asList(110L, 199L);

        // when
        Map<Long, FirstApprovedAffiliationDetails> actual = underTest
                .findFirstApprovedAffiliationDetailsBy(publisherIds);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testFindActivatedSiteIdsByShouldReturnCorrectDataWhenSearchDataFound() {
        // when
        List<Site> actual = underTest
                .findActivatedSiteIdsBy(Arrays.asList(15L));

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertSiteFields(actual.get(0), 15L, 15L);
        assertSiteFields(actual.get(1), 15L, 16L);
    }

    @Test
    public void testFindActivatedSiteIdsByReturnEmptyWhenSearchDataNotFound() {
        // when
        List<Site> actual = underTest
                .findActivatedSiteIdsBy(Arrays.asList(150L));

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testFindPublisherFunnelDetailsByTimeShouldReturnEmptyWhenNoDataFound() {
        // given
        List<Long> publisherIds = Arrays.asList(999L, 1000L);

        // when
        List<PublisherFunnelDetailsByTime> actual = underTest
                .findPublisherFunnelDetailsByTime(publisherIds);

        // then
        assertTrue(actual.isEmpty());
    }

    @Test
    public void testUpsertPublisherFunnelTrendSummaryAndFindPublisherFunnelDetailsByTimeShouldUpsertDataCorrectlyWhenCalled() {
        // given
        List<PublisherFunnelDetails> publisherFunnelDetails = Arrays.asList(
                createPublisherFunnelDetails(11L, PublisherAccountType.INDIVIDUAL,
                        LocalDate.of(2023, 1, 15), LocalDate.of(2023, 1, 16),
                        LocalDate.of(2023, 1, 17), LocalDate.of(2023, 1, 18),
                        LocalDate.of(2023, 1, 19), LocalDate.of(2023, 1, 20),
                        LocalDate.of(2023, 1, 21), LocalDate.of(2023, 1, 22),
                        LocalDate.of(2023, 1, 23), 300L, "twitter",
                        "http://example.com/ref4", "example.com", "social",
                        "content4", "campaign4", "term4", "<EMAIL>",
                        "MY", BigDecimal.valueOf(25.00), BigDecimal.valueOf(50.00),
                        new BigDecimal("12.50"), new BigDecimal("12.50"),
                        new BigDecimal("12.50"), new BigDecimal("20.00"),
                        new BigDecimal("40.00"), new BigDecimal("10.00"),
                        new BigDecimal("10.00"), new BigDecimal("10.00")),
                createPublisherFunnelDetails(8L, PublisherAccountType.INDIVIDUAL,
                        LocalDate.of(2019, 12, 20), LocalDate.of(2019, 12, 21),
                        LocalDate.of(2019, 12, 22), LocalDate.of(2019, 12, 23),
                        LocalDate.of(2019, 12, 24), LocalDate.of(2019, 12, 25),
                        LocalDate.of(2019, 12, 26), LocalDate.of(2019, 12, 27),
                        LocalDate.of(2019, 12, 28), 100L, "google",
                        "http://example.com/ref1", "example.com", "cpc",
                        "content1", "campaign1", "term1", "<EMAIL>",
                        "MY", BigDecimal.valueOf(30.00), BigDecimal.valueOf(60.00),
                        new BigDecimal("15.00"), new BigDecimal("15.00"),
                        new BigDecimal("15.00"), new BigDecimal("24.00"),
                        new BigDecimal("48.00"), new BigDecimal("12.00"),
                        new BigDecimal("12.00"), new BigDecimal("12.00"))
        );

        // when
        int result = underTest.upsertPublisherFunnelTrendSummary(publisherFunnelDetails,
                LocalDateTime.now());

        // then
        assertEquals(2, result);
        List<PublisherFunnelDetailsByTime> updatedData = underTest
                .findPublisherFunnelDetailsByTime(Arrays.asList(8L, 11L));
        assertEquals(2, updatedData.size());
        PublisherFunnelDetailsByTime account = updatedData.stream()
                .filter(item -> item.getAccountId() == 8L)
                .findFirst().orElse(null);
        assertNotNull(account);
        assertEquals(new BigDecimal("30.00"), account.getOccurredSalesReward());
        assertEquals(new BigDecimal("24.00"), account.getApprovedSalesReward());
        PublisherFunnelDetailsByTime account2 = updatedData.stream()
                .filter(item -> item.getAccountId() == 11L)
                .findFirst().orElse(null);
        assertNotNull(account2);
        assertEquals(new BigDecimal("25.00"), account2.getOccurredSalesReward());
        assertEquals(new BigDecimal("20.00"), account2.getApprovedSalesReward());
    }

    private void assertFields(PublisherAccountFunnelDetails actual,
            String expectedCountryCode, long expectedAccountId,
            PublisherAccountType expectedAccountType, LocalDate expectedRegisteredDate,
            LocalDate expectedActivatedDate, LocalDate expectedFirstApprovedSiteDate,
            LocalDate expectedFirstAffiliationDate, LocalDate expectedFirstPaymentDate,
            String expectedRegistrationReferralUrl, String expectedUtmSource,
            String expectedUtmMedium, String expectedUtmContent,
            String expectedUtmCampaign, String expectedUtmTerm, String expectedEmail) {
        assertNotNull(actual);
        assertEquals(expectedCountryCode, actual.getCountryCode());
        assertEquals(expectedAccountId, actual.getAccountId());
        assertEquals(expectedAccountType, actual.getAccountType());
        assertEquals(expectedRegisteredDate, actual.getRegisteredDate());
        assertEquals(expectedActivatedDate, actual.getActivatedDate());
        assertEquals(expectedFirstApprovedSiteDate, actual.getFirstApprovedSiteDate());
        assertEquals(expectedFirstAffiliationDate, actual.getFirstAffiliationDate());
        assertEquals(expectedFirstPaymentDate, actual.getFirstPaymentDate());
        assertEquals(expectedRegistrationReferralUrl,
                actual.getRegistrationReferralUrl());
        assertEquals(expectedUtmSource, actual.getUtmSource());
        assertEquals(expectedUtmMedium, actual.getUtmMedium());
        assertEquals(expectedUtmContent, actual.getUtmContent());
        assertEquals(expectedUtmCampaign, actual.getUtmCampaign());
        assertEquals(expectedUtmTerm, actual.getUtmTerm());
        assertEquals(expectedEmail, actual.getEmail());
    }

    private void assertFields(FirstApprovedAffiliationDetails actual, long expectedAccountId,
            LocalDate expectedRegisteredDate) {
        assertEquals(expectedAccountId, actual.getAccountId());
        assertEquals(expectedRegisteredDate, actual.getFirstApprovedAffiliationDate());
    }

    private void assertSiteFields(Site actual, long expectedPublisherId,
            long expectedSiteId) {
        assertNotNull(actual);
        assertEquals(expectedPublisherId, actual.getPublisherId());
        assertEquals(expectedSiteId, actual.getSiteId());
    }

    private PublisherFunnelDetails createPublisherFunnelDetails(long accountId,
            PublisherAccountType accountType, LocalDate registeredDate,
            LocalDate activatedDate, LocalDate firstApprovedSiteDate,
            LocalDate firstAffiliationDate, LocalDate firstApproveAffiliationDate,
            LocalDate firstImpressionOrClickDate, LocalDate firstConversionDate,
            LocalDate firstApprovedConversionDate, LocalDate firstPaymentDate,
            Long referrerId, String utmSource, String registrationReferralUrl,
            String registrationReferralDomain, String utmMedium, String utmContent,
            String utmCampaign, String utmTerm, String email, String countryCode,
            BigDecimal occurredSalesReward, BigDecimal occurredTransactionAmountReward,
            BigDecimal occurredAtCommission, BigDecimal occurredMerchantAgentCommission,
            BigDecimal occurredPublisherAgentCommission, BigDecimal approvedSalesReward,
            BigDecimal approvedTransactionAmountReward, BigDecimal approvedAtCommission,
            BigDecimal approvedMerchantAgentCommission,
            BigDecimal approvedPublisherAgentCommission) {
        return new PublisherFunnelDetails(accountId, countryCode, referrerId, utmSource,
                accountType, registeredDate, activatedDate, firstApprovedSiteDate,
                firstAffiliationDate, firstApproveAffiliationDate,
                firstImpressionOrClickDate, firstConversionDate,
                firstApprovedConversionDate, firstPaymentDate, occurredSalesReward,
                occurredTransactionAmountReward, occurredAtCommission,
                occurredMerchantAgentCommission, occurredPublisherAgentCommission,
                approvedSalesReward, approvedTransactionAmountReward, approvedAtCommission,
                approvedMerchantAgentCommission, approvedPublisherAgentCommission,
                registrationReferralUrl, registrationReferralDomain, utmMedium,
                utmContent, utmCampaign, utmTerm, email);
    }
}
