/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import lombok.Getter;

import org.easybatch.core.record.Batch;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.batch.processor.AsyncStaffConversionUpdaterRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.AsyncStaffConversionUpdaterRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.AsyncStaffConversionUpdaterRecordWriter;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonIntegrationTestHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.BonusStatus;
import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.ConversionUpdateMessageRecord;
import jp.ne.interspace.taekkyeon.model.ConversionUpdateRecord;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.IntegrationTestClickParameter;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversion;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversionBonus;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversionRankUpdatedHistory;
import jp.ne.interspace.taekkyeon.model.IntegrationTestFixedBonus;
import jp.ne.interspace.taekkyeon.model.IntegrationTestFixedBonusDetails;
import jp.ne.interspace.taekkyeon.model.IntegrationTestInvoicePaymentTax;
import jp.ne.interspace.taekkyeon.model.InvoiceAndPaidDate;
import jp.ne.interspace.taekkyeon.model.PostbackStatus;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.module.AsyncStaffConversionUpdaterPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.module.SimpleQueueServiceQueueConsumerModule;
import jp.ne.interspace.taekkyeon.module.TaekkyeonHttpClientModule;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.IntegrationTestMapper;
import jp.ne.interspace.taekkyeon.persist.aws.s3.SimpleStorageServiceClient;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.ConversionInsertionQueue;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.ConversionUpdateQueue;

import static java.time.LocalDate.of;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.CommissionType.GROSS_AMOUNT_SOLD;
import static jp.ne.interspace.taekkyeon.model.CommissionType.GROSS_FIXED_AMOUNT;
import static jp.ne.interspace.taekkyeon.model.CommissionType.NET;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.APPROVED;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.PENDING;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.REJECTED;
import static jp.ne.interspace.taekkyeon.model.DeviceType.ANDROID_TAB;
import static jp.ne.interspace.taekkyeon.model.DeviceType.DESKTOP;
import static jp.ne.interspace.taekkyeon.model.DeviceType.IPAD;
import static jp.ne.interspace.taekkyeon.model.DeviceType.IPHONE;
import static jp.ne.interspace.taekkyeon.model.PostbackStatus.NOT_NEEDED;
import static jp.ne.interspace.taekkyeon.model.RewardType.CPA_FIXED;
import static jp.ne.interspace.taekkyeon.module.AsyncStaffConversionUpdaterModule.BIND_KEY_CONVERSION_UPDATE_REQUESTS_S3_BUCKET;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.ADDITIONAL_PARAMETERS_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.CLICK_DATE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.DEVICE_TYPE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.IP_ADDRESS_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.LANGUAGE_COLUMN_NAME;
import static jp.ne.interspace.taekkyeon.persist.aws.dynamodb.SessionDynamoDbTable.RK_COLUMN_NAME;
import static lombok.AccessLevel.PACKAGE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

/**
 * Integration test for async staff conversion updater batch.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonIntegrationTestHsqldbJunitRunner.class)
@TaekkyeonModules({ AsyncStaffConversionUpdaterPropertiesJunitModule.class,
        TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class,
        SimpleQueueServiceQueueConsumerModule.class,
        TaekkyeonHttpClientModule.class})
public class AsyncStaffConversionUpdaterIntegrationTest {
    private static final String UPDATER = "0123456789abcdefghijklmnopqrstu1";
    private static final String CREATOR = "0123456789abcdefghijklmnopqrstu3";

    private static final String UUID_1 = "34ed066df378efacc9b924ec161e7639testClickId1";
    private static final String UUID_2 = "34ed066df378efacc9b924ec161e7639testClickId2";
    private static final String UUID_3 = "34ed066df378efacc9b924ec161e7639testClickId3";
    private static final String RK_1 = "0000YA0000DX";
    private static final String RK_2 = "0004DQ0000DY";
    private static final String RK_3 = "0006YC0000DZ";
    private static final Map<String, AttributeValue> ADDITIONAL_PARAMETERS_1 =
            ImmutableMap.of("param1", new AttributeValue("value1"),
                    "param2", new AttributeValue("value2"));
    private static final Map<String, AttributeValue> ADDITIONAL_PARAMETERS_2 =
            ImmutableMap.of("param3", new AttributeValue("value3"),
                    "param4", new AttributeValue("value4"));
    private static final Map<String, AttributeValue> ADDITIONAL_PARAMETERS_3 =
            ImmutableMap.of("param5", new AttributeValue("value5"),
                    "param6", new AttributeValue("value6"));
    private static final String CLICK_TIME_1 = "2020-04-01 11:22:33";
    private static final String CLICK_TIME_2 = "2020-04-02 22:33:44";
    private static final String CLICK_TIME_3 = "2020-04-03 01:02:03";
    private static final String DEVICE_TYPE_1 = "5";
    private static final String DEVICE_TYPE_2 = "6";
    private static final String DEVICE_TYPE_3 = "4";
    private static final String IP_ADDRESS_1 = "127.0.0.1";
    private static final String IP_ADDRESS_2 = "*********";
    private static final String LANGUAGE_1 = "en-US";
    private static final String LANGUAGE_2 = "vi-VN";

    private static final BigDecimal ZERO = new BigDecimal("0.00");
    private static final int TEST_COUNT = 11;
    private static final String RANDOM_ID = UUID.randomUUID().toString();

    private static final long CONVERSION_ID = 10;

    private static boolean isSetUpDone = false;
    private static int testedCount = 0;

    private static final int ERROR_COUNT = 0;
    private static final long CAMPAIGN_ID = 1;
    private static final String STAFF_EMAIL = "<EMAIL>";
    private static final long DATA_COUNT = 1;

    @Inject
    private AsyncStaffConversionUpdaterRecordProcessor recordProcessor;

    @Inject
    private AsyncStaffConversionUpdaterRecordReader recordReader;

    @Inject
    private AsyncStaffConversionUpdaterRecordWriter recordWriter;

    @Inject
    private ConversionInsertionQueue conversionInsertionQueue;

    @Inject
    private ConversionUpdateQueue conversionUpdateQueue;

    @Inject
    private SimpleStorageServiceClient s3Client;

    @Inject
    private DynamoDbTable dynamoDbTable;

    @Inject
    private IntegrationTestMapper testMapper;

    @Inject @Named(BIND_KEY_CONVERSION_UPDATE_REQUESTS_S3_BUCKET) @Getter(PACKAGE)
    private String conversionUpdateRequestsBucketName;

    @Before
    public void setup() {
        if (!isSetUpDone) {
            dynamoDbTable.setUpForTest();
            dynamoDbTable.waitUntilAvailable();
            conversionUpdateQueue.setUpForTest();
            conversionUpdateQueue.waitUntilAvailable();
            conversionInsertionQueue.setUpForTest();
            conversionInsertionQueue.waitUntilAvailable();
            isSetUpDone = true;
        }
    }

    @After
    public void tearDown() {
        if (++testedCount == TEST_COUNT) {
            dynamoDbTable.tearDownForTest();
            conversionUpdateQueue.tearDownForTestWithoutTestSeedReset();
            conversionInsertionQueue.tearDownForTestWithoutTestSeedReset();
        }
        removeCounversionUpdateRequest();
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionWhenGivenStatusUpdateType()
            throws Exception {
        // given
        insertStatusUpdateData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestConversion> actualConversions = testMapper
                .findAllConversionsBy(Arrays.asList(20L, 21L, 22L, 23L));
        assertNotNull(actualConversions);
        assertEquals(4, actualConversions.size());

        assertFields(actualConversions.get(0), 20L, APPROVED, of(2020, 2, 1));
        assertNotNull(actualConversions.get(0).getUpdater());
        assertFields(actualConversions.get(1), 21L, APPROVED, of(2020, 2, 1), UPDATER);
        assertFields(actualConversions.get(2), 22L, APPROVED, of(2020, 1, 31));
        assertNull(actualConversions.get(2).getUpdater());
        assertNull(actualConversions.get(2).getUpdatedDate());
        assertFields(actualConversions.get(3), 23L, APPROVED, of(2020, 2, 1), UPDATER);

        verifyRedshiftSyncRequiredIsSetToOne(20L);
        verifyRedshiftSyncRequiredIsSetToOne(21L);
        verifyRedshiftSyncRequiredIsSetToOne(23L);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionWhenGivenConversionImportType()
            throws Exception {
        // given
        registerSessionToDynamoDb();
        insertConversionImportData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<Long> conversionIds = testMapper.findCovnersionIds(CONVERSION_ID);
        List<IntegrationTestConversion> actualConversions = testMapper
                .findAllConversionsBy(conversionIds);
        assertNotNull(actualConversions);
        assertEquals(6, actualConversions.size());

        assertFields(actualConversions.get(0), conversionIds.get(0), 1234L, 301L,
                LocalDateTime.of(2020, 4, 1, 11, 22, 33),
                LocalDateTime.of(2020, 1, 9, 11, 22, 34),
                LocalDateTime.of(2020, 1, 9, 11, 22, 34), null,
                "2020-01-09$301$v20200411_001$2", 501L, 7, "v20200411_001", 2, EMPTY,
                PENDING, 1, new BigDecimal("110.00"), new BigDecimal("110.00"), CPA_FIXED,
                ZERO, ZERO, NET, ZERO, ZERO, 1, new BigDecimal("110.00"), 2, ANDROID_TAB,
                false, EMPTY, new BigDecimal("1.11"), CREATOR, NOT_NEEDED, "",
                IP_ADDRESS_1, "English", false, "2020-01-09 11:22:34-v20200411_001-2");
        assertFields(actualConversions.get(1), conversionIds.get(1), 5678L, 301L,
                LocalDateTime.of(2020, 4, 2, 22, 33, 44),
                LocalDateTime.of(2020, 1, 9, 11, 22, 35),
                LocalDateTime.of(2020, 1, 9, 11, 22, 35), of(2020, 2, 1),
                "2020-01-09$301$v20200411_002$30$testCustomerType", 502L, 8,
                "v20200411_002", 30, "p20200411_002", REJECTED, 1,
                new BigDecimal("221.11"), new BigDecimal("221.11"), CPA_FIXED, ZERO, ZERO,
                GROSS_AMOUNT_SOLD, ZERO, ZERO, 1, new BigDecimal("221.11"), 30, IPAD,
                false, "c20200411_002", new BigDecimal("1.11"), CREATOR, NOT_NEEDED,
                "testCustomerType", IP_ADDRESS_2, "Vietnamese", false,
                "2020-01-09 11:22:35-v20200411_002-30-testCustomerType-p20200411_002-0");
        assertFields(actualConversions.get(2), conversionIds.get(2), 5678L, 301L,
                LocalDateTime.of(2020, 4, 2, 22, 33, 44),
                LocalDateTime.of(2020, 1, 9, 11, 22, 35),
                LocalDateTime.of(2020, 1, 9, 11, 22, 35), of(2020, 2, 1),
                "2020-01-09$301$v20200411_002$30$testCustomerType", 502L, 8,
                "v20200411_002", 30, "p20200411_002", REJECTED, 1,
                new BigDecimal("221.11"), new BigDecimal("221.11"), CPA_FIXED, ZERO, ZERO,
                GROSS_AMOUNT_SOLD, ZERO, ZERO, 1, new BigDecimal("221.11"), 30, IPAD,
                false, "c20200411_002", new BigDecimal("1.11"), CREATOR, NOT_NEEDED,
                "testCustomerType", IP_ADDRESS_2, "Vietnamese", false,
                "2020-01-09 11:22:35-v20200411_002-30-testCustomerType-p20200411_002-1");
        assertFields(actualConversions.get(3), conversionIds.get(3), 9012L, 301L,
                LocalDateTime.of(2020, 4, 3, 1, 2, 3),
                LocalDateTime.of(2020, 4, 10, 1, 2, 3),
                LocalDateTime.of(2020, 4, 10, 1, 2, 3), of(2020, 2, 1),
                "2020-04-10$301$v20200411_003$3", 503L, 9, "v20200411_003", 3, "iphone",
                APPROVED, 1, new BigDecimal("332.22"), new BigDecimal("332.22"),
                CPA_FIXED, ZERO, ZERO, NET, ZERO, ZERO, 1, new BigDecimal("332.22"), 3,
                IPHONE, false, "", new BigDecimal("1.11"), CREATOR, NOT_NEEDED, "", "",
                "unknown", false, "2020-04-10 01:02:03-v20200411_003-3-iphone-0");
        assertFields(actualConversions.get(4), conversionIds.get(4), 9012L, 301L,
                LocalDateTime.of(2020, 4, 3, 1, 2, 3),
                LocalDateTime.of(2020, 4, 10, 1, 2, 3),
                LocalDateTime.of(2020, 4, 10, 1, 2, 3), of(2020, 2, 1),
                "2020-04-10$301$v20200411_003$3", 503L, 9, "v20200411_003", 3, "iphone",
                APPROVED, 1, new BigDecimal("332.22"), new BigDecimal("332.22"),
                CPA_FIXED, ZERO, ZERO, NET, ZERO, ZERO, 1, new BigDecimal("332.22"), 3,
                IPHONE, false, "", new BigDecimal("1.11"), CREATOR, NOT_NEEDED, "", "",
                "unknown", false, "2020-04-10 01:02:03-v20200411_003-3-iphone-1");
        assertFields(actualConversions.get(5), conversionIds.get(5), 9012L, 301L,
                LocalDateTime.of(2020, 4, 3, 1, 2, 3),
                LocalDateTime.of(2020, 4, 10, 1, 2, 3),
                LocalDateTime.of(2020, 4, 10, 1, 2, 3), of(2020, 2, 1),
                "2020-04-10$301$v20200411_003$3", 503L, 9, "v20200411_003", 3, "iphone",
                APPROVED, 1, new BigDecimal("332.22"), new BigDecimal("332.22"),
                CPA_FIXED, ZERO, ZERO, NET, ZERO, ZERO, 1, new BigDecimal("332.22"), 3,
                IPHONE, false, "", new BigDecimal("1.11"), CREATOR, NOT_NEEDED, "", "",
                "unknown", false, "2020-04-10 01:02:03-v20200411_003-3-iphone-2");

        List<IntegrationTestClickParameter> actualClickParameters =
                testMapper.findClickParametersBy(301L);
        assertNotNull(actualClickParameters);
        assertEquals(6, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "param1", "value1", CREATOR);
        assertFields(actualClickParameters.get(1), "param2", "value2", CREATOR);
        assertFields(actualClickParameters.get(2), "param3", "value3", CREATOR);
        assertFields(actualClickParameters.get(3), "param4", "value4", CREATOR);
        assertFields(actualClickParameters.get(4), "param5", "value5", CREATOR);
        assertFields(actualClickParameters.get(5), "param6", "value6", CREATOR);

        testMapper.deleteConversions(conversionIds);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionWhenGivenUpdateRewardEditDateByRewardSettingsType()
            throws Exception {
        // given
        insertUpdateRewardEditDateByRewardSettingsData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        int countConversionWithNullRewardEditDate = testMapper
                .countConversionsWithNullRewardEditDate(102,
                        LocalDateTime.of(2020, Month.JANUARY, 1, 0, 0, 0),
                        LocalDateTime.of(2020, Month.JANUARY, 31, 0, 0, 0));
        assertNotEquals(0, countConversionWithNullRewardEditDate);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionWhenGivenConversionUpdateByIdType()
            throws Exception {
        // given
        insertConversionUpdateByIdData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestConversion> actualConversions = testMapper
                .findAllConversionsBy(Arrays.asList(18L));
        assertNotNull(actualConversions);
        assertEquals(1, actualConversions.size());
        assertUpdateConversionByIdFields(actualConversions.get(0),
                new BigDecimal("1000.00"), new BigDecimal("200.00"), 10, "a01", null,
                null, 1L, new BigDecimal("1000.00"), new BigDecimal("1000.00"),
                new BigDecimal("1000.00"), new BigDecimal("1000.00"),
                new BigDecimal("166.70"), new BigDecimal("166.70"),
                new BigDecimal("166.70"), new BigDecimal("166.70"));
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionWhenGivenPaymentMassUpdateType()
            throws Exception {
        // given
        insertPaymentMassUpdateData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<String> invoices = Arrays
                .asList("invoice1", "invoice2", "invoice3", "invoice4", "invoice5",
                        "invoice6", "invoice7", "invoice8");
        int countPaidPayment = testMapper.countPaidPaymentBy(41, invoices);
        List<InvoiceAndPaidDate> actualInvoiceAndPaidDate = testMapper
                .getPaidDateOfGivenInvoices(invoices);
        LocalDate expectedPaidDate = LocalDate.of(2020, 1, 1);
        assertEquals(3, countPaidPayment);
        assertEquals(5, actualInvoiceAndPaidDate.size());
        assertPaidDate(expectedPaidDate, actualInvoiceAndPaidDate);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionWhenGivenUpdateConversionRankType()
            throws Exception {
        // given
        insertUpdateConversionRankData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestConversion> actualConversions = testMapper
                .findAllConversionsBy(Arrays.asList(19L));
        assertNotNull(actualConversions);
        assertEquals(1, actualConversions.size());

        assertUpdateConversionRankFields(actualConversions.get(0), 19L, 250L, 218L, 6,
                null, 2L, "staff1LoginName", PostbackStatus.NEEDED);
        List<IntegrationTestConversionRankUpdatedHistory> history = testMapper
                .findConversionRankUpdateHistoryBy(250L, 218L);
        assertEquals(1, history.size());
        assertConversionRankUpdatedHistoryFields(history.get(0), 250L, 218L, 5, 6,
                LocalDateTime.of(2020, 7, 31, 0, 0, 0),
                LocalDateTime.of(2020, 7, 31, 23, 59, 59), "staff1LoginName");
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldInsertConversionWhenGivenConversionImportWithoutClickIdType()
            throws Exception {
        // given
        insertConversionWithoutClickIdRegistrationData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<Long> conversionIds = testMapper.findCovnersionIds(CONVERSION_ID);
        List<IntegrationTestConversion> actualConversions = testMapper
                .findAllConversionsBy(conversionIds);
        assertNotNull(actualConversions);
        assertEquals(1, actualConversions.size());
        assertFields(actualConversions.get(0), 1L, 999L, 222L,
                LocalDateTime.of(2022, 6, 10, 22, 33, 44),
                LocalDateTime.of(2022, 6, 20, 1, 2, 3),
                LocalDateTime.of(2022, 6, 20, 1, 2, 3), LocalDate.of(2022, 6, 22),
                "2022-06-20$222$test identifier$30$test customer type", 601L, 5,
                "test identifier", 30, "test product Id", APPROVED, 1,
                new BigDecimal("1234.00"), new BigDecimal("1234.00"), CPA_FIXED, ZERO,
                ZERO, GROSS_FIXED_AMOUNT, ZERO, ZERO, 1, new BigDecimal("1234.00"), 30,
                DESKTOP, false, "test category Id", new BigDecimal("50.05"), CREATOR,
                NOT_NEEDED, "test customer type", EMPTY, EMPTY, true);
        assertEquals("2022-06-20 01:02:03-test identifier-30-test product Id",
                actualConversions.get(0).getTransactionId().substring(0,54));

        List<IntegrationTestClickParameter> actualClickParameters = testMapper
                .findClickParametersBy(222L);
        assertNotNull(actualClickParameters);
        assertEquals(2, actualClickParameters.size());
        assertFields(actualClickParameters.get(0), "utm_campaign", "utm_campaign",
                CREATOR);
        assertFields(actualClickParameters.get(1), "utm_source", "utm_source", CREATOR);

        deleteAllConversions(conversionIds);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionBonusWhenGivenUpdateConversionBonusImport()
            throws Exception {
        // given
        insertConversionBonusImportData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestConversionBonus> actualConversions = testMapper
                .findConversionsBonusDetailsBy(Arrays.asList(10L));
        assertNotNull(actualConversions);
        assertEquals(1, actualConversions.size());
        assertConversionBonusImportFields(actualConversions.get(0), 1L,
                new BigDecimal("200.00"), new BigDecimal("10.00"), new BigDecimal("5.00"),
                new BigDecimal("1.00"), new BigDecimal("40.00"), new BigDecimal("2.00"),
                new BigDecimal("1.00"), new BigDecimal("0.20"), UPDATER, UPDATER);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldInsertFixedBonusWhenGivenUpdateFixedBonusImport()
            throws Exception {
        // given
        insertFixedBonusImportData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestFixedBonus> actualConversions = testMapper
                .findFixedBonusBy(Arrays.asList(110L));
        assertNotNull(actualConversions);
        assertEquals(1, actualConversions.size());
        assertFixedBonusImportFields(actualConversions.get(0), 110L,
                new BigDecimal("200.00"), new BigDecimal("10.00"), new BigDecimal("5.00"),
                new BigDecimal("1.00"), new BigDecimal("40.00"), new BigDecimal("2.00"),
                new BigDecimal("1.00"), new BigDecimal("0.20"), 218L, 101L,
                BonusStatus.APPROVED, UPDATER, null);
        List<IntegrationTestFixedBonusDetails> actualDetails = testMapper
                .findFixedBonusDatailsBy(Arrays.asList(1L));
        assertNotNull(actualDetails);
        assertEquals(2, actualDetails.size());
        assertFixedBonusDetailsFields(actualDetails.get(0), 1L, 10L, UPDATER);
        assertFixedBonusDetailsFields(actualDetails.get(1), 1L, 20L, UPDATER);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateFixedBonusStatusWhenGivenUpdateFixedBonusStatusRequest()
            throws Exception {
        // given
        updateFixedBonusStatusData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestFixedBonus> actualConversions = testMapper
                .findFixedBonusBy(Arrays.asList(203L));
        assertNotNull(actualConversions);
        assertEquals(1, actualConversions.size());
        assertFixedBonusImportFields(actualConversions.get(0), 203L,
                new BigDecimal("1000.00"), new BigDecimal("8000.00"), new BigDecimal("6000.00"),
                new BigDecimal("4000.00"), new BigDecimal("5000.00"), new BigDecimal("4000.00"),
                new BigDecimal("3000.00"), new BigDecimal("2000.00"), 2000L, 101L,
                BonusStatus.REJECTED, "StaffTest", UPDATER);
    }

    @Test
    public void testAsyncStaffConversionUpdaterBatchShouldUpdateConversionWhenGivenPaymentTaxMassUpdateType()
            throws Exception {
        // given
        insertPaymentTaxMassUpdateData();

        // when
        recordReader.open();
        while (true) {
            ConversionUpdateMessageRecord inputRecord = recordReader.readRecord();
            if (inputRecord == null) {
                break;
            }
            ConversionUpdateRecord outputRecord = recordProcessor
                    .processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordWriter.close();

        // then
        List<IntegrationTestInvoicePaymentTax> actualConversions = testMapper
                .findInvoicePaymentTax(42L, "invoice1");
        assertNotNull(actualConversions);
        assertEquals(1, actualConversions.size());
        assertInvoicePaymentTaxFields(actualConversions.get(0), 0, 42L, "invoice1",
                LocalDateTime.of(2020, 1, 1, 0, 0), new BigDecimal("-150.00"),
                new BigDecimal("-100.00"), new BigDecimal("-30.00"), new BigDecimal("-20.00"),
                "staff1", LocalDateTime.of(2022, 3, 5, 0, 0), UPDATER, "note1");
    }

    private void assertPaidDate(LocalDate expectedLocalDate,
            List<InvoiceAndPaidDate> invoiceAndPaidDates) {
        invoiceAndPaidDates.forEach(invoiceAndPaidDate -> assertEquals(expectedLocalDate,
                invoiceAndPaidDate.getPaidDate()));
    }

    private void assertConversionRankUpdatedHistoryFields(
            IntegrationTestConversionRankUpdatedHistory actual, long expectedCampaignId,
            long expectedSiteId, int expectedOldRank, int expectednewRank,
            LocalDateTime expectedFrom, LocalDateTime expectedTo,
            String expectedUpdater) {
        assertEquals(expectedCampaignId, actual.getCampaignId());
        assertEquals(expectedSiteId,actual.getSiteId());
        assertEquals(expectedFrom, actual.getFrom());
        assertEquals(expectedTo, actual.getTo());
        assertEquals(expectedOldRank, actual.getOldRank());
        assertEquals(expectednewRank, actual.getNewRank());
        assertEquals(expectedUpdater, actual.getLoginName());
        assertNotNull(actual.getLatestUpdatedTime());
    }

    private void assertUpdateConversionRankFields(IntegrationTestConversion actual,
            long expectedConversionId, long expectedCampaignId, long expectedSiteId,
            int expectedRank, LocalDateTime expectedRewardEditDate, long expectedBannerId,
            String expectedUpdater, PostbackStatus expectedPostbackStatus) {
        assertEquals(expectedConversionId, actual.getConversionId().longValue());
        assertEquals(expectedCampaignId, actual.getCampaignId().longValue());
        assertEquals(expectedSiteId, actual.getSiteId().longValue());
        assertEquals(expectedRank, actual.getRank().intValue());
        assertEquals(expectedRewardEditDate, actual.getRewardEditDate());
        assertEquals(expectedBannerId, actual.getCreativeId().longValue());
        assertEquals(expectedUpdater, actual.getUpdater());
        assertEquals(expectedPostbackStatus, actual.getPostbackStatus());
        assertNotNull(actual.getUpdatedDate());
    }

    private void assertFields(IntegrationTestConversion actual,
            long expectedConversionId, ConversionStatus expectedStatus,
            LocalDate expectedConfirmationDate) {
        assertNotNull(actual);
        assertEquals(expectedConversionId, actual.getConversionId().longValue());
        assertEquals(expectedStatus, actual.getStatus());
        assertEquals(expectedConfirmationDate, actual.getConfirmationDate());
    }

    private void assertFields(IntegrationTestConversion actual,
            long expectedConversionId, ConversionStatus expectedStatus,
            LocalDate expectedConfirmationDate, String expectedUpdater) {
        assertFields(actual, expectedConversionId, expectedStatus,
                expectedConfirmationDate);
        assertEquals(expectedUpdater, actual.getUpdater());
        assertNotNull(actual.getUpdatedDate());
    }

    private void assertFields(IntegrationTestConversion actual, long expectedConversionId,
            long expectedCreativeId, long expectedCampaignId,
            LocalDateTime expectedClickTime, LocalDateTime expectedConversionTime,
            LocalDateTime expectedRegistrationTime,
            LocalDate expectedConfirmationDate,
            String expectedInternalTransactionId, long expectedSiteId, int expectedRank,
            String expectedVerify, int expectedResultId, String expectedProductId,
            ConversionStatus expectedStatus, int expectedProductQuantity,
            BigDecimal expectedProductUnitPrice, BigDecimal expectedTransactionAmount,
            RewardType expectedRewardType, BigDecimal expectedTransactionReward,
            BigDecimal expectedTransactionTotalReward,
            CommissionType expectedCommissionType, BigDecimal expectedAtCommission,
            BigDecimal expectedAgentCommission, int expectedDefaultProductQuantity,
            BigDecimal expectedDefaultProductUnitPrice, Integer expectedDefaultResultId,
            DeviceType expectedDeviceType, Boolean expectedPostbackIdDuplicativeFlag,
            String expectedCategoryId, BigDecimal expectedDiscount,
            String expectedCreator, PostbackStatus expectedPostbackStatus,
            String expectedCustomerType, String expectedClickIp, String expectedLanguage,
            Boolean expectedNewFlag) {
        assertNotNull(actual);
        assertEquals(expectedConversionId, actual.getConversionId().longValue());
        assertEquals(expectedCreativeId, actual.getCreativeId().longValue());
        assertEquals(expectedCampaignId, actual.getCampaignId().longValue());
        assertEquals(expectedClickTime, actual.getClickTime());
        assertEquals(expectedConversionTime, actual.getConversionTime());
        assertEquals(expectedRegistrationTime, actual.getRegistrationTime());
        assertEquals(expectedConfirmationDate, actual.getConfirmationDate());
        assertNotNull(actual.getTransactionId());
        assertEquals(expectedInternalTransactionId, actual.getInternalTransactionId());
        assertEquals(expectedSiteId, actual.getSiteId().intValue());
        assertEquals(expectedRank, actual.getRank().intValue());
        assertEquals(expectedVerify, actual.getVerify());
        assertEquals(expectedResultId, actual.getResultId().intValue());
        assertEquals(expectedProductId, actual.getProductId());
        assertEquals(expectedStatus, actual.getStatus());
        assertEquals(expectedProductQuantity, actual.getProductQuantity().intValue());
        assertEquals(expectedProductUnitPrice, actual.getProductUnitPrice());
        assertEquals(expectedTransactionAmount, actual.getTransactionAmount());
        assertEquals(expectedRewardType, actual.getRewardType());
        assertEquals(expectedTransactionReward, actual.getTransactionReward());
        assertEquals(expectedTransactionTotalReward, actual.getTransactionTotalReward());
        assertEquals(expectedCommissionType, actual.getCommissionType());
        assertEquals(expectedAtCommission, actual.getAtCommission());
        assertEquals(expectedAgentCommission, actual.getAgentCommission());
        assertEquals(expectedDefaultProductQuantity,
                actual.getDefaultProductQuantity().intValue());
        assertEquals(expectedDefaultProductUnitPrice,
                actual.getDefaultProductUnitPrice());
        assertEquals(expectedDefaultResultId, actual.getDefaultResultId());
        assertEquals(expectedDeviceType, actual.getDeviceType());
        assertEquals(expectedPostbackIdDuplicativeFlag, actual.getPostbackIdDuplicativeFlag());
        assertEquals(expectedCategoryId, actual.getCategoryId());
        assertEquals(expectedDiscount, actual.getDiscount());
        assertEquals(expectedCreator, actual.getCreator());
        assertNotNull(actual.getCreationTime());
        assertEquals(expectedPostbackStatus, actual.getPostbackStatus());
        assertEquals(expectedCustomerType, actual.getCustomerType());
        assertEquals(expectedClickIp, actual.getClickIp());
        assertEquals(expectedLanguage, actual.getLanguage());
        assertEquals(expectedNewFlag, actual.getNewFlag());
    }

    private void assertFields(IntegrationTestConversion actual, long expectedConversionId,
            long expectedCreativeId, long expectedCampaignId,
            LocalDateTime expectedClickTime, LocalDateTime expectedConversionTime,
            LocalDateTime expectedRegistrationTime,
            LocalDate expectedConfirmationDate,
            String expectedInternalTransactionId, long expectedSiteId, int expectedRank,
            String expectedVerify, int expectedResultId, String expectedProductId,
            ConversionStatus expectedStatus, int expectedProductQuantity,
            BigDecimal expectedProductUnitPrice, BigDecimal expectedTransactionAmount,
            RewardType expectedRewardType, BigDecimal expectedTransactionReward,
            BigDecimal expectedTransactionTotalReward,
            CommissionType expectedCommissionType, BigDecimal expectedAtCommission,
            BigDecimal expectedAgentCommission, int expectedDefaultProductQuantity,
            BigDecimal expectedDefaultProductUnitPrice, Integer expectedDefaultResultId,
            DeviceType expectedDeviceType, Boolean expectedPostbackIdDuplicativeFlag,
            String expectedCategoryId, BigDecimal expectedDiscount,
            String expectedCreator, PostbackStatus expectedPostbackStatus,
            String expectedCustomerType, String expectedClickIp, String expectedLanguage,
            Boolean expectedNewFlag, String expectedTransactionId) {
        assertNotNull(actual);
        assertEquals(expectedConversionId, actual.getConversionId().longValue());
        assertEquals(expectedCreativeId, actual.getCreativeId().longValue());
        assertEquals(expectedCampaignId, actual.getCampaignId().longValue());
        assertEquals(expectedClickTime, actual.getClickTime());
        assertEquals(expectedConversionTime, actual.getConversionTime());
        assertEquals(expectedRegistrationTime, actual.getRegistrationTime());
        assertEquals(expectedConfirmationDate, actual.getConfirmationDate());
        assertNotNull(actual.getTransactionId());
        assertEquals(expectedInternalTransactionId, actual.getInternalTransactionId());
        assertEquals(expectedSiteId, actual.getSiteId().intValue());
        assertEquals(expectedRank, actual.getRank().intValue());
        assertEquals(expectedVerify, actual.getVerify());
        assertEquals(expectedResultId, actual.getResultId().intValue());
        assertEquals(expectedProductId, actual.getProductId());
        assertEquals(expectedStatus, actual.getStatus());
        assertEquals(expectedProductQuantity, actual.getProductQuantity().intValue());
        assertEquals(expectedProductUnitPrice, actual.getProductUnitPrice());
        assertEquals(expectedTransactionAmount, actual.getTransactionAmount());
        assertEquals(expectedRewardType, actual.getRewardType());
        assertEquals(expectedTransactionReward, actual.getTransactionReward());
        assertEquals(expectedTransactionTotalReward, actual.getTransactionTotalReward());
        assertEquals(expectedCommissionType, actual.getCommissionType());
        assertEquals(expectedAtCommission, actual.getAtCommission());
        assertEquals(expectedAgentCommission, actual.getAgentCommission());
        assertEquals(expectedDefaultProductQuantity,
                actual.getDefaultProductQuantity().intValue());
        assertEquals(expectedDefaultProductUnitPrice,
                actual.getDefaultProductUnitPrice());
        assertEquals(expectedDefaultResultId, actual.getDefaultResultId());
        assertEquals(expectedDeviceType, actual.getDeviceType());
        assertEquals(expectedPostbackIdDuplicativeFlag, actual.getPostbackIdDuplicativeFlag());
        assertEquals(expectedCategoryId, actual.getCategoryId());
        assertEquals(expectedDiscount, actual.getDiscount());
        assertEquals(expectedCreator, actual.getCreator());
        assertNotNull(actual.getCreationTime());
        assertEquals(expectedPostbackStatus, actual.getPostbackStatus());
        assertEquals(expectedCustomerType, actual.getCustomerType());
        assertEquals(expectedClickIp, actual.getClickIp());
        assertEquals(expectedLanguage, actual.getLanguage());
        assertEquals(expectedNewFlag, actual.getNewFlag());
        assertEquals(expectedTransactionId, actual.getTransactionId());
    }

    private void assertFields(IntegrationTestClickParameter actual,
            String expectedParamName, String expectedParamValue, String expectedCreator) {
        assertNotNull(actual);
        assertNotNull(actual.getInternalTransactionId());
        assertEquals(expectedParamName, actual.getParamName());
        assertEquals(expectedParamValue, actual.getParamValue());
        assertEquals(expectedCreator, actual.getCreator());
        assertNotNull(actual.getCreationTime());
    }

    private void assertUpdateConversionByIdFields(IntegrationTestConversion actual,
            BigDecimal expectedTransactionAmount, BigDecimal expectedProductUnitPrice,
            Integer expectedResultId, String expectedProductId, String expectedCategoryId,
            String expectedCustomerType, Long expectedBonusSettingId,
            BigDecimal expectedPublisherBonus, BigDecimal expectedPublisherAgentBonus,
            BigDecimal expectedMerchantAgentBonus, BigDecimal expectedAtBonus,
            BigDecimal expectedPublisherBonusInUsd,
            BigDecimal expectedPublisherAgentBonusInUsd,
            BigDecimal expectedMerchantAgentBonusInUsd, BigDecimal expectedAtBonusInUsd) {
        assertEquals(expectedTransactionAmount, actual.getTransactionAmount());
        assertEquals(expectedProductUnitPrice, actual.getProductUnitPrice());
        assertEquals(expectedResultId, actual.getResultId());
        assertEquals(expectedProductId, actual.getProductId());
        assertEquals(expectedCategoryId, actual.getCategoryId());
        assertEquals(expectedCustomerType, actual.getCustomerType());
        assertEquals(expectedBonusSettingId, actual.getBonusSettingId());
        assertEquals(expectedPublisherBonus, actual.getPublisherBonus());
        assertEquals(expectedPublisherAgentBonus, actual.getPublisherAgentBonus());
        assertEquals(expectedMerchantAgentBonus, actual.getMerchantAgentBonus());
        assertEquals(expectedAtBonus, actual.getAtBonus());
        assertEquals(expectedPublisherBonusInUsd, actual.getPublisherBonusInUsd());
        assertEquals(expectedPublisherAgentBonusInUsd,
                actual.getPublisherAgentBonusInUsd());
        assertEquals(expectedMerchantAgentBonusInUsd,
                actual.getMerchantAgentBonusInUsd());
        assertEquals(expectedAtBonusInUsd, actual.getAtBonusInUsd());
    }

    private void assertConversionBonusImportFields(IntegrationTestConversionBonus actual,
            long bonusSettingId, BigDecimal publisherBonus,
            BigDecimal publisherAgentBonus, BigDecimal merchantAgentBonus,
            BigDecimal atBonus, BigDecimal publisherBonusInUsd,
            BigDecimal publisherAgentBonusInUsd, BigDecimal merchantAgentBonusInUsd,
            BigDecimal atBonusInUsd, String bonusCreatedBy, String updatedBy) {
        assertNotNull(actual);
        assertEquals(bonusSettingId, actual.getBonusSettingId().longValue());
        assertEquals(publisherBonus, actual.getPublisherBonus());
        assertEquals(publisherAgentBonus, actual.getPublisherAgentBonus());
        assertEquals(merchantAgentBonus, actual.getMerchantAgentBonus());
        assertEquals(atBonus, actual.getAtBonus());
        assertEquals(publisherBonusInUsd, actual.getPublisherBonusInUsd());
        assertEquals(publisherAgentBonusInUsd, actual.getPublisherAgentBonusInUsd());
        assertEquals(merchantAgentBonusInUsd, actual.getMerchantAgentBonusInUsd());
        assertEquals(atBonusInUsd, actual.getAtBonusInUsd());
        assertEquals(bonusCreatedBy, actual.getBonusCreatedBy());
        assertEquals(updatedBy, actual.getUpdatedBy());
    }

    private void assertFixedBonusImportFields(IntegrationTestFixedBonus actual,
            long bonusSettingId, BigDecimal publisherBonus,
            BigDecimal publisherAgentBonus, BigDecimal merchantAgentBonus,
            BigDecimal atBonus, BigDecimal publisherBonusInUsd,
            BigDecimal publisherAgentBonusInUsd, BigDecimal merchantAgentBonusInUsd,
            BigDecimal atBonusInUsd, long siteId, long campaignId, BonusStatus status,
            String createdBy, String updatedBy) {
        assertNotNull(actual);
        assertEquals(bonusSettingId, actual.getBonusSettingId().longValue());
        assertEquals(publisherBonus, actual.getPublisherBonus());
        assertEquals(publisherAgentBonus, actual.getPublisherAgentBonus());
        assertEquals(merchantAgentBonus, actual.getMerchantAgentBonus());
        assertEquals(atBonus, actual.getAtBonus());
        assertEquals(publisherBonusInUsd, actual.getPublisherBonusInUsd());
        assertEquals(publisherAgentBonusInUsd, actual.getPublisherAgentBonusInUsd());
        assertEquals(merchantAgentBonusInUsd, actual.getMerchantAgentBonusInUsd());
        assertEquals(atBonusInUsd, actual.getAtBonusInUsd());
        assertEquals(siteId, actual.getSiteId().longValue());
        assertEquals(campaignId, actual.getCampaignId().longValue());
        assertEquals(status, actual.getStatus());
        assertNotNull(actual.getConfirmedDate());
        assertEquals(createdBy, actual.getCreatedBy());
        assertEquals(updatedBy, actual.getUpdatedBy());
    }

    private void assertInvoicePaymentTaxFields(IntegrationTestInvoicePaymentTax actual,
            long id, long publisherId, String invoiceId, LocalDateTime requestDate,
            BigDecimal wht, BigDecimal vat, BigDecimal whtInUsd, BigDecimal vatInUsd,
            String creator, LocalDateTime creationDate, String updater, String note) {
        assertNotNull(actual);
        assertEquals(id, actual.getId().longValue());
        assertEquals(publisherId, actual.getPublisherId().longValue());
        assertEquals(invoiceId, actual.getInvoiceId());
        assertEquals(requestDate, actual.getRequestDate());
        assertEquals(wht, actual.getWht());
        assertEquals(vat, actual.getVat());
        assertEquals(whtInUsd, actual.getWhtInUsd());
        assertEquals(vatInUsd, actual.getVatInUsd());
        assertEquals(creator, actual.getCreator());
        assertEquals(creationDate, actual.getCreationDate());
        assertEquals(updater, actual.getUpdater());
        assertEquals(note, actual.getNote());
    }

    private void assertFixedBonusDetailsFields(IntegrationTestFixedBonusDetails actual,
            long bonusId, long conversionId, String createdBy) {
        assertNotNull(actual);
        assertEquals(bonusId, actual.getBonusId().longValue());
        assertEquals(conversionId, actual.getConversionId().longValue());
        assertEquals(createdBy, actual.getCreatedBy());
    }

    private void verifyRedshiftSyncRequiredIsSetToOne(long conversionId) {
        Integer redshiftSyncRequired = testMapper.getRedshiftSyncRequiredByConversionId(conversionId);
        assertEquals(Integer.valueOf(1), redshiftSyncRequired);
    }

    private void verifyRedshiftSyncRequiredIsSetToOneForAll(List<Long> conversionIds) {
        for (Long conversionId : conversionIds) {
            verifyRedshiftSyncRequiredIsSetToOne(conversionId);
        }
    }

    private void insertStatusUpdateData() {
        String message = "{\"type\" : \"STATUS_UPDATE\", "
                + "\"details\" : \"{\\\"campaignId\\\":101,"
                + "\\\"campaignName\\\":\\\"Vuivui.com\\\","
                + "\\\"status\\\":\\\"APPROVED\\\","
                + "\\\"confirmationDate\\\":\\\"2020-02-01T00:00:00\\\","
                + "\\\"conversionIds\\\":[null,20,21,22,23],"
                + "\\\"transactionIds\\\":[]}\", \"staffId\" : 11,"
                + "\"multiPartId\" : \"test_id\"}";
        String fileName = RANDOM_ID + "-status-update.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertConversionImportData() {
        String message = "{\"type\" : \"CONVERSION_IMPORT\", "
                + "\"details\" : \"{\\\"campaignId\\\": 301,"
                + "\\\"campaignName\\\": \\\"integration test campaign\\\","
                + "\\\"confirmationDate\\\": \\\"2020-02-01\\\","
                + "\\\"conversions\\\": ["
                + "{\\\"conversionTime\\\": \\\"2020-01-09T11:22:34+0700\\\","
                + "\\\"transactionId\\\": \\\"v20200411_001\\\","
                + "\\\"resultId\\\":2,"
                + "\\\"customerType\\\": \\\"\\\","
                + "\\\"productCategoryId\\\": \\\"\\\","
                + "\\\"productId\\\": \\\"\\\","
                + "\\\"productQuantity\\\": 1,"
                + "\\\"productUnitPrice\\\": 111.11,"
                + "\\\"discount\\\": 1.11,"
                + "\\\"status\\\": \\\"PENDING\\\","
                + "\\\"clickId\\\": \\\"testClickId1\\\"},"
                + "{\\\"conversionTime\\\": \\\"2020-01-09T11:22:35+0700\\\","
                + "\\\"transactionId\\\": \\\"v20200411_002\\\","
                + "\\\"resultId\\\":30,"
                + "\\\"customerType\\\": \\\"testCustomerType\\\","
                + "\\\"productCategoryId\\\": \\\"c20200411_002\\\","
                + "\\\"productId\\\": \\\"p20200411_002\\\","
                + "\\\"productQuantity\\\": 2,"
                + "\\\"productUnitPrice\\\": 222.22,"
                + "\\\"discount\\\": 2.22,"
                + "\\\"status\\\": \\\"REJECTED\\\","
                + "\\\"clickId\\\": \\\"testClickId2\\\"},"
                + "{\\\"conversionTime\\\": \\\"2020-04-10T01:02:03+0700\\\","
                + "\\\"transactionId\\\": \\\"v20200411_003\\\","
                + "\\\"resultId\\\":3,"
                + "\\\"customerType\\\": \\\"\\\","
                + "\\\"productCategoryId\\\": \\\"\\\","
                + "\\\"productId\\\": \\\"iphone\\\","
                + "\\\"productQuantity\\\": 3,"
                + "\\\"productUnitPrice\\\": 333.33,"
                + "\\\"discount\\\": 3.33,"
                + "\\\"status\\\": \\\"APPROVED\\\","
                + "\\\"clickId\\\": \\\"testClickId3\\\"}"
                + "]}\","
                + "\"staffId\": 13}";
        String fileName = RANDOM_ID + "-conversion-import.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertUpdateRewardEditDateByRewardSettingsData() {
        String message = "{'type' : 'UPDATE_REWARD_EDIT_DATE_BY_REWARD_SETTINGS',"
                + "'details' : \"{"
                + "'items':[{'campaignId':102,"
                + "'resultId':3,"
                + "'rank':5,"
                + "'productIds':[],"
                + "'categoryIds':[],"
                + "'from':'2020-01-01T00:00:00',"
                + "'to':'2020-01-31T23:59:59'}]}\","
                + "'staffId' : 11}";
        String fileName = RANDOM_ID + "-update-reward-edit-date-by-reward-settings.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertConversionUpdateByIdData() {
        String message = "{"
                + "    'type' : 'CONVERSION_UPDATE_BY_ID',"
                + "    'details' : \"{"
                + "         'items':["
                + "         {'conversionId':18,"
                + "         'transactionAmount':1000,"
                + "         'resultId':10,"
                + "         'productId':'a01',"
                + "         'customerType': null,"
                + "         'categoryId': null,"
                + "         'bonusSettingId':1,"
                + "         'publisherBonus': 1000,"
                + "         'publisherAgentBonus': 1000,"
                + "         'merchantAgentBonus': 1000,"
                + "         'atBonus': 1000}]"
                + "    }\","
                + "    'staffId' : 11"
                + "}";
        String fileName = RANDOM_ID + "-conversion-update-by-id.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertPaymentMassUpdateData() {
        String message = "{"
                + "    'type' : 'PAYMENT_MASS_UPDATE',"
                + "    'details' : \"{"
                + "     'invoiceNumbers':['invoice1','invoice2','invoice3'"
                + "        ,'invoice4','invoice5','invoice6','invoice7','invoice8'],"
                + "     'countryCode' : 'SG',"
                + "     'paidDate':'2020-01-01'}\","
                + "    'staffId' : 11"
                + "}";
        String fileName = RANDOM_ID + "-payment-mass-update.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertUpdateConversionRankData() {
        String message = "{"
                + "    'type' : 'UPDATE_CONVERSION_RANK',"
                + "    'details' : \"{"
                + "     'campaignId': '250',"
                + "     'siteId': '218',"
                + "     'rank': '6',"
                + "     'from': '2020-07-31T00:00:00',"
                + "     'to' : '2020-07-31T23:59:59'}\","
                + "    'staffId' : 11"
                + "}";
        String fileName = RANDOM_ID + "-update-conversion-rank.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertConversionWithoutClickIdRegistrationData() {
        String message = "{" + "    \"type\": \"CONVERSION_IMPORT_WITHOUT_CLICK_ID\","
                + "    \"details\": \"{" + "        \\\"data\\\":[" + "            {"
                + "                \\\"campaignId\\\": 222,"
                + "                \\\"creativeId\\\": 999,"
                + "                \\\"discount\\\": 50.05,"
                + "                \\\"newFlag\\\": true,"
                + "                \\\"conversionTime\\\": \\\"2022-06-20T01:02:03\\\","
                + "                \\\"categoryId\\\": \\\"test category Id\\\","
                + "                \\\"siteId\\\": 601,"
                + "                \\\"transactionAmount\\\": 1234,"
                + "                \\\"clickDate\\\": \\\"2022-06-10T22:33:44\\\","
                + "                \\\"identifier\\\": \\\"test identifier\\\","
                + "                \\\"quantity\\\": 1,"
                + "                \\\"resultId\\\": 30,"
                + "                \\\"productId\\\": \\\"test product Id\\\","
                + "                \\\"deviceType\\\": \\\"DESKTOP\\\","
                + "                \\\"customerType\\\": \\\"test customer type\\\","
                + "                \\\"conversionStatus\\\": \\\"APPROVED\\\","
                + "                \\\"clickParameters\\\":{"
                + "                    \\\"utm_source\\\": \\\"utm_source\\\","
                + "                    \\\"utm_campaign\\\": \\\"utm_campaign\\\""
                + "                },"
                + "                \\\"affiliationMonth\\\": \\\"2022-06\\\","
                + "                \\\"confirmationTime\\\": \\\"2022-06-22T11:22:33\\\""
                + "            }" + "        ]" + "    }\"," + "    \"staffId\": 14"
                + "}";
        String fileName = RANDOM_ID + "-conversion-import-without-click-id.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertConversionBonusImportData() {
        String message = "{"
                + "    'type':'CONVERSION_BONUS_IMPORT',"
                + "    'details':\"{"
                + "         'items':[{"
                + "         'bonusSettingId':1,"
                + "         'publisherBonus':200,"
                + "         'publisherAgentBonus':10,"
                + "         'merchantAgentBonus':5,"
                + "         'atBonus':1,"
                + "         'conversionId':10"
                + "         }]"
                + "    }\","
                + "    'staffId':11"
                + "}";
        String fileName = RANDOM_ID + "-conversion-bonus-update.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertFixedBonusImportData() {
        String message = "{"
                + "    'type':'FIXED_BONUS_IMPORT',"
                + "    'details':\"{"
                + "         'items':[{"
                + "         'bonusSettingId':110,"
                + "         'siteId':218,"
                + "         'campaignId':101,"
                + "         'publisherBonus':200,"
                + "         'publisherAgentBonus':10,"
                + "         'merchantAgentBonus':5,"
                + "         'atBonus':1,"
                + "         'confirmationDate':'2020-02-01T16:00:49',"
                + "         'status':'APPROVED',"
                + "         'conversionIds':[10,20]"
                + "         }]"
                + "    }\","
                + "    'staffId':11"
                + "}";
        String fileName = RANDOM_ID + "-fixed-bonus-import.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void updateFixedBonusStatusData() {
        String message = "{"
                + "    'type':'FIXED_BONUS_STATUS_UPDATE',"
                + "    'details':\"{"
                + "         'items':[{"
                + "         'bonusId':1003,"
                + "         'status':'REJECTED',"
                + "         'confirmationDate':'2020-02-01T17:00:49'"
                + "         }]"
                + "    }\","
                + "    'staffId':11"
                + "}";
        String fileName = RANDOM_ID + "-fixed-bonus-status-update.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void insertPaymentTaxMassUpdateData() {
        String message = "{"
                + "    'type' : 'PAYMENT_TAX_MASS_UPDATE',"
                + "    'details':\"{"
                + "         'items':[{"
                + "         'publisherId':42,"
                + "         'invoiceId':'invoice1',"
                + "         'wht':-150,"
                + "         'vat':-100,"
                + "         'note':'note1'"
                + "         }]"
                + "    }\","
                + "    'staffId' : 11"
                + "}";
        String fileName = RANDOM_ID + "-payment-tax-mass-update.json";
        s3Client.putObject(getConversionUpdateRequestsBucketName(), fileName, message);
        testMapper.insertCounversionUpdateRequest(fileName, ERROR_COUNT, CAMPAIGN_ID,
                STAFF_EMAIL, DATA_COUNT);
    }

    private void removeCounversionUpdateRequest() {
        testMapper.deleteCounversionUpdateRequest();
    }

    private void registerSessionToDynamoDb() {
        Map<String, AttributeValue> session1 = new ImmutableMap.Builder<String, AttributeValue>()
                .put(RK_COLUMN_NAME, new AttributeValue(RK_1))
                .put(ADDITIONAL_PARAMETERS_COLUMN_NAME,
                        new AttributeValue().withM(ADDITIONAL_PARAMETERS_1))
                .put(CLICK_DATE_COLUMN_NAME, new AttributeValue(CLICK_TIME_1))
                .put(DEVICE_TYPE_COLUMN_NAME, new AttributeValue(DEVICE_TYPE_1))
                .put(IP_ADDRESS_COLUMN_NAME, new AttributeValue(IP_ADDRESS_1))
                .put(LANGUAGE_COLUMN_NAME, new AttributeValue(LANGUAGE_1))
                .build();
        dynamoDbTable.update(UUID_1, session1);

        Map<String, AttributeValue> session2 = new ImmutableMap.Builder<String, AttributeValue>()
                .put(RK_COLUMN_NAME, new AttributeValue(RK_2))
                .put(ADDITIONAL_PARAMETERS_COLUMN_NAME,
                        new AttributeValue().withM(ADDITIONAL_PARAMETERS_2))
                .put(CLICK_DATE_COLUMN_NAME, new AttributeValue(CLICK_TIME_2))
                .put(DEVICE_TYPE_COLUMN_NAME, new AttributeValue(DEVICE_TYPE_2))
                .put(IP_ADDRESS_COLUMN_NAME, new AttributeValue(IP_ADDRESS_2))
                .put(LANGUAGE_COLUMN_NAME, new AttributeValue(LANGUAGE_2))
                .build();
        dynamoDbTable.update(UUID_2, session2);

        Map<String, AttributeValue> session3 = new ImmutableMap.Builder<String, AttributeValue>()
                .put(RK_COLUMN_NAME, new AttributeValue(RK_3))
                .put(ADDITIONAL_PARAMETERS_COLUMN_NAME,
                        new AttributeValue().withM(ADDITIONAL_PARAMETERS_3))
                .put(CLICK_DATE_COLUMN_NAME, new AttributeValue(CLICK_TIME_3))
                .put(DEVICE_TYPE_COLUMN_NAME, new AttributeValue(DEVICE_TYPE_3))
                .build();
        dynamoDbTable.update(UUID_3, session3);
    }

    private void deleteAllConversions(List<Long> conversionIds) {
        testMapper.deleteConversions(conversionIds);
        testMapper.deleteConversionParameters();
    }
}
