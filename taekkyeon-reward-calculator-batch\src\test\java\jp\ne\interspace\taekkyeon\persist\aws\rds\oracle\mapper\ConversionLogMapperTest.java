/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

import com.google.inject.Inject;

import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.PublisherAgencyCommissionPolicy;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.model.UpdateConversionRequest;

import static java.math.BigDecimal.valueOf;
import static java.time.ZoneOffset.UTC;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static jp.ne.interspace.taekkyeon.model.PublisherAgencyCommissionPolicy.DONT_PAY;
import static jp.ne.interspace.taekkyeon.model.PublisherAgencyCommissionPolicy.FULL_AT_COMMISSION;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.runners.MethodSorters.NAME_ASCENDING;

/**
 * Integration test for {@link ConversionLogMapper}.
 *
 * <AUTHOR> Shin
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
@FixMethodOrder(NAME_ASCENDING)
public class ConversionLogMapperTest {

    private static final int MAX_RECORD_COUNT = 2;

    private static final ZonedDateTime ZONED_LAST_UPDATE_TIME = ZonedDateTime
            .of(LocalDateTime.of(2018, 4, 24, 18, 45, 0), UTC);

    @Inject
    private ConversionLogMapper underTest;

    @Inject
    private ConversionLogIntegrationTestMapper integrationTestMapper;

    @Test
    public void testFindAllWithoutRewardEditDateShouldReturnAllCorrectDataWhenGivenEmptyCampaignIds() {
        // when
        List<Conversion> actual = underTest.findAllWithoutRewardEditDate(
                MAX_RECORD_COUNT, "SG", emptyList(), emptyList());

        // then
        assertNotNull(actual);
        assertEquals(2, actual.size());
        assertFields(actual.get(0), 1, 2, LocalDateTime.of(2017, 11, 21, 0, 0), 5, 3,
                DeviceType.UNKNOWN, "goodsId1", "categoryId1", RewardType.CPC,
                CommissionType.GROSS_FIXED_AMOUNT, valueOf(2), valueOf(50), valueOf(100),
                valueOf(200), valueOf(1), valueOf(2), valueOf(3), FULL_AT_COMMISSION,
                valueOf(0.00), valueOf(300));
        assertFields(actual.get(1), 2, 3, LocalDateTime.of(2017, 11, 22, 0, 0), 4, 30,
                DeviceType.DESKTOP, null, null, RewardType.CPA_FIXED,
                CommissionType.GROSS_AMOUNT_SOLD, valueOf(3), valueOf(60), valueOf(200),
                valueOf(300), valueOf(4), valueOf(5), valueOf(6), DONT_PAY,
                valueOf(0.00), valueOf(300));
    }

    @Test
    public void testFindAllWithoutRewardEditDateShouldReturnTargetDataWhenGivenSpecifiedCampaignIds() {
        // when
        List<Conversion> actual = underTest.findAllWithoutRewardEditDate(
                MAX_RECORD_COUNT, "SG", asList(2L), asList(3L));

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertFields(actual.get(0), 1, 2, LocalDateTime.of(2017, 11, 21, 0, 0), 5, 3,
                DeviceType.UNKNOWN, "goodsId1", "categoryId1", RewardType.CPC,
                CommissionType.GROSS_FIXED_AMOUNT, valueOf(2), valueOf(50), valueOf(100),
                valueOf(200), valueOf(1), valueOf(2), valueOf(3), FULL_AT_COMMISSION,
                valueOf(0.00), valueOf(300));
    }

    @Test
    public void testUpdateRewardsByShouldReturnOneAndSetRedshiftSyncRequiredToOneWhenGivenCorrectData() {
        // given
        UpdateConversionRequest conversion =
                new UpdateConversionRequest(1L, RewardType.CPC,
                        CommissionType.GROSS_FIXED_AMOUNT, valueOf(100), valueOf(200),
                        valueOf(1), valueOf(2), valueOf(3), ZONED_LAST_UPDATE_TIME,
                        valueOf(100), valueOf(100), valueOf(100), valueOf(100),
                        valueOf(100), BigDecimal.ZERO, valueOf(50));

        // when
        int actual = underTest.updateRewardsBy(conversion);

        // then
        assertEquals(1, actual);
        Integer redshiftSyncRequired = integrationTestMapper
                .getRedshiftSyncRequiredByConversionId(1L);
        assertEquals(Integer.valueOf(1), redshiftSyncRequired);
    }

    @Test
    public void testFindTotalRewardByShouldReturnCorrectTotalRewardWhenCategoryIdIsNull() {
        // given
        String internalTransactionId = "internalTransactionId";
        String categoryId = null;
        List<String> transactionIds = asList("transactionId2", "transactionId3");

        // when
        BigDecimal actual = underTest.findTotalRewardBy(internalTransactionId, categoryId,
                transactionIds);

        // then
        assertNotNull(actual);
        assertEquals(306, actual.doubleValue(), 0);
    }

    @Test
    public void testFindTotalRewardByShouldReturnCorrectTotalRewardWhenCategoryIdIsNotEmpty() {
        // given
        String internalTransactionId = "internalTransactionId";
        String categoryId = "categoryId1";
        List<String> transactionIds = asList("transactionId2", "transactionId3");

        // when
        BigDecimal actual = underTest.findTotalRewardBy(internalTransactionId, categoryId,
                transactionIds);

        // then
        assertNotNull(actual);
        assertEquals(306, actual.doubleValue(), 0);
    }

    private void assertFields(Conversion actual, long id, long campaignId,
            LocalDateTime conversionTime, int rank, int resultId, DeviceType deviceType,
            String productId, String categoryId, RewardType rewardType,
            CommissionType commissionType, BigDecimal count, BigDecimal price,
            BigDecimal reward, BigDecimal transactionAmountReward,
            BigDecimal atCommission, BigDecimal agentCommission,
            BigDecimal publisherAgentCommission,
            PublisherAgencyCommissionPolicy publisherAgencyPolicy, BigDecimal discount,
            BigDecimal transactionAmount) {
        assertNotNull(actual);
        assertEquals(id, actual.getId().longValue());
        assertEquals(campaignId, actual.getCampaignId().longValue());
        assertEquals(conversionTime, actual.getConversionTime());
        assertEquals(rank, actual.getRank().intValue());
        assertEquals(resultId, actual.getResultId().intValue());
        assertEquals(deviceType, actual.getDeviceType());
        assertEquals(productId, actual.getProductId());
        assertEquals(categoryId, actual.getCategoryId());
        assertEquals(rewardType, actual.getRewardType());
        assertEquals(commissionType, actual.getCommissionType());
        assertEquals(count.longValue(), actual.getCount().longValue());
        assertEquals(price.doubleValue(), actual.getPrice().doubleValue(), 0);
        assertEquals(reward.doubleValue(), actual.getReward().doubleValue(), 0);
        assertEquals(transactionAmountReward.doubleValue(),
                actual.getTransactionAmountReward().doubleValue(), 0);
        assertEquals(atCommission.doubleValue(),
                actual.getAtCommission().doubleValue(), 0);
        assertEquals(agentCommission.doubleValue(),
                actual.getAgentCommission().doubleValue(), 0);
        assertEquals(publisherAgentCommission.doubleValue(),
                actual.getPublisherAgentCommission().doubleValue(), 0);
        assertEquals(publisherAgencyPolicy, actual.getPublisherAgencyPolicy());
        assertEquals(discount.doubleValue(), actual.getDiscount().doubleValue(), 0);
        assertEquals(transactionAmount.doubleValue(),
                actual.getTransactionAmount().doubleValue(), 0);
    }
}
