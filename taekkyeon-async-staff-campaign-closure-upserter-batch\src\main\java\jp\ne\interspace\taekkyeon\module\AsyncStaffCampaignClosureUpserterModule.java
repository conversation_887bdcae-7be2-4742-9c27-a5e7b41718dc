/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import java.util.Map;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;

import org.easybatch.core.processor.RecordProcessor;
import org.easybatch.core.reader.RecordReader;
import org.easybatch.core.record.Record;
import org.easybatch.core.writer.RecordWriter;

import jp.ne.interspace.taekkyeon.batch.processor.AsyncStaffCampaignClosureUpserterProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.AsyncStaffCampaignClosureUpserterRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.AsyncStaffCampaignClosureUpserterRecordWriter;
import jp.ne.interspace.taekkyeon.model.CampaignClosureActionType;

import static jp.ne.interspace.taekkyeon.model.CampaignClosureActionType.CLOSE_CAMPAIGN;
import static jp.ne.interspace.taekkyeon.model.CampaignClosureActionType.CLOSE_MONTH;
import static jp.ne.interspace.taekkyeon.model.CampaignClosureActionType.FINISH_VALIDATION;

/**
 * Guice module for the campain closure supperter batch.
 *
 * <AUTHOR> Vuong
 */
public class AsyncStaffCampaignClosureUpserterModule extends AbstractModule {

    public static final String BIND_KEY_CAMPAIGN_CLOSURE_EMAIL_TEMPLATE_PATHS = "campaign.closure.email.template.path";
    public static final String BIND_KEY_CAMPAIGN_CLOSURE_EMAIL_SUBJECT = "campaign.closure.email.subject";

    private static final ImmutableMap<CampaignClosureActionType, String> CAMPAIGN_CLOSURE_EMAIL_TEMPLATE_PATHS = new ImmutableMap.Builder<CampaignClosureActionType, String>()
            .put(FINISH_VALIDATION, "finishValidationCampaignClosureEmail.ftl")
            .put(CLOSE_CAMPAIGN, "closeCampaignClosureEmail.ftl")
            .put(CLOSE_MONTH, "closeAllCampaignClosureEmail.ftl").build();

    private static final ImmutableMap<CampaignClosureActionType, String> CAMPAIGN_CLOSURE_EMAIL_SUBJECT = new ImmutableMap.Builder<CampaignClosureActionType, String>()
            .put(FINISH_VALIDATION,
                    "%s Campaign validation is finished and now pending for merchant payment")
            .put(CLOSE_CAMPAIGN, "%s Campaigns are closed")
            .put(CLOSE_MONTH, "%s is closed")
            .build();

    @Override
    protected void configure() {
        install(new SimpleQueueServiceQueueConsumerModule());

        bind(RecordReader.class).annotatedWith(MainRecordReaderBinding.class)
                .to(AsyncStaffCampaignClosureUpserterRecordReader.class);
        bind(new TypeLiteral<RecordProcessor<? extends Record<?>, ? extends Record<?>>>() {
        }).annotatedWith(MainRecordProcessorBinding.class)
                .to(AsyncStaffCampaignClosureUpserterProcessor.class);
        bind(RecordWriter.class).annotatedWith(MainRecordWriterBinding.class)
                .to(AsyncStaffCampaignClosureUpserterRecordWriter.class);
        bind(Integer.class).annotatedWith(CustomBatchSizeBinding.class).toInstance(1);
    }

    @Provides @Singleton @Named(BIND_KEY_CAMPAIGN_CLOSURE_EMAIL_TEMPLATE_PATHS)
    private Map<CampaignClosureActionType, String> provideCampaignClosureEmailTemplatePaths() {
        return CAMPAIGN_CLOSURE_EMAIL_TEMPLATE_PATHS;
    }

    @Provides @Singleton @Named(BIND_KEY_CAMPAIGN_CLOSURE_EMAIL_SUBJECT)
    private Map<CampaignClosureActionType, String> provideCampaignClosureEmailSubject() {
        return CAMPAIGN_CLOSURE_EMAIL_SUBJECT;
    }
}
