/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.updater;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import javax.inject.Singleton;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.model.CampaignClosurePeriod;
import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.model.ClickSession;
import jp.ne.interspace.taekkyeon.model.ConversionImportDetails;
import jp.ne.interspace.taekkyeon.model.ConversionImportErrorDetails;
import jp.ne.interspace.taekkyeon.model.ConversionImportResult;
import jp.ne.interspace.taekkyeon.model.ConversionInsertRequest;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.ConversionUpdateErrorDetails;
import jp.ne.interspace.taekkyeon.model.ConversionUpdateRequest;
import jp.ne.interspace.taekkyeon.model.RewardSettings;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignClosureMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CountryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.MonthlyClosureMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.StaffAccountMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.CampaignSettingsMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.MerchantAccountMapper;
import jp.ne.interspace.taekkyeon.service.AffiliationService;
import jp.ne.interspace.taekkyeon.service.ClickSessionService;
import jp.ne.interspace.taekkyeon.service.CommonConversionService;
import jp.ne.interspace.taekkyeon.service.ConversionService;
import jp.ne.interspace.taekkyeon.service.RewardService;
import jp.ne.interspace.taekkyeon.util.DateUtils;
import jp.ne.interspace.taekkyeon.validator.CommonValidator;
import jp.ne.interspace.taekkyeon.validator.ConversionRegisterValidator;

import static java.math.BigDecimal.ROUND_HALF_UP;
import static java.math.BigDecimal.ZERO;
import static java.time.LocalDateTime.MIN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.HYPHEN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.UNDERSCORE;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.PENDING;

/**
 * Register for conversion.
 *
 * <AUTHOR>
 */
@Singleton @Slf4j
public class ConversionRegister
        implements ConversionUpdater<ConversionImportDetails, ConversionImportResult> {

    private static final int CONFIRMATION_DATE_ERROR_COUNT = 1;
    private static final long DEFAULT_QUANTITY = 1;
    private static final int UNIT_QUANTITY = 1;

    private static final int TRANSACTION_AMOUNT_MULTIPLY_SCALE = 2;

    private static final String ERROR_MESSAGE_INSERT_FAILED = "Insert failed";

    private static final String UNKNOWN_VALUE = "unknown";
    private static final String NULL_STRING = "NULL";

    @Inject
    private CampaignSettingsMapper campaignSettingsMapper;

    @Inject
    private ConversionLogMapper conversionLogMapper;

    @Inject
    private CountryMapper countryMapper;

    @Inject
    private MerchantAccountMapper merchantAccountMapper;

    @Inject
    private MonthlyClosureMapper monthlyClosureMapper;

    @Inject
    private StaffAccountMapper staffAccountMapper;

    @Inject
    private CampaignClosureMapper campaignClosureMapper;

    @Inject
    private AffiliationService affiliationService;

    @Inject
    private CommonConversionService commonConversionService;

    @Inject
    private ConversionService conversionService;

    @Inject
    private ClickSessionService clickSessionService;

    @Inject
    private RewardService rewardService;

    @Inject
    private CommonValidator commonValidator;

    @Inject
    private ConversionRegisterValidator conversionRegisterValidator;

    @Inject
    private DateUtils dateUtils;

    @Override
    public ConversionImportResult update(ConversionImportDetails conversionImportDetails,
            long staffId) {

        String staffUid = staffAccountMapper.findStaffUidBy(staffId);

        String merchantCountryCode = merchantAccountMapper.findCountryCodeBy(
                conversionImportDetails.getCampaignId());
        LocalDate closedDate = monthlyClosureMapper.findClosedDateBy(
                merchantCountryCode);
        String zoneId = countryMapper.findZoneIdBy(merchantCountryCode);

        List<CampaignClosurePeriod> campaignClosurePeriods = campaignClosureMapper
                .findValidatedCampaignClosurePeriods(
                        Sets.newHashSet(conversionImportDetails.getCampaignId()),
                        closedDate);
        String errorMessage = commonValidator.validateDate(
                conversionImportDetails.getConfirmationDate().atStartOfDay(), closedDate,
                campaignClosurePeriods);
        if (errorMessage.isEmpty()) {
            return insertConversion(conversionImportDetails, closedDate,
                    staffUid, zoneId, campaignClosurePeriods);
        } else {
            ConversionUpdateErrorDetails errorDetails = new ConversionImportErrorDetails(MIN,
                    EMPTY, 0, EMPTY, EMPTY, EMPTY, 0, ZERO, ZERO, PENDING, EMPTY,
                    errorMessage);
            return new ConversionImportResult(0, CONFIRMATION_DATE_ERROR_COUNT,
                    Arrays.asList(errorDetails), Collections.emptyList(),
                    conversionImportDetails.getConversions().size());
        }
    }

    @Override
    public ConversionImportResult mergeResult(
            List<ConversionImportResult> conversionResults) {
        int successfulConversionCount = 0;
        int failedConversionCount = 0;
        List<ConversionUpdateErrorDetails> errorDetails = new LinkedList<>();
        List<ConversionUpdateRequest> updateRequest = new LinkedList<>();
        int totalConversionCount = 0;

        for (ConversionImportResult conversionResult : conversionResults) {
            successfulConversionCount += conversionResult
                    .getSuccessfulConversionCount();
            failedConversionCount += conversionResult.getFailedConversionCount();
            errorDetails.addAll(conversionResult.getErrorDetails());
            updateRequest.addAll(conversionResult.getUpdateRequest());
            totalConversionCount += conversionResult.getTotalConversionCount();
        }

        return new ConversionImportResult(successfulConversionCount,
                failedConversionCount, errorDetails, updateRequest, totalConversionCount);
    }

    @VisibleForTesting
    ConversionImportResult insertConversion(ConversionImportDetails conversionImportDetails,
            LocalDate closedDate, String staffUid, String zoneId,
            List<CampaignClosurePeriod> campaignClosurePeriods) {
        int successInsertCount = 0;
        List<ConversionUpdateErrorDetails> updateErrors = new LinkedList<>();
        List<ConversionUpdateRequest> insertRequests = new LinkedList<>();

        long campaignId = conversionImportDetails.getCampaignId();
        long clickFromCampaignId = conversionImportDetails.getClickFromCampaignId();
        long campaignIdForClickSession = clickFromCampaignId > 0
                ? clickFromCampaignId : campaignId;
        LocalDate confirmationDate = conversionImportDetails.getConfirmationDate();

        CampaignSettingDuplicationCutDetails duplicationCutDetails = campaignSettingsMapper
                .findDuplicationCutDetailsBy(campaignId);
        String merchantCountryCode = merchantAccountMapper.findCountryCodeBy(campaignId);
        ZoneId localZoneId = ZoneId.of(zoneId);

        for (ConversionRegistrationDetails conversion : conversionImportDetails
                .getConversions()) {
            ClickSession clickSession = clickSessionService.findClickSession(
                    campaignIdForClickSession, conversion.getClickId());
            Integer rank = affiliationService.findRank(campaignId,
                    clickSession.getSiteId(),
                    conversion.getConversionTime().toLocalDateTime());
            String errorMessage = conversionRegisterValidator.validateEligibleForInsert(
                    conversion, clickSession, confirmationDate, closedDate, rank,
                    campaignClosurePeriods, campaignId);

            if (errorMessage.isEmpty()) {
                List<ConversionInsertRequest> insertConversionRequest = createInsertRequest(
                        campaignId, confirmationDate, conversion, duplicationCutDetails,
                        clickSession, staffUid, localZoneId, rank);
                errorMessage = insertConversionWithClickParameters(insertConversionRequest,
                        clickSession.getAdditionalParameters());
                if (errorMessage.isEmpty()) {
                    successInsertCount++;
                    for (ConversionInsertRequest insertConversion : insertConversionRequest) {
                        insertRequests.add(commonConversionService
                                .createSqsRegistrationRequest(insertConversion,
                                        merchantCountryCode, localZoneId));
                    }
                }
            }
            if (!errorMessage.isEmpty()) {
                updateErrors.add(createImportErrorDetails(conversion, errorMessage));
            }
        }
        return new ConversionImportResult(successInsertCount, updateErrors.size(),
                updateErrors, insertRequests,
                conversionImportDetails.getConversions().size());
    }

    @VisibleForTesting
    List<ConversionInsertRequest> createInsertRequest(long campaignId, LocalDate confirmationDate,
            ConversionRegistrationDetails conversion,
            CampaignSettingDuplicationCutDetails duplicationCutDetails,
            ClickSession clickSession, String creator, ZoneId zoneId, Integer rank) {

        long quantity = conversion.getProductQuantity() > 0
                ? conversion.getProductQuantity() : DEFAULT_QUANTITY;
        BigDecimal unitDiscount = conversion.getDiscount()
                .divide(new BigDecimal(quantity), TRANSACTION_AMOUNT_MULTIPLY_SCALE,
                        ROUND_HALF_UP);

        RewardSettings rewardSettings = rewardService.findRewardSettings(conversion,
                campaignId, rank, clickSession.getDeviceType());

        BigDecimal afterDiscountedUnitPrice = getDiscountedUnitPrice(
                conversion.getProductUnitPrice(), unitDiscount);

        List<String> transactionIds = conversionService
                .generateTransactionIdsFrom(quantity,
                        conversion.getConversionTime().toLocalDateTime(),
                        conversion.getTransactionId(), conversion.getProductId(),
                        conversion.getResultId(), campaignId,
                        conversion.getCustomerType());
        List<ConversionInsertRequest> conversionInsertRequests = new LinkedList<>();
        for (String transactionId : transactionIds) {
            ConversionInsertRequest conversionInsertRequest = new ConversionInsertRequest(
                    0L, clickSession.getCreativeId(), campaignId,
                    createDateFrom(zoneId, clickSession.getClickTime()),
                    conversion.getConversionTime().toLocalDateTime(),
                    getConfirmationTimeFrom(confirmationDate, conversion.getStatus()),
                    transactionId, commonConversionService
                    .createInternalTransactionId(conversion, campaignId,
                            duplicationCutDetails), clickSession.getSiteId(), rank,
                    conversion.getTransactionId(), conversion.getResultId(),
                    conversion.getProductId(), conversion.getStatus(), UNIT_QUANTITY,
                    afterDiscountedUnitPrice, afterDiscountedUnitPrice,
                    rewardSettings.getRewardType(), rewardSettings.getCommissionType(),
                    clickSession.getDeviceType(), conversion.getProductCategoryId(),
                    unitDiscount, creator,
                    commonConversionService.getUtcZonedDateTimeWith(zoneId),
                    commonConversionService
                            .getPostbackStatusFrom(clickSession.getSiteId(),
                                    clickSession.getCreativeId()),
                    conversion.getCustomerType(), clickSession.getClickIp(),
                    getLanguageFrom(clickSession.getLanguage()), clickSession.getUuid(),
                    false);
            conversionInsertRequests.add(conversionInsertRequest);
        }

        return conversionInsertRequests;
    }

    @VisibleForTesting
    BigDecimal getDiscountedUnitPrice(BigDecimal unitPrice, BigDecimal unitDiscount) {
        BigDecimal discountedUnitPrice = unitPrice.subtract(unitDiscount)
                .setScale(TRANSACTION_AMOUNT_MULTIPLY_SCALE, ROUND_HALF_UP);
        return discountedUnitPrice.compareTo(ZERO) > 0 ? discountedUnitPrice : ZERO;
    }

    @VisibleForTesting
    String getLanguageFrom(String language) {
        if (!isNullOrEmptyOrNullString(language)) {
            String[] locals = language.replace(UNDERSCORE, HYPHEN).split(HYPHEN);
            return new Locale(locals[0]).getDisplayLanguage(Locale.ENGLISH);
        }
        return UNKNOWN_VALUE;
    }

    @VisibleForTesting
    boolean isNullOrEmptyOrNullString(String string) {
        return Strings.isNullOrEmpty(string) || NULL_STRING.equalsIgnoreCase(string);
    }

    @VisibleForTesting
    String getUuid() {
        return UUID.randomUUID().toString();
    }

    @VisibleForTesting
    String insertConversionWithClickParameters(List<ConversionInsertRequest> insertRequests,
            Map<String, String> additionalParameters) {
        String errorMessage = EMPTY;
        boolean isInsertedParameterAlready = false;
        try {
            for (ConversionInsertRequest insertRequest : insertRequests) {
                if (conversionLogMapper.insert(insertRequest) == 1) {
                    if (!isInsertedParameterAlready) {
                        commonConversionService
                                .insertClickParameters(additionalParameters,
                                        insertRequest.getCampaignId(),
                                        insertRequest.getInternalTransactionId(),
                                        insertRequest.getCreator());
                        isInsertedParameterAlready = true;
                    }
                } else {
                    errorMessage = ERROR_MESSAGE_INSERT_FAILED;
                }
            }
        } catch (Exception ex) {
            String causeError =
                    ex.getCause() == null ? EMPTY : ": " + ex.getCause().toString();
            getLogger()
                    .error(ERROR_MESSAGE_INSERT_FAILED + ":" + insertRequests.toString()
                            + ":" + additionalParameters + "\n:" + causeError);
            return ERROR_MESSAGE_INSERT_FAILED + causeError;
        }
        return errorMessage;
    }

    @VisibleForTesting
    ConversionImportErrorDetails createImportErrorDetails(
            ConversionRegistrationDetails conversion, String errorMessage) {
        return new ConversionImportErrorDetails(
                conversion.getConversionTime().toLocalDateTime(),
                conversion.getTransactionId(), conversion.getResultId(),
                conversion.getCustomerType(), conversion.getProductCategoryId(),
                conversion.getProductId(), conversion.getProductQuantity(),
                conversion.getProductUnitPrice(), conversion.getDiscount(),
                conversion.getStatus(), conversion.getClickId(), errorMessage);
    }

    @VisibleForTesting
    LocalDateTime getConfirmationTimeFrom(LocalDate confirmationDate,
            ConversionStatus status) {
        return status == PENDING ? null : confirmationDate.atTime(0, 0, 0);
    }

    @VisibleForTesting
    LocalDateTime createDateFrom(ZoneId zoneId, LocalDateTime dateTime) {
        return dateUtils.convertByTimeZone(dateTime, zoneId.getId()).toLocalDateTime();
    }

    @VisibleForTesting
    Logger getLogger() {
        return log;
    }
}
