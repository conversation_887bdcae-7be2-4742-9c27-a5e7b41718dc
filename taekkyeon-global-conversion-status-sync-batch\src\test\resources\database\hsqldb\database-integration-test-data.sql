
INSERT INTO MERCHANT_CAMPAIGN(CA<PERSON><PERSON><PERSON><PERSON>_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, <PERSON>MA<PERSON>_URL, DESCRIPTION, DESCRIPTION_EN, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, HIDE_CLICK_REFERRER)
VALUES(1, 1, 1, 'MERCHANT_CAMPAIGN_01', 'http://test1.ne.jp', 'http://test11.ne.jp', 'This is test', 'This is english test', 0, 0, 0, 0, 0, 0, 1, 0, TO_<PERSON>AT<PERSON>('2016/03/05', 'YYYY/MM/DD'), TO_<PERSON><PERSON><PERSON>('2016/07/03', 'YYYY/MM/DD'), 0, 1);
INSERT INTO MERCHANT_CAMPAIGN(CAMPAIGN_NO, ACCOUNT_NO, CAMPAIGN_STATE_ID, CAMPAIGN_NAME, URL, IMAGE_URL, DESCRIPTION, CATEGORY1, CATEGORY2, CATEGORY3, AUTO_AFF_LIMITATION_OPTION, AUTO_AFF_LIMITATION_DIVISION, GET_PARAMETER_FLAG, SELF_CONVERSION_FLAG, POINTBACK_PERMISSION, CAMPAIGN_START_DATE, CAMPAIGN_END_DATE, CAMPAIGN_TYPE, HIDE_CLICK_REFERRER, ORIGINAL_CAMPAIGN_ID, ORIGINAL_COUNTRY_CODE)
VALUES(2, 2, 2, 'MERCHANT_CAMPAIGN_02', 'http://test2.ne.jp', 'http://test22.ne.jp', 'This is campaign2', 0, 0, 0, 0, 0, 0, 0, 0, TO_DATE('2016/02/04', 'YYYY/MM/DD'), TO_DATE('2016/06/05', 'YYYY/MM/DD'), 1, 0, 1, 'ID');

INSERT INTO SALES_LOG (SEQ_NO, MERCHANT_CAMPAIGN_NO, PARTNER_SITE_NO, CONFIRMED_DATE, TRANSACTION_ID, SALES_LOG_STATUS, BANNER_ID, SALES_DATE, LOG_DATE, SALES_COUNT, PRICE, TOTAL_PRICE, REWARD_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UPDATED_BY, REDSHIFT_SYNC_REQUIRED)
VALUES (1, 1, 29310, TO_DATE('2020-08-03 00:00:00', 'YYYY-MM-DD HH24:MI:SS'), 'TRANSACTION_ID1', 1, 1, TO_DATE('2020/07/10', 'YYYY/MM/DD'), TO_DATE('2020/07/10', 'YYYY/MM/DD'), 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 'UPDATED_BY1', 0);
INSERT INTO SALES_LOG (SEQ_NO, MERCHANT_CAMPAIGN_NO, PARTNER_SITE_NO, CONFIRMED_DATE, TRANSACTION_ID, SALES_LOG_STATUS, BANNER_ID, SALES_DATE, LOG_DATE, SALES_COUNT, PRICE, TOTAL_PRICE, REWARD_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UPDATED_BY, REDSHIFT_SYNC_REQUIRED)
VALUES (2, 1, 29310, TO_DATE('2020-08-04 00:00:00', 'YYYY-MM-DD HH24:MI:SS'), 'TRANSACTION_ID2', 1, 1, TO_DATE('2020/07/10', 'YYYY/MM/DD'), TO_DATE('2020/07/10', 'YYYY/MM/DD'), 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 'UPDATED_BY2', 0);
INSERT INTO SALES_LOG (SEQ_NO, MERCHANT_CAMPAIGN_NO, PARTNER_SITE_NO, CONFIRMED_DATE, TRANSACTION_ID, SALES_LOG_STATUS, BANNER_ID, SALES_DATE, LOG_DATE, SALES_COUNT, PRICE, TOTAL_PRICE, REWARD_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UPDATED_BY, REDSHIFT_SYNC_REQUIRED)
VALUES (3, 2, 293101, TO_DATE('2020-08-06 00:00:00', 'YYYY-MM-DD HH24:MI:SS'), 'TRANSACTION_ID1', 0, 1, TO_DATE('2020/07/10', 'YYYY/MM/DD'), TO_DATE('2020/07/10', 'YYYY/MM/DD'), 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 'UPDATED_BY3', 0);
INSERT INTO SALES_LOG (SEQ_NO, MERCHANT_CAMPAIGN_NO, PARTNER_SITE_NO, CONFIRMED_DATE, TRANSACTION_ID, SALES_LOG_STATUS, BANNER_ID, SALES_DATE, LOG_DATE, SALES_COUNT, PRICE, TOTAL_PRICE, REWARD_TYPE, SALES_REWARD, TOTAL_PRICE_REWARD, COMMISSION_TYPE, AT_COMMISSION, AGENT_COMMISSION, DEFAULT_SALES_COUNT, DEFAULT_PRICE, UPDATED_BY, REDSHIFT_SYNC_REQUIRED)
VALUES (4, 2, 293101, TO_DATE('2020-08-05 00:00:00', 'YYYY-MM-DD HH24:MI:SS'), 'TRANSACTION_ID2', 0, 1, TO_DATE('2020/07/10', 'YYYY/MM/DD'), TO_DATE('2020/07/10', 'YYYY/MM/DD'), 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 'UPDATED_BY4', 0);


INSERT INTO MONTHLY_CLOSING (closed_month, target_month, temporary_closing_flag, created_by, created_on, updated_by, updated_on, immediate_closure_status, country_code)
VALUES ('202008', '202009', 1, NULL, NULL, NULL, NULL, 1, 'ID');
INSERT INTO MONTHLY_CLOSING (closed_month, target_month, temporary_closing_flag, created_by, created_on, updated_by, updated_on, immediate_closure_status, country_code)
VALUES ('202007', '202008', 0, NULL, NULL, NULL, NULL, 0, 'SG');

INSERT INTO SYNCHRONIZATION_DATA (TABLE_NAME, SYNC_START_TIME, DATABASE_NAME, CONVERSION_ID, COUNTRY_CODE, SITE_ID, CLOSED_MONTH)
VALUES ('global_conversion_status', TO_DATE('2020-08-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'), 'ORACLE', 0, 'ID' , 29310, TO_DATE('2020-08-01', 'YYYY-MM-DD'));

INSERT INTO COUNTRY (CODE, ZONE_ID)
VALUES ('ID', 'Asia/Jakarta');
INSERT INTO COUNTRY (CODE, ZONE_ID)
VALUES ('MY', 'Asia/Kuala_Lumpur');
INSERT INTO COUNTRY (CODE, ZONE_ID)
VALUES ('SG', 'Asia/Singapore');
