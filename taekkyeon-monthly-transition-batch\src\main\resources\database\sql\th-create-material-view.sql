DECLARE
    mv_sql VARCHAR2(1000);
    index_sql VARCHAR2(1000);
BEGIN
    BEGIN
        mv_sql := '
            CREATE MATERIALIZED VIEW MV_CREATIVE_ACCESS_LOG_SUMMARY_${nextMonth}
            PARALLEL ${parallelCount}
            BUILD IMMEDIATE
            REFRESH COMPLETE ON DEMAND AS
            SELECT *
            FROM
                BANNER_ACCESS_LOG_SUMMARY
            WHERE
                LOG_DATE >= TO_DATE(''${nextMonth}'', ''YYYYMM'')
            AND
                LOG_DATE < ADD_MONTHS(TO_DATE(''${nextMonth}'', ''YYYYMM''), 1)';
        index_sql := '
            CREATE INDEX IDX_MCALS${nextMonth}_01 ON MV_CREATIVE_ACCESS_LOG_SUMMARY_${nextMonth} (MERCHANT_CAMPAIGN_NO, PARTNER_SITE_NO, BANNER_ID, IMPRESSION_COUNT, C<PERSON><PERSON><PERSON>_COUNT, CL<PERSON><PERSON>_REWARD, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, LOG_DATE)';
        EXECUTE IMMEDIATE mv_sql;
        EXECUTE IMMEDIATE index_sql;
    EXCEPTION
        WHEN OTHERS THEN
            IF SQLCODE != -12006 THEN
                RAISE;
            END IF;
    END;
    BEGIN
        mv_sql := '
            CREATE MATERIALIZED VIEW "MV_CONVERSION_OCCURRED_${nextMonth}"
            PARALLEL ${parallelCount}
            BUILD IMMEDIATE
            REFRESH COMPLETE ON DEMAND AS
            SELECT *
            FROM
                SALES_LOG
            WHERE
                SALES_DATE >= TO_DATE(''${nextMonth}'', ''YYYYMM'')
            AND
                SALES_DATE < ADD_MONTHS(TO_DATE(''${nextMonth}'', ''YYYYMM''), 1)';
        EXECUTE IMMEDIATE mv_sql;
        index_sql := '
            CREATE INDEX IDX_MCO${nextMonth}_01 ON MV_CONVERSION_OCCURRED_${nextMonth} (SEQ_NO, SALES_DATE)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCO${nextMonth}_02 ON MV_CONVERSION_OCCURRED_${nextMonth} (SALES_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, SALES_LOG_STATUS, CONFIRMED_DATE, BANNER_ID, DEVICE_TYPE, RANK, INTERNAL_TRANSACTION_ID, RESULT_ID, SALES_COUNT, TOTAL_PRICE, SALES_REWARD, TOTAL_PRICE_REWARD, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, PUBLISHER_REWARD_IN_USD, TRANSACTION_AMOUNT_IN_USD, UNIT_PRICE_IN_USD, PUBLISHER_BONUS, PUBLISHER_AGENT_BONUS, MERCHANT_AGENT_BONUS, AT_BONUS)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCO${nextMonth}_03 ON MV_CONVERSION_OCCURRED_${nextMonth} (SALES_DATE, MERCHANT_CAMPAIGN_NO, SALES_LOG_STATUS, CONFIRMED_DATE)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCO${nextMonth}_04 ON MV_CONVERSION_OCCURRED_${nextMonth} (SALES_DATE, RESULT_ID, GOODS_ID)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCO${nextMonth}_05 ON MV_CONVERSION_OCCURRED_${nextMonth} (SALES_DATE, VERIFY)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCO${nextMonth}_06 ON MV_CONVERSION_OCCURRED_${nextMonth} (VERIFY ASC, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, SALES_DATE)';
        EXECUTE IMMEDIATE index_sql;
    EXCEPTION
        WHEN OTHERS THEN
            IF SQLCODE != -12006 THEN
                RAISE;
            END IF;
    END;
    BEGIN
        mv_sql := '
            CREATE MATERIALIZED VIEW "MV_CONVERSION_CONFIRMED_${nextMonth}"
            PARALLEL ${parallelCount}
            BUILD IMMEDIATE
            REFRESH COMPLETE ON DEMAND AS
            SELECT *
            FROM
                SALES_LOG
            WHERE
                CONFIRMED_DATE >= TO_DATE(''${nextMonth}'', ''YYYYMM'')
            AND
                CONFIRMED_DATE < ADD_MONTHS(TO_DATE(''${nextMonth}'', ''YYYYMM''), 1)';
        EXECUTE IMMEDIATE mv_sql;
        index_sql := '
            CREATE INDEX IDX_MCC${nextMonth}_01 ON MV_CONVERSION_CONFIRMED_${nextMonth} (SEQ_NO, CONFIRMED_DATE)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCC${nextMonth}_02 ON MV_CONVERSION_CONFIRMED_${nextMonth} (CONFIRMED_DATE, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, SALES_LOG_STATUS, BANNER_ID, DEVICE_TYPE, RANK, INTERNAL_TRANSACTION_ID, RESULT_ID, SALES_COUNT, TOTAL_PRICE, SALES_REWARD, TOTAL_PRICE_REWARD, AT_COMMISSION, AGENT_COMMISSION, P_AGENT_COMMISSION, PUBLISHER_REWARD_IN_USD, TRANSACTION_AMOUNT_IN_USD, UNIT_PRICE_IN_USD, PUBLISHER_BONUS, PUBLISHER_AGENT_BONUS, MERCHANT_AGENT_BONUS, AT_BONUS, SALES_DATE)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCC${nextMonth}_03 ON MV_CONVERSION_CONFIRMED_${nextMonth} (CONFIRMED_DATE, MERCHANT_CAMPAIGN_NO, SALES_LOG_STATUS)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCC${nextMonth}_04 ON MV_CONVERSION_CONFIRMED_${nextMonth} (CONFIRMED_DATE, RESULT_ID, GOODS_ID)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCC${nextMonth}_05 ON MV_CONVERSION_CONFIRMED_${nextMonth} (CONFIRMED_DATE, VERIFY)';
        EXECUTE IMMEDIATE index_sql;
        index_sql := '
            CREATE INDEX IDX_MCO${nextMonth}_06 ON MV_CONVERSION_CONFIRMED_${nextMonth} (VERIFY ASC, PARTNER_SITE_NO, MERCHANT_CAMPAIGN_NO, SALES_DATE)';
        EXECUTE IMMEDIATE index_sql;
    EXCEPTION
        WHEN OTHERS THEN
            IF SQLCODE != -12006 THEN
                RAISE;
            END IF;
    END;
END;
/