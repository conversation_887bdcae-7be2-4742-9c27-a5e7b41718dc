/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch;

import java.time.LocalDateTime;
import java.util.List;

import com.google.inject.Inject;
import org.easybatch.core.record.Batch;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.batch.processor.GlobalConversionStatusSyncRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.GlobalConversionStatusSyncRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.GlobalConversionStatusSyncRecordWriter;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonGlobalHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonIntegrationTestHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSyncInputRecord;
import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSyncOutputRecord;
import jp.ne.interspace.taekkyeon.module.CampaignClosureGlobalConversionStatusSyncQueue;
import jp.ne.interspace.taekkyeon.module.GlobalConversionStatusSyncJunitModule;
import jp.ne.interspace.taekkyeon.module.TaekkyeonSyncPropertiesModule;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.MyBatisMapperRepository;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.TestConversionMapper;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.GLOBAL_COUNTRY;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.APPROVED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for global conversion status sync batch.
 *
 * <AUTHOR> Mayur
 */
@RunWith(TaekkyeonIntegrationTestHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        GlobalConversionStatusSyncJunitModule.class,
        TaekkyeonGlobalHsqldbOracleJunitModule.class,
        TaekkyeonSyncPropertiesModule.class })
public class GlobalConversionStatusSyncIntegrationTest {

    @Inject
    private GlobalConversionStatusSyncRecordReader recordReader;

    @Inject
    private GlobalConversionStatusSyncRecordProcessor recordProcessor;

    @Inject
    private GlobalConversionStatusSyncRecordWriter recordWriter;

    @Inject
    private MyBatisMapperRepository mapperRepository;

    @Inject
    private CampaignClosureGlobalConversionStatusSyncQueue globalConversionStatusSyncQueue;

    @Before
    public void setup() {
        globalConversionStatusSyncQueue.setUpForTest();
        globalConversionStatusSyncQueue.waitUntilAvailable();
    }

    @After
    public void tearDown() {
        globalConversionStatusSyncQueue.tearDownForTest();
    }

    @Test
    public void testGlobalConversionStatusSyncBatchShouldUpsertTheCorrectDataToGlobalDatabaseWhenExecuted()
            throws Exception {
        // when
        recordReader.open();
        recordWriter.open();
        GlobalConversionStatusSyncInputRecord inputRecord = null;
        GlobalConversionStatusSyncOutputRecord outputRecord = null;
        while ((inputRecord = recordReader.readRecord()) != null) {
            outputRecord = recordProcessor.processRecord(inputRecord);
            recordWriter.writeRecords(new Batch(outputRecord));
        }
        recordReader.close();
        recordWriter.close();

        // then
        TestConversionMapper conversionMapper = mapperRepository
                .getMapperOf(TestConversionMapper.class, GLOBAL_COUNTRY);
        List<Conversion> actual = conversionMapper.findAllSyncedConversions();
        assertEquals(2, actual.size());
        assertFields(actual.get(0), 3, LocalDateTime.of(2020, 8, 31, 23, 59, 59),
                APPROVED, "TRANSACTION_ID1", 2, "GLOBAL_CONVERSION_STATUS_SYNC_BATCH");
        assertFields(actual.get(1), 4, LocalDateTime.of(2020, 8, 31, 23, 59, 59),
                APPROVED, "TRANSACTION_ID2", 2, "GLOBAL_CONVERSION_STATUS_SYNC_BATCH");
        verifyRedshiftSyncRequiredIsSetToOne(conversionMapper, 3L);
        verifyRedshiftSyncRequiredIsSetToOne(conversionMapper, 4L);
    }

    private void assertFields(Conversion actual, long conversionId,
            LocalDateTime confirmationTime, ConversionStatus status, String transactionId,
            long campaignId, String updatedBy) {
        assertNotNull(actual);
        assertEquals(conversionId, actual.getConversionId().longValue());
        assertEquals(confirmationTime, actual.getConfirmationTime());
        assertEquals(status, actual.getStatus());
        assertEquals(transactionId, actual.getTransactionId());
        assertEquals(campaignId, actual.getCampaignId().longValue());
        assertEquals(updatedBy, actual.getUpdatedBy());
    }

    private void verifyRedshiftSyncRequiredIsSetToOne(
            TestConversionMapper conversionMapper, long conversionId) {
        Integer redshiftSyncRequired = conversionMapper
                .getRedshiftSyncRequiredByConversionId(conversionId);
        assertEquals(Integer.valueOf(1), redshiftSyncRequired);
    }
}
