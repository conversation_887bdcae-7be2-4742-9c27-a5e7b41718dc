/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;

import com.google.inject.Inject;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSynchronizationData;

import static java.time.LocalDateTime.of;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.APPROVED;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.REJECTED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link ConversionMapper}.
 *
 * <AUTHOR> Mayur
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonPropertiesJunitModule.class,
        TaekkyeonHsqldbOracleJunitModule.class })
public class ConversionMapperTest {

    private static final LocalDateTime SYNC_START_TIME = of(2020, 8, 1, 0, 0);
    private static final long CONVERSION_ID = 1;
    private static final long SITE_ID = 29310;
    private static final int MAX_RECORD_COUNT = 2;
    private static final YearMonth CLOSED_MONTH = YearMonth.of(2020, 8);
    private static final String COUNTRY_CODE = "ID";
    private static final long CAMPAIGN_ID = 1;
    private static final  GlobalConversionStatusSynchronizationData SYNCHRONIZATION_DATA =
            new GlobalConversionStatusSynchronizationData(SYNC_START_TIME,
                    CONVERSION_ID, SITE_ID, CLOSED_MONTH);

    @Inject
    private ConversionMapper underTest;

    @Inject
    private TestConversionMapper testConversionMapper;

    @Test
    public void testFindConversionsForSyncShouldReturnCorrectDataWhenFoundData() {
        // when
        List<Conversion> actual = underTest.findConversionsForSync(SYNCHRONIZATION_DATA,
                MAX_RECORD_COUNT);

        // then
        assertEquals(2, actual.size());
        assertFileds(actual.get(0), 1, of(2020, 8, 3, 0, 0),
                APPROVED, "TRANSACTION_ID1", 1, "UPDATED_BY1");
        assertFileds(actual.get(1), 2, of(2020, 8, 4, 0, 0),
                REJECTED, "TRANSACTION_ID2", 2, "UPDATED_BY2");
    }

    @Test
    public void testCountConversionsforSyncShouldReturnCorrectCountWhenCalled() {
        // when
        long actual = underTest.countConversionsforSync(SYNCHRONIZATION_DATA);

        // then
        assertEquals(5, actual);
    }

    @Test
    public void testUpdateShouldUpdateCorrectDataWhenCalled() {
        // given
        Conversion conversion = new Conversion(4L, of(2020, 8, 10, 0, 0),
                APPROVED, "TRANSACTION_ID4", 2L, "UPDATED_BY4", true);

        // when
        int actual = underTest.update(conversion, COUNTRY_CODE);

        // then
        assertEquals(1, actual);

        Integer redshiftSyncRequired = testConversionMapper.getRedshiftSyncRequiredByConversionId(4L);
        assertEquals(Integer.valueOf(1), redshiftSyncRequired);
    }

    @Test
    public void testFindConversionsWithCampaignIdForSyncShouldReturnCorrectDataWhenFoundData() {
        // when
        List<Conversion> actual = underTest.findConversionsWithCampaignIdForSync(
                SYNCHRONIZATION_DATA, MAX_RECORD_COUNT, CAMPAIGN_ID);

        // then
        assertEquals(1, actual.size());
        assertFileds(actual.get(0), 1, of(2020, 8, 3, 0, 0),
                APPROVED, "TRANSACTION_ID1", 1, "UPDATED_BY1");
    }

    @Test
    public void testCountConversionsWithCampaignIdForSyncShouldReturnCorrectCountWhenCalled() {
        // when
        long actual = underTest
                .countConversionsWithCampaignIdForSync(SYNCHRONIZATION_DATA, CAMPAIGN_ID);

        // then
        assertEquals(1, actual);
    }

    private void assertFileds(Conversion actual, long conversionId,
            LocalDateTime confirmationTime, ConversionStatus status, String transactionId,
            long campaignId, String updatedBy) {
        assertNotNull(actual);
        assertEquals(conversionId, actual.getConversionId().longValue());
        assertEquals(confirmationTime, actual.getConfirmationTime());
        assertEquals(status, actual.getStatus());
        assertEquals(transactionId, actual.getTransactionId());
        assertEquals(campaignId, actual.getCampaignId().longValue());
        assertEquals(updatedBy, actual.getUpdatedBy());
    }
}
