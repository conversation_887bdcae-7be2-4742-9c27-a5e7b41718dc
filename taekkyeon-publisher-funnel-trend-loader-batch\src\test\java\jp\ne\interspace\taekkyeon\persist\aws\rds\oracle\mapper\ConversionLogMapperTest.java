/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.google.inject.Inject;

import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.model.ApprovedRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.ConversionTrackingFunnelDetails;
import jp.ne.interspace.taekkyeon.model.OccurredRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.module.PublisherFunnelTrendLoaderJunitModule;

import static com.google.common.primitives.Longs.asList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for {@link ConversionLogMapper}.
 *
 * <AUTHOR> Pachpind
 */
@RunWith(TaekkyeonHsqldbJunitRunner.class)
@TaekkyeonModules({ TaekkyeonHsqldbOracleJunitModule.class,
        PublisherFunnelTrendLoaderJunitModule.class })
public class ConversionLogMapperTest {

    private static final LocalDate CONVERSION_DATE_1 = LocalDate.of(2017, 11, 21);
    private static final LocalDate APPROVED_DATE_1 = LocalDate.of(2017, 11, 22);

    @Inject
    private ConversionLogMapper underTest;

    @Test
    public void testFindOccurredRewardFunnelDetailsShouldReturnCorrectDataWhenCalled() {
        // given
        List<Long> siteIds = asList(1L, 3L);

        // when
        Map<Long, OccurredRewardFunnelDetails> actual = underTest
                .findOccurredRewardFunnelDetails(siteIds);

        // then
        assertEquals(2, actual.size());
        assertField(actual.get(1L), 1L, BigDecimal.valueOf(13.20), BigDecimal.valueOf(26.40),
                BigDecimal.valueOf(11.00), BigDecimal.valueOf(11.00), BigDecimal.valueOf(11.00));
        assertField(actual.get(3L), 3L, BigDecimal.valueOf(11.00), BigDecimal.valueOf(22.00),
                BigDecimal.valueOf(8.80), BigDecimal.valueOf(8.80), BigDecimal.valueOf(8.80));
    }

    @Test
    public void testFindApprovedRewardFunnelDetailsShouldReturnCorrectDataWhenCalled() {
        // given
        List<Long> siteIds = asList(1L, 3L);

        // when
        Map<Long, ApprovedRewardFunnelDetails> actual = underTest
                .findApprovedRewardFunnelDetails(siteIds);

        // then
        assertEquals(2, actual.size());
        assertField(actual.get(1L), 1L, BigDecimal.valueOf(12.10), BigDecimal.valueOf(24.20),
                BigDecimal.valueOf(9.90), BigDecimal.valueOf(9.90), BigDecimal.valueOf(9.90));
        assertField(actual.get(3L), 3L, BigDecimal.valueOf(5.50), BigDecimal.valueOf(11.00),
                BigDecimal.valueOf(4.40), BigDecimal.valueOf(4.40), BigDecimal.valueOf(4.40));
    }

    @Test
    public void testFindConversionTrackingFunnelDetailsShouldReturnCorrectDataWhenCalled() {
        // given
        List<Long> siteIds = asList(1L, 3L);

        // when
        Map<Long, ConversionTrackingFunnelDetails> actual = underTest
                .findConversionTrackingFunnelDetails(siteIds);

        // then
        assertEquals(2, actual.size());
        assertField(actual.get(1L), 1L, CONVERSION_DATE_1, APPROVED_DATE_1);
        assertField(actual.get(3L), 3L, LocalDate.of(2017, 11, 28), LocalDate.of(2023, 1, 17));
    }

    private void assertField(OccurredRewardFunnelDetails actual, long expectedSiteId,
            BigDecimal expectedOccurredSalesReward,
            BigDecimal expectedOccurredTransactionAmountReward,
            BigDecimal expectedOccurredAtCommission,
            BigDecimal expectedOccurredMerchantAgentCommission,
            BigDecimal expectedOccurredPublisherAgentCommission) {

        assertNotNull(actual);
        assertEquals(expectedSiteId, actual.getSiteId());
        assertEquals(0,
                expectedOccurredSalesReward.compareTo(actual.getOccurredSalesReward()));
        assertEquals(0, expectedOccurredTransactionAmountReward
                .compareTo(actual.getOccurredTransactionAmountReward()));
        assertEquals(0,
                expectedOccurredAtCommission.compareTo(actual.getOccurredAtCommission()));
        assertEquals(0, expectedOccurredMerchantAgentCommission
                .compareTo(actual.getOccurredMerchantAgentCommission()));
        assertEquals(0, expectedOccurredPublisherAgentCommission
                .compareTo(actual.getOccurredPublisherAgentCommission()));
    }

    private void assertField(ApprovedRewardFunnelDetails actual, long expectedSiteId,
            BigDecimal expectedApprovedSalesReward,
            BigDecimal expectedApprovedTransactionAmountReward,
            BigDecimal expectedApprovedAtCommission,
            BigDecimal expectedApprovedMerchantAgentCommission,
            BigDecimal expectedApprovedPublisherAgentCommission) {

        assertNotNull(actual);
        assertEquals(expectedSiteId, actual.getSiteId());
        assertEquals(0,
                expectedApprovedSalesReward.compareTo(actual.getApprovedSalesReward()));
        assertEquals(0, expectedApprovedTransactionAmountReward
                .compareTo(actual.getApprovedTransactionAmountReward()));
        assertEquals(0,
                expectedApprovedAtCommission.compareTo(actual.getApprovedAtCommission()));
        assertEquals(0, expectedApprovedMerchantAgentCommission
                .compareTo(actual.getApprovedMerchantAgentCommission()));
        assertEquals(0, expectedApprovedPublisherAgentCommission
                .compareTo(actual.getApprovedPublisherAgentCommission()));
    }

    private void assertField(ConversionTrackingFunnelDetails actual, long siteId,
            LocalDate conversionTime, LocalDate approvedTime) {
        assertNotNull(actual);
        assertEquals(siteId, actual.getSiteId());
        assertEquals(conversionTime, actual.getFirstConversionDate());
        assertEquals(approvedTime, actual.getFirstApprovedConversionDate());
    }

    @Test
    public void testFindApprovedRewardFunnelDetailsWithTimeRangeShouldReturnCorrectDataWhenCalled() {
        // given
        List<Long> siteIds = asList(1L, 3L);
        LocalDateTime targetTimeFrom = LocalDateTime.of(2023, 1, 15, 0, 0, 0);
        LocalDateTime targetTimeEnd = LocalDateTime.of(2023, 1, 18, 0, 0, 0);

        // when
        Map<Long, ApprovedRewardFunnelDetails> actual = underTest
                .findApprovedRewardFunnelDetailsWithTimeRange(siteIds, targetTimeFrom,
                        targetTimeEnd);

        // then
        assertEquals(2, actual.size());
        assertField(actual.get(1L), 1L, BigDecimal.valueOf(3.30), BigDecimal.valueOf(6.60),
                BigDecimal.valueOf(2.20), BigDecimal.valueOf(2.20), BigDecimal.valueOf(2.20));
        assertField(actual.get(3L), 3L, BigDecimal.valueOf(5.50), BigDecimal.valueOf(11.00),
                BigDecimal.valueOf(4.40), BigDecimal.valueOf(4.40), BigDecimal.valueOf(4.40));
    }

    @Test
    public void testFindOccurredRewardFunnelDetailsWithTimeRangeShouldReturnCorrectDataWhenCalled() {
        // given
        List<Long> siteIds = asList(1L, 3L);
        LocalDateTime targetTimeFrom = LocalDateTime.of(2023, 1, 15, 0, 0, 0);
        LocalDateTime targetTimeEnd = LocalDateTime.of(2023, 1, 18, 0, 0, 0);

        // when
        Map<Long, OccurredRewardFunnelDetails> actual = underTest
                .findOccurredRewardFunnelDetailsWithTimeRange(siteIds, targetTimeFrom,
                        targetTimeEnd);

        // then
        assertEquals(2, actual.size());
        assertField(actual.get(1L), 1L, BigDecimal.valueOf(3.30), BigDecimal.valueOf(6.60),
                BigDecimal.valueOf(2.20), BigDecimal.valueOf(2.20), BigDecimal.valueOf(2.20));
        assertField(actual.get(3L), 3L, BigDecimal.valueOf(9.90), BigDecimal.valueOf(19.80),
                BigDecimal.valueOf(7.70), BigDecimal.valueOf(7.70), BigDecimal.valueOf(7.70));
    }

    @Test
    public void testFindApprovedRewardFunnelDetailsWithTimeRangeShouldReturnEmptyWhenNoDataInTimeRange() {
        // given
        List<Long> siteIds = asList(1L, 3L);
        LocalDateTime targetTimeFrom = LocalDateTime.of(2023, 2, 1, 0, 0, 0);
        LocalDateTime targetTimeEnd = LocalDateTime.of(2023, 2, 28, 0, 0, 0);

        // when
        Map<Long, ApprovedRewardFunnelDetails> actual = underTest
                .findApprovedRewardFunnelDetailsWithTimeRange(siteIds, targetTimeFrom,
                        targetTimeEnd);

        // then
        assertEquals(0, actual.size());
    }

    @Test
    public void testFindOccurredRewardFunnelDetailsWithTimeRangeShouldReturnEmptyWhenNoDataInTimeRange() {
        // given
        List<Long> siteIds = asList(1L, 3L);
        LocalDateTime targetTimeFrom = LocalDateTime.of(2023, 2, 1, 0, 0, 0);
        LocalDateTime targetTimeEnd = LocalDateTime.of(2023, 2, 28, 0, 0, 0);

        // when
        Map<Long, OccurredRewardFunnelDetails> actual = underTest
                .findOccurredRewardFunnelDetailsWithTimeRange(siteIds, targetTimeFrom,
                        targetTimeEnd);

        // then
        assertEquals(0, actual.size());
    }
}
