/**
 * Copyright © 2024 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

import com.amazonaws.services.dynamodbv2.model.AttributeValue;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.common.CreativeHelper;
import jp.ne.interspace.taekkyeon.common.StringHelper;
import jp.ne.interspace.taekkyeon.exception.TaekkyeonException;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.Creative;
import jp.ne.interspace.taekkyeon.model.CreativeDetails;
import jp.ne.interspace.taekkyeon.model.CustomCreativeTrackingUrlRule;
import jp.ne.interspace.taekkyeon.model.SubId;
import jp.ne.interspace.taekkyeon.module.Country;
import jp.ne.interspace.taekkyeon.persist.aws.dynamodb.DynamoDbTable;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CreativeDataMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PublisherSiteMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.MerchantAccountMapper;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.CreativeType.TEXT;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link CreativeService}.
 *
 * <AUTHOR> Huynh
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class CreativeServiceTest {

    private static final String BASE_TRACKING_URL = "http://click.st-dsbd-id.asean-accesstrade.net/adv.php?rk=xxxxxxxxxxxx";
    private static final long SITE_ID = 1L;
    private static final long CREATIVE_ID = 1L;
    public static final long NOT_EXISTING_SITE_ID = 0L;
    private static final long DEFAULT_CREATIVE_ID_FOR_SUB_IDS = 0;
    private static final String COUNTRY_CODE = "ID";
    private static final String CUSTOM_CREATIVE_URL_PREFIX = "http://urlPrefix.com?url=";
    private static final String CUSTOM_CREATIVE_URL_PARAMETERS_WITH_QUERY_STRING_DELIMITER = "?parameter1=value1&parameter2=value2";
    private static final String CUSTOM_CREATIVE_LANDING_URL_WITH_QUERY_STRING = "customCreativeLandingUrl?parameter0=value0";
    private static final int CUSTOM_CREATIVE_URL_ENCODE_TIMES = 2;
    private static final String SUB_ID_NAME_1 = "subId1Name";
    private static final String SUB_ID_VALUE_1 = "subId1Value";
    private static final String SUB_ID_NAME_2 = "subId2Name";
    private static final String SUB_ID_VALUE_2 = "subId2Value";
    private static final String BASE_TRACKING_URL_WITH_RK_CODE = "http://click.st-dsbd-id.asean-accesstrade.net/adv.php?rk=000001000001";
    private static final String CUSTOM_CREATIVE_LANDING_URL_FOR_INSERTION = "customCreativeLandingUrl";
    private static final String CUSTOM_CREATIVE_URL_PARAMETERS = "parameter1=value1&parameter2=value2";
    private static final String RK = "000001000001";
    private static final List<SubId> SUB_IDS = Arrays.asList(
            new SubId(SUB_ID_NAME_1, SUB_ID_VALUE_1),
            new SubId(SUB_ID_NAME_2, SUB_ID_VALUE_2));

    private static final ImmutableMap<Country, String> BASE_SHORT_AFFILIATE_URL =
            new ImmutableMap.Builder<Country, String>()
                    .put(INDONESIA, BASE_TRACKING_URL).build();

    @InjectMocks @Spy
    private CreativeService underTest;

    @Mock
    private CreativeHelper creativeHelper;

    @Mock
    private StringHelper stringHelper;

    @Mock
    private CreativeDataMapper creativeMapper;

    @Mock
    private PublisherSiteMapper publisherSiteMapper;

    @Mock
    private MerchantAccountMapper merchantAccountMapper;

    @Mock
    private DynamoDbTable dynamoDbTable;

    @Test
    public void testCreateBaseTrackingUrlShouldReturnCorrectDataWhenCalled() {
        // given
        String trackingDomains = "trackingDomains";
        String expected = "trackingDomains/adv.php?rk=xxxxxxxxxxxx";

        // when
        String actual = underTest.createBaseTrackingUrl(trackingDomains);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateBaseTrackingUrlForShouldReturnCorrectDataWhenCalled() {
        // given
        ImmutableMap<String, String> baseLocalTrackingUrl =
                new ImmutableMap.Builder<String, String>()
                        .put(COUNTRY_CODE, BASE_TRACKING_URL).build();
        doReturn(baseLocalTrackingUrl).when(underTest).getBaseTrackingUrl();
        when(creativeMapper.findExternalSiteIdBy(SITE_ID, CREATIVE_ID))
                .thenReturn(NOT_EXISTING_SITE_ID);
        doReturn(BASE_TRACKING_URL).when(underTest)
                .createBaseTrackingUrl(BASE_TRACKING_URL);

        // when
        String actual = underTest.generateBaseTrackingUrlFor(
                SITE_ID, CREATIVE_ID, COUNTRY_CODE);

        // then
        assertEquals(BASE_TRACKING_URL, actual);
    }

    @Test
    public void testGenerateBaseTrackingUrlForShouldReturnCorrectDataWhenExternalSiteIdNotEqualZero() {
        // given
        String expected = "http://click.st-dsbd-id.asean-accesstrade.net/adv.php?rk=xxxxxxxxxxxx&esid=1";
        ImmutableMap<String, String> baseLocalTrackingUrl =
                new ImmutableMap.Builder<String, String>()
                        .put(COUNTRY_CODE, BASE_TRACKING_URL).build();
        doReturn(baseLocalTrackingUrl).when(underTest).getBaseTrackingUrl();
        when(creativeMapper.findExternalSiteIdBy(SITE_ID, CREATIVE_ID)).thenReturn(1L);
        doReturn(BASE_TRACKING_URL).when(underTest)
                .createBaseTrackingUrl(BASE_TRACKING_URL);

        // when
        String actual = underTest.generateBaseTrackingUrlFor(
                SITE_ID, CREATIVE_ID, COUNTRY_CODE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGetEncodedLandingUrlWithQueryParameterShouldReturnCorrectEncodedUrlWhenCalled()
            throws UnsupportedEncodingException {
        // given
        CustomCreativeTrackingUrlRule trackingUrlRule = new CustomCreativeTrackingUrlRule(
                CUSTOM_CREATIVE_URL_PREFIX,
                CUSTOM_CREATIVE_URL_PARAMETERS_WITH_QUERY_STRING_DELIMITER,
                CUSTOM_CREATIVE_URL_ENCODE_TIMES);
        String expectedEncodedUrl = "url=http%3A%2F%2FurlPrefix.com%3Furl%3Dcustom"
                + "CreativeLandingUrl%253Fparameter0%253Dvalue0";

        // when
        String actual = underTest.getEncodedLandingUrlWithQueryParameter(
                CUSTOM_CREATIVE_LANDING_URL_WITH_QUERY_STRING, trackingUrlRule);

        // then
        assertEquals(expectedEncodedUrl, actual);
    }

    @Test
    public void testGenerateTrackingUrlForShouldReturnTrackingUrlWithSubIdsWhenSubIdsAreAvailable() {
        // given
        String expected = BASE_TRACKING_URL_WITH_RK_CODE
                + "&subId1Name=subId1Value&subId2Name=subId2Value";
        when(creativeMapper.findSubIdBy(SITE_ID, CREATIVE_ID)).thenReturn(SUB_IDS);
        when(publisherSiteMapper.findCountryCodeBy(SITE_ID)).thenReturn(COUNTRY_CODE);
        when(creativeHelper.createRk(CREATIVE_ID, SITE_ID)).thenReturn(RK);
        doReturn(BASE_TRACKING_URL).when(underTest)
                .generateBaseTrackingUrlFor(SITE_ID, CREATIVE_ID, COUNTRY_CODE);

        // when
        String actual = underTest.generateTrackingUrlFor(
                CREATIVE_ID, SITE_ID, COUNTRY_CODE);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateTrackingUrlForShouldReturnTrackingUrlWithSubIdsWhenSubIdsIsEmpty() {
        // given
        when(creativeMapper.findSubIdBy(SITE_ID, DEFAULT_CREATIVE_ID_FOR_SUB_IDS))
                .thenReturn(Collections.emptyList());
        when(publisherSiteMapper.findCountryCodeBy(SITE_ID)).thenReturn(COUNTRY_CODE);
        when(creativeHelper.createRk(CREATIVE_ID, SITE_ID)).thenReturn(RK);
        doReturn(BASE_TRACKING_URL).when(underTest)
                .generateBaseTrackingUrlFor(SITE_ID, CREATIVE_ID, COUNTRY_CODE);

        // when
        String actual = underTest.generateTrackingUrlFor(
                CREATIVE_ID, SITE_ID, COUNTRY_CODE);

        // then
        assertEquals(BASE_TRACKING_URL_WITH_RK_CODE, actual);
    }

    @Test
    public void testGetCustomCreativeLandingUrlWithQueryParameterShouldReturnCorrectTrackingUrlWhenNoPredefinedParametersExistForTheCampaign() {
        // given
        CustomCreativeTrackingUrlRule trackingUrlRule = new CustomCreativeTrackingUrlRule(
                CUSTOM_CREATIVE_URL_PREFIX, "", CUSTOM_CREATIVE_URL_ENCODE_TIMES);

        // when
        String actual = underTest.getCustomCreativeLandingUrlWithQueryParameter(
                CUSTOM_CREATIVE_LANDING_URL_FOR_INSERTION, trackingUrlRule);

        // then
        assertEquals(CUSTOM_CREATIVE_LANDING_URL_FOR_INSERTION, actual);
    }

    @Test
    public void testGetCustomCreativeLandingUrlWithQueryParameterShouldReturnCorrectTrackingUrlWhenPredefinedParametersWithoutQueryStringDelimiterExistForTheCampaign() {
        // given
        CustomCreativeTrackingUrlRule trackingUrlRule = new CustomCreativeTrackingUrlRule(
                CUSTOM_CREATIVE_URL_PREFIX, CUSTOM_CREATIVE_URL_PARAMETERS,
                CUSTOM_CREATIVE_URL_ENCODE_TIMES);

        // when
        String actual = underTest.getCustomCreativeLandingUrlWithQueryParameter(
                CUSTOM_CREATIVE_LANDING_URL_FOR_INSERTION, trackingUrlRule);

        // then
        assertEquals(CUSTOM_CREATIVE_LANDING_URL_FOR_INSERTION + "?"
                + CUSTOM_CREATIVE_URL_PARAMETERS, actual);
    }

    @Test
    public void testGetCustomCreativeLandingUrlWithQueryParameterShouldReturnCorrectTrackingUrlWhenPredefinedParametersWithQueryStringDelimiterExistForTheCampaign() {
        // given
        CustomCreativeTrackingUrlRule trackingUrlRule = new CustomCreativeTrackingUrlRule(
                CUSTOM_CREATIVE_URL_PREFIX,
                CUSTOM_CREATIVE_URL_PARAMETERS_WITH_QUERY_STRING_DELIMITER,
                CUSTOM_CREATIVE_URL_ENCODE_TIMES);

        // when
        String actual = underTest.getCustomCreativeLandingUrlWithQueryParameter(
                CUSTOM_CREATIVE_LANDING_URL_FOR_INSERTION, trackingUrlRule);

        // then
        assertEquals(
                CUSTOM_CREATIVE_LANDING_URL_FOR_INSERTION
                        + CUSTOM_CREATIVE_URL_PARAMETERS_WITH_QUERY_STRING_DELIMITER,
                actual);
    }

    @Test
    public void testGetCustomCreativeLandingUrlWithQueryParameterShouldReturnCorrectTrackingUrlWhenPredefinedParametersWithQueryStringDelimiterExistForTheCampaignAndLandingUrlHasQueryString()
            throws UnsupportedEncodingException {
        // given

        CustomCreativeTrackingUrlRule trackingUrlRule = new CustomCreativeTrackingUrlRule(
                CUSTOM_CREATIVE_URL_PREFIX,
                CUSTOM_CREATIVE_URL_PARAMETERS_WITH_QUERY_STRING_DELIMITER,
                CUSTOM_CREATIVE_URL_ENCODE_TIMES);

        // when
        String actual = underTest.getCustomCreativeLandingUrlWithQueryParameter(
                CUSTOM_CREATIVE_LANDING_URL_WITH_QUERY_STRING, trackingUrlRule);

        // then
        assertEquals(CUSTOM_CREATIVE_LANDING_URL_WITH_QUERY_STRING + "&"
                + CUSTOM_CREATIVE_URL_PARAMETERS, actual);
    }

    @Test
    public void testGetOriginalUrlShouldReturnCorrectDataWhenCalled()
            throws UnsupportedEncodingException {
        // given
        long campaignId = 1L;
        String trackingUrl = "trackingUrl";
        String landingUrlQueryParameter = "landingUrlQueryParameter";
        String landingUrl = "landingUrl";
        String encodedLandingUrlQueryParameter = "encodedLandingUrlQueryParameter";
        CreativeDetails creativeDetails = mock(CreativeDetails.class);
        CustomCreativeTrackingUrlRule trackingUrlRule
                = mock(CustomCreativeTrackingUrlRule.class);
        when(creativeDetails.getSiteId()).thenReturn(SITE_ID);
        when(creativeDetails.getCampaignId()).thenReturn(campaignId);
        when(creativeDetails.getLandingUrl()).thenReturn(landingUrl);
        doReturn(trackingUrl).when(underTest)
                .generateTrackingUrlFor(SITE_ID, CREATIVE_ID, COUNTRY_CODE);
        when(creativeMapper.findTrackingUrlRuleBy(campaignId))
                .thenReturn(trackingUrlRule);
        doReturn(landingUrlQueryParameter).when(underTest)
                .getCustomCreativeLandingUrlWithQueryParameter(
                        landingUrl, trackingUrlRule);
        doReturn(encodedLandingUrlQueryParameter).when(underTest)
                .getEncodedLandingUrlWithQueryParameter(
                        landingUrlQueryParameter, trackingUrlRule);

        // when
        String actual = underTest.getOriginalUrl(
                CREATIVE_ID, creativeDetails, COUNTRY_CODE);

        // then
        assertEquals(trackingUrl + "&" + encodedLandingUrlQueryParameter, actual);
    }

    @Test
    public void testGetShortenLinkCodeShouldReturnCorrectDataWhenCalled()
            throws UnsupportedEncodingException {
        // given
        String originalUrl = "originalUrl";
        String truncatedOriginalUrl = "truncatedOriginalUrl";
        CreativeDetails creativeDetails = mock(CreativeDetails.class);
        Map<String, AttributeValue> shorterUrlValues = new HashMap<>();
        shorterUrlValues.put("PATH", new AttributeValue("value1"));
        shorterUrlValues.put("PATH1", new AttributeValue("value2"));
        Pair<String, AttributeValue> partitionKey = ImmutablePair.of(
                "TRUNCATED_ORIGINAL_URL", new AttributeValue(truncatedOriginalUrl));
        when(creativeMapper.findCreativeDetailsBy(CREATIVE_ID))
                .thenReturn(creativeDetails);
        doReturn(originalUrl).when(underTest)
                .getOriginalUrl(CREATIVE_ID, creativeDetails, COUNTRY_CODE);
        when(stringHelper.truncateToBytes(originalUrl, 2048))
                .thenReturn(truncatedOriginalUrl);
        when(dynamoDbTable.query(
                ":truncatedOriginalUrl", partitionKey, "TRUNCATED_ORIGINAL_URL-index"))
                .thenReturn(Optional.of(shorterUrlValues));

        // when
        String actual = underTest.getShortenLinkCode(
                CREATIVE_ID, creativeDetails, COUNTRY_CODE);

        // then
        assertEquals("value1", actual);
    }

    @Test
    public void testGetShortenLinkCodeShouldReturnEmptyWhenShorterUrlValuesIsEmpty()
            throws UnsupportedEncodingException {
        // given
        String originalUrl = "originalUrl";
        String truncatedOriginalUrl = "truncatedOriginalUrl";
        CreativeDetails creativeDetails = mock(CreativeDetails.class);
        Pair<String, AttributeValue> partitionKey = ImmutablePair.of(
                "TRUNCATED_ORIGINAL_URL", new AttributeValue(truncatedOriginalUrl));
        when(creativeMapper.findCreativeDetailsBy(CREATIVE_ID))
                .thenReturn(creativeDetails);
        doReturn(originalUrl).when(underTest)
                .getOriginalUrl(CREATIVE_ID, creativeDetails, COUNTRY_CODE);
        when(stringHelper.truncateToBytes(originalUrl, 2048))
                .thenReturn(truncatedOriginalUrl);
        when(dynamoDbTable.query(
                ":truncatedOriginalUrl", partitionKey, "TRUNCATED_ORIGINAL_URL-index"))
                .thenReturn(Optional.empty());

        // when
        String actual = underTest.getShortenLinkCode(
                CREATIVE_ID, creativeDetails, COUNTRY_CODE);

        // then
        assertEquals(EMPTY, actual);
    }

    @Test
    public void testGetAffiliateLinkShouldReturnCorrectDataWhenCalled()
            throws UnsupportedEncodingException, ExecutionException {
        // given
        doReturn(BASE_SHORT_AFFILIATE_URL).when(underTest).getBaseShortAffiliateUrl();
        CreativeDetails creativeDetails = mock(CreativeDetails.class);
        long campaignId = 1;
        when(creativeDetails.getCampaignId()).thenReturn(campaignId);
        when(creativeMapper.findCreativeDetailsBy(CREATIVE_ID)).thenReturn(
                creativeDetails);
        when(merchantAccountMapper.findCountryCodeBy(campaignId)).thenReturn(
                COUNTRY_CODE);
        String shortenLinkCode = "shortenLinkCode";
        when(creativeDetails.getSiteId()).thenReturn(SITE_ID);


        // when
        String actual = underTest.getAffiliateLink(CREATIVE_ID, shortenLinkCode);

        // then
        assertEquals(BASE_TRACKING_URL + "/go/shortenLinkCode", actual);
    }

    @Test
    public void testGetAffiliateLinkShouldReturnCorrectDataWhenShortenLinkCodeIsEmpty()
            throws UnsupportedEncodingException, ExecutionException {
        // given
        doReturn(BASE_SHORT_AFFILIATE_URL).when(underTest).getBaseShortAffiliateUrl();
        CreativeDetails creativeDetails = mock(CreativeDetails.class);
        long campaignId = 1;
        when(creativeDetails.getCampaignId()).thenReturn(campaignId);
        when(creativeMapper.findCreativeDetailsBy(CREATIVE_ID)).thenReturn(
                creativeDetails);
        when(merchantAccountMapper.findCountryCodeBy(campaignId)).thenReturn(
                COUNTRY_CODE);
        doReturn("shortenLinkCode").when(underTest)
                .getShortenLinkCode(CREATIVE_ID, creativeDetails, COUNTRY_CODE);

        // when
        String actual = underTest.getAffiliateLink(CREATIVE_ID, EMPTY);

        // then
        assertEquals(BASE_TRACKING_URL + "/go/shortenLinkCode", actual);
    }

    @Test
    public void testGetAffiliateLinkShouldThrowTaekkyeonExceptionWhenGivenCreativeIdDoesNotExist() {
        // given
        long creativeId = 30L;
        String expectedMessage = "The banner 30 is not found.";
        doThrow(new TaekkyeonException(expectedMessage)).when(underTest)
                .getCreativeDetailsBy(creativeId);

        // when
        try {
            underTest.getAffiliateLink(creativeId, EMPTY);
            fail();
        } catch (TaekkyeonException | UnsupportedEncodingException ex) {
            // then
            assertEquals(expectedMessage, ex.getMessage());
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testGetAffiliateLinkShouldThrowTaekkyeonExceptionWhenCountryCodeDoesNotExist() {
        // given
        String expectedMessage = "The country was not found for campaign: 123.";
        Long campaignId = 123L;
        CreativeDetails creativeDetails = mock(CreativeDetails.class);
        when(creativeDetails.getSiteId()).thenReturn(SITE_ID);
        when(creativeDetails.getCampaignId()).thenReturn(campaignId);
        when(creativeMapper.findCreativeDetailsBy(CREATIVE_ID)).thenReturn(
                creativeDetails);
        doThrow(new TaekkyeonException(expectedMessage)).when(underTest)
                .getCountryCodeBy(campaignId);

        // when
        try {
            underTest.getAffiliateLink(CREATIVE_ID, EMPTY);
            fail();
        } catch (TaekkyeonException | UnsupportedEncodingException
                 | ExecutionException ex) {
            // then
            assertEquals(expectedMessage, ex.getMessage());
        }
    }

    @Test
    public void testCreateAffiliateLinkForQuickLinkShouldReturnCorrectDataWhenCalled() {
        // given
        String baseUrl = "baseUrl";
        String expected = "baseUrl/000001000001";
        when(creativeHelper.createRk(CREATIVE_ID, SITE_ID)).thenReturn(RK);

        // when
        String actual = underTest.createAffiliateLinkForQuickLink(
                CREATIVE_ID, SITE_ID, baseUrl);

        // then
        assertEquals(expected, actual);
    }

    @Test
    public void testProcessCreativesShouldSetAffiliateLinkForQuickLink() {
        // given
        Creative quickLinkCreative = new Creative(1L, "quick", "QUICK_LINK", EMPTY, EMPTY);
        List<Creative> creatives = Collections.singletonList(quickLinkCreative);
        String expected = "QUICK_LINK";

        // when
        List<Creative> actual = underTest.processCreatives(creatives);

        // then
        assertEquals(1, actual.size());
        assertEquals(expected, actual.get(0).getAffiliateLink());
    }

    @Test
    public void testProcessCreativesShouldSetAffiliateLinkForProductFeed() {
        // given
        Creative productFeedCreative = new Creative(2L, "product", "PRODUCT_FEED", EMPTY,
                EMPTY);
        List<Creative> creatives = Collections.singletonList(productFeedCreative);

        // when
        List<Creative> actual = underTest.processCreatives(creatives);

        // then
        assertEquals(1, actual.size());
        assertEquals("PRODUCT_FEED_LINK", actual.get(0).getAffiliateLink());
    }

    @Test
    public void testProcessCreativesShouldSetAffiliateLinkForCustom()
            throws UnsupportedEncodingException, ExecutionException {
        // given
        Creative customCreative = new Creative(3L, "custom", "CUSTOM", EMPTY, "shortCode");
        List<Creative> creatives = Collections.singletonList(customCreative);
        doReturn("customAffiliate").when(underTest).getAffiliateLink(3L, "shortCode");

        // when
        List<Creative> actual = underTest.processCreatives(creatives);

        // then
        assertEquals(1, actual.size());
        assertEquals("customAffiliate", actual.get(0).getAffiliateLink());
    }

    @Test
    public void testProcessCreativesShouldSetAffiliateLinkForDefaultType()
            throws UnsupportedEncodingException, ExecutionException {
        // given
        Creative defaultCreative = new Creative(4L, "default", TEXT, EMPTY, EMPTY);
        List<Creative> creatives = Collections.singletonList(defaultCreative);
        String expected = "TEXT Link";

        // when
        List<Creative> actual = underTest.processCreatives(creatives);

        // then
        assertEquals(1, actual.size());
        assertEquals(expected, actual.get(0).getAffiliateLink());
    }
}
