/**
 * Copyright © 2021 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import static java.math.BigDecimal.ZERO;

/**
 * DTO for holding publisher funnel conversion tracking reward data.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter @Setter
public class OccurredRewardFunnelDetails {

    public static final OccurredRewardFunnelDetails DEFAULT_ITEM = new OccurredRewardFunnelDetails(
            0, ZERO, ZERO, ZERO, ZERO, ZERO);

    private long siteId;
    private BigDecimal occurredSalesReward;
    private BigDecimal occurredTransactionAmountReward;
    private BigDecimal occurredAtCommission;
    private BigDecimal occurredMerchantAgentCommission;
    private BigDecimal occurredPublisherAgentCommission;
}
