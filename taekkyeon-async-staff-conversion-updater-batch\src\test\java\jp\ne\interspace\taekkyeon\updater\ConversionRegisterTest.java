/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.updater;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Sets;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.slf4j.Logger;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.CampaignClosurePeriod;
import jp.ne.interspace.taekkyeon.model.CampaignSettingDuplicationCutDetails;
import jp.ne.interspace.taekkyeon.model.ClickSession;
import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.ConversionImportDetails;
import jp.ne.interspace.taekkyeon.model.ConversionImportErrorDetails;
import jp.ne.interspace.taekkyeon.model.ConversionImportResult;
import jp.ne.interspace.taekkyeon.model.ConversionInsertRequest;
import jp.ne.interspace.taekkyeon.model.ConversionRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionSqsRegistrationRequest;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.ConversionUpdateErrorDetails;
import jp.ne.interspace.taekkyeon.model.ConversionUpdateRequest;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.PostbackStatus;
import jp.ne.interspace.taekkyeon.model.RewardSettings;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CampaignClosureMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ClickParametersMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CountryMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.MonthlyClosureMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.PostbackUrlMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.StaffAccountMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.CampaignSettingsMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.core.MerchantAccountMapper;
import jp.ne.interspace.taekkyeon.service.AffiliationService;
import jp.ne.interspace.taekkyeon.service.ClickSessionService;
import jp.ne.interspace.taekkyeon.service.CommonConversionService;
import jp.ne.interspace.taekkyeon.service.ConversionService;
import jp.ne.interspace.taekkyeon.service.CountryService;
import jp.ne.interspace.taekkyeon.service.PublisherAccountService;
import jp.ne.interspace.taekkyeon.service.RewardService;
import jp.ne.interspace.taekkyeon.util.DateUtils;
import jp.ne.interspace.taekkyeon.validator.CommonValidator;
import jp.ne.interspace.taekkyeon.validator.ConversionRegisterValidator;

import static java.math.BigDecimal.ZERO;
import static java.time.LocalDate.of;
import static java.time.LocalDateTime.MIN;
import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.EMPTY;
import static jp.ne.interspace.taekkyeon.model.CommissionType.GROSS_FIXED_AMOUNT;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.APPROVED;
import static jp.ne.interspace.taekkyeon.model.ConversionStatus.PENDING;
import static jp.ne.interspace.taekkyeon.model.DeviceType.IPAD;
import static jp.ne.interspace.taekkyeon.model.DuplicationCutTarget.PERMANENT;
import static jp.ne.interspace.taekkyeon.model.PostbackStatus.NEEDED;
import static jp.ne.interspace.taekkyeon.model.RewardType.CPA_FIXED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link ConversionRegister}.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class ConversionRegisterTest {

    private static final String ERROR_MESSAGE = "test error message";
    private static final String ERROR_MESSAGE_INSERT_FAILED = "Insert failed: java.lang.Throwable: Error occurred";
    private static final String ERROR_MESSAGE_OUTPUT_LOG = "Insert failed:[insertRequest]:{}\n"
            + ":: java.lang.Throwable: Error occurred";

    private static final long STAFF_ID = 11;
    private static final String STAFF_UID = "staff_uid";
    private static final String ZONE_ID = "Asia/Singapore";
    private static final ZoneId LOCAL_ZONE_ID = ZoneId.of(ZONE_ID);
    private static final LocalDate CLOSED_DATE = LocalDate.of(2020, 1, 17);

    private static final long CAMPAIGN_ID = 22;
    private static final long CLICK_FROM_CAMPAIGN_ID = 33;
    private static final String CAMPAIGN_NAME = "test campaign name";
    private static final LocalDate CONFIRMATION_DATE = LocalDate.of(2020, 1, 18);
    private static final LocalDateTime CONFIRMATION_TIME = LocalDateTime.of(2020, 1, 18,
            0, 0, 0);
    private static final String MERCHANT_COUNTRY_CODE = "ID";

    private static final long SITE_ID = 33L;
    private static final long CREATIVE_ID = 44L;
    private static final String CLICK_IP = "***********";
    private static final String LANGUAGE = "en-US";
    private static final String EXPECTED_LANGUAGE = "English";
    private static final String UUID = "UUID";
    private static final LocalDateTime CLICK_TIME = LocalDateTime.of(2020, 1, 20, 22, 33,
            44);

    private static final LocalDateTime EXPECTED_CLICK_TIME = LocalDateTime.of(2020, 1, 20, 23, 33,
            44);

    private static final List<CampaignClosurePeriod> CAMPAIGN_CLOSURE_PERIODS = Arrays
            .asList(mock(CampaignClosurePeriod.class));
    private static final int RANK = 7;
    private static final ZonedDateTime CONVERSION_TIME = ZonedDateTime.of(2020, 1, 19, 11,
            22, 33, 0, LOCAL_ZONE_ID);
    private static final LocalDateTime CONVERSION_TIME_LOCAL = LocalDateTime.of(2020, 1,
            19, 11, 22, 33, 0);
    private static final String TRANSACTION_ID = "test transaction ID";
    private static final int RESULT_ID = 3;
    private static final String CUSTOMER_TYPE = "test customer type";
    private static final String PRODUCT_CATEGORY_ID = "test category ID";
    private static final String PRODUCT_ID = "test product ID";
    private static final int PRODUCT_QUANTITY = 2;
    private static final BigDecimal PRODUCT_UNIT_PRICE = new BigDecimal("11.11");
    private static final BigDecimal DISCOUNT = BigDecimal.TEN;
    private static final String CLICK_ID = "test click ID";

    private static final String CONVERSION_TRANSACTION_ID = "test conversion transaction ID";
    private static final String INTERNAL_TRANSACTION_ID = "test intenal transaction ID";
    private static final BigDecimal TRANSACTION_AMOUNT = new BigDecimal("67.77");
    private static final ZonedDateTime CREATION_TIME = ZonedDateTime.of(2020, 1, 21, 1, 2,
            3, 0, LOCAL_ZONE_ID);

    @InjectMocks @Spy
    private ConversionRegister underTest;

    @Mock
    private CampaignSettingsMapper campaignSettingsMapper;

    @Mock
    private ClickParametersMapper clickParametersMapper;

    @Mock
    private ConversionLogMapper conversionLogMapper;

    @Mock
    private CountryMapper countryMapper;

    @Mock
    private MerchantAccountMapper merchantAccountMapper;

    @Mock
    private MonthlyClosureMapper monthlyClosureMapper;

    @Mock
    private PostbackUrlMapper postbackUrlMapper;

    @Mock
    private StaffAccountMapper staffAccountMapper;

    @Mock
    private CampaignClosureMapper campaignClosureMapper;

    @Mock
    private AffiliationService affiliationService;

    @Mock
    private ClickSessionService clickSessionService;

    @Mock
    private CommonConversionService commonConversionService;

    @Mock
    private ConversionService conversionService;

    @Mock
    private PublisherAccountService publisherAccountService;

    @Mock
    private RewardService rewardService;

    @Mock
    private CountryService countryService;

    @Mock
    private DateUtils dateUtils;

    @Mock
    private CommonValidator commonValidator;

    @Mock
    private ConversionRegisterValidator conversionRegisterValidator;

    @Mock
    private Logger logger;

    @Mock
    private RuntimeException exception;

    @Before
    public void doMock() {
        Throwable throwable = new Throwable("Error occurred");
        doReturn(throwable).when(exception).getCause();
    }

    @Test
    public void testUpdateShouldReturnCorrectDataWhenClosedDateIsBeforeConfirmationDate() {
        // given
        List<ConversionRegistrationDetails> conversions =
                Arrays.asList(mock(ConversionRegistrationDetails.class));
        ConversionImportDetails importDetails = createConversionImportDetails(0L,
                CONFIRMATION_DATE, conversions);

        when(staffAccountMapper.findStaffUidBy(STAFF_ID)).thenReturn(STAFF_UID);

        when(merchantAccountMapper.findCountryCodeBy(importDetails.getCampaignId()))
                .thenReturn(MERCHANT_COUNTRY_CODE);
        when(monthlyClosureMapper.findClosedDateBy(MERCHANT_COUNTRY_CODE))
                .thenReturn(CLOSED_DATE);
        when(countryMapper.findZoneIdBy(MERCHANT_COUNTRY_CODE)).thenReturn(ZONE_ID);

        String errorMessage = "test error message";
        when(campaignClosureMapper
                .findValidatedCampaignClosurePeriods(Sets.newHashSet(CAMPAIGN_ID), CLOSED_DATE))
                        .thenReturn(CAMPAIGN_CLOSURE_PERIODS);
        when(commonValidator.validateDate(CONFIRMATION_TIME, CLOSED_DATE,
                CAMPAIGN_CLOSURE_PERIODS))
                .thenReturn(errorMessage);

        // when
        ConversionImportResult actual = underTest.update(importDetails, STAFF_ID);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.getSuccessfulConversionCount());
        assertEquals(1, actual.getFailedConversionCount());
        assertNotNull(actual.getErrorDetails());
        assertEquals(1, actual.getErrorDetails().size());
        ConversionImportErrorDetails actualErrorDetails = (ConversionImportErrorDetails)
                actual.getErrorDetails().get(0);
        assertFields(actualErrorDetails, MIN, EMPTY, 0, EMPTY, EMPTY, EMPTY, 0, ZERO,
                ZERO, PENDING, EMPTY, errorMessage);
        assertNotNull(actual.getUpdateRequest());
        assertTrue(actual.getUpdateRequest().isEmpty());
        assertEquals(conversions.size(), actual.getTotalConversionCount());
    }

    @Test
    public void testUpdateShouldReturnCorrectDataWhenClosedDateIsAfterConfirmationDate() {
        // given
        List<ConversionRegistrationDetails> conversions =
                Arrays.asList(mock(ConversionRegistrationDetails.class));
        ConversionImportDetails importDetails = createConversionImportDetails(0L,
                CONFIRMATION_DATE, conversions);

        when(staffAccountMapper.findStaffUidBy(STAFF_ID)).thenReturn(STAFF_UID);

        LocalDate closedDate = of(2020, 1, 17);
        when(merchantAccountMapper.findCountryCodeBy(importDetails.getCampaignId()))
                .thenReturn(MERCHANT_COUNTRY_CODE);
        when(monthlyClosureMapper.findClosedDateBy(MERCHANT_COUNTRY_CODE))
                .thenReturn(closedDate);
        when(countryMapper.findZoneIdBy(MERCHANT_COUNTRY_CODE)).thenReturn(ZONE_ID);

        when(campaignClosureMapper
                .findValidatedCampaignClosurePeriods(Sets.newHashSet(CAMPAIGN_ID), closedDate))
                        .thenReturn(CAMPAIGN_CLOSURE_PERIODS);
        when(commonValidator.validateDate(CONFIRMATION_TIME, closedDate,
                CAMPAIGN_CLOSURE_PERIODS)).thenReturn(EMPTY);
        ConversionImportResult expected = mock(ConversionImportResult.class);
        doReturn(expected).when(underTest).insertConversion(importDetails, closedDate,
                STAFF_UID, ZONE_ID, CAMPAIGN_CLOSURE_PERIODS);

        // when
        ConversionImportResult actual = underTest.update(importDetails, STAFF_ID);

        // then
        assertNotNull(actual);
        assertSame(expected, actual);
    }

    @Test
    public void testInsertConversionShouldReturnCorrectDataWhenValidationErrorOccurs() {
        // given
        ConversionRegistrationDetails conversion =
                mock(ConversionRegistrationDetails.class);
        when(conversion.getClickId()).thenReturn(CLICK_ID);
        when(conversion.getConversionTime()).thenReturn(CONVERSION_TIME);
        List<ConversionRegistrationDetails> conversions = Arrays.asList(conversion);
        ConversionImportDetails importDetails = createConversionImportDetails(0L,
                CONFIRMATION_DATE, conversions);

        CampaignSettingDuplicationCutDetails campaignSettingDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(campaignSettingsMapper.findDuplicationCutDetailsBy(CAMPAIGN_ID))
                .thenReturn(campaignSettingDetails);
        when(merchantAccountMapper.findCountryCodeBy(CAMPAIGN_ID))
                .thenReturn(MERCHANT_COUNTRY_CODE);

        ClickSession clickSession = ClickSession.DEFAULT_CLICK_SESSION;
        when(clickSessionService.findClickSession(CAMPAIGN_ID, CLICK_ID))
                .thenReturn(clickSession);

        when(affiliationService.findRank(CAMPAIGN_ID, clickSession.getSiteId(),
                CONVERSION_TIME_LOCAL)).thenReturn(RANK);

        when(conversionRegisterValidator.validateEligibleForInsert(conversion,
                clickSession, CONFIRMATION_DATE, CLOSED_DATE, RANK,
                CAMPAIGN_CLOSURE_PERIODS, CAMPAIGN_ID)).thenReturn(ERROR_MESSAGE);

        ConversionImportErrorDetails importErrorDetails = mock(
                ConversionImportErrorDetails.class);
        doReturn(importErrorDetails).when(underTest).createImportErrorDetails(conversion,
                ERROR_MESSAGE);

        // when
        ConversionImportResult actual = underTest.insertConversion(importDetails,
                CLOSED_DATE, STAFF_UID, ZONE_ID, CAMPAIGN_CLOSURE_PERIODS);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.getSuccessfulConversionCount());
        assertEquals(1, actual.getFailedConversionCount());
        assertNotNull(actual.getErrorDetails());
        assertEquals(1, actual.getErrorDetails().size());
        ConversionImportErrorDetails actualErrorDetails = (ConversionImportErrorDetails) actual
                .getErrorDetails().get(0);
        assertEquals(importErrorDetails, actualErrorDetails);
        assertNotNull(actual.getUpdateRequest());
        assertTrue(actual.getUpdateRequest().isEmpty());
        assertEquals(conversions.size(), actual.getTotalConversionCount());
    }

    @Test
    public void testInsertConversionShouldReturnCorrectDataWhenNoValidationErrorOccursAndInsertSuccessful() {
        // given
        ConversionRegistrationDetails conversion =
                mock(ConversionRegistrationDetails.class);
        when(conversion.getClickId()).thenReturn(CLICK_ID);
        when(conversion.getConversionTime()).thenReturn(CONVERSION_TIME);
        List<ConversionRegistrationDetails> conversions = Arrays.asList(conversion);
        ConversionImportDetails importDetails = createConversionImportDetails(0L,
                CONFIRMATION_DATE, conversions);

        CampaignSettingDuplicationCutDetails campaignSettingDetails =
                mock(CampaignSettingDuplicationCutDetails.class);
        when(campaignSettingsMapper.findDuplicationCutDetailsBy(CAMPAIGN_ID))
                .thenReturn(campaignSettingDetails);
        when(merchantAccountMapper.findCountryCodeBy(CAMPAIGN_ID))
                .thenReturn(MERCHANT_COUNTRY_CODE);

        ClickSession clickSession = mock(ClickSession.class);
        Map<String, String> additionalParameters = Collections.emptyMap();
        when(clickSession.getAdditionalParameters()).thenReturn(additionalParameters);
        when(clickSessionService.findClickSession(CAMPAIGN_ID, CLICK_ID))
                .thenReturn(clickSession);

        when(affiliationService.findRank(CAMPAIGN_ID, clickSession.getSiteId(),
                CONVERSION_TIME_LOCAL)).thenReturn(RANK);

        when(conversionRegisterValidator.validateEligibleForInsert(conversion,
                clickSession, CONFIRMATION_DATE, CLOSED_DATE, RANK,
                CAMPAIGN_CLOSURE_PERIODS, CAMPAIGN_ID)).thenReturn(EMPTY);

        List<ConversionInsertRequest> insertRequests = new LinkedList<>();
        ConversionInsertRequest insertRequest = mock(ConversionInsertRequest.class);
        insertRequests.add(insertRequest);

        doReturn(insertRequests).when(underTest).createInsertRequest(CAMPAIGN_ID,
                CONFIRMATION_DATE, conversion, campaignSettingDetails, clickSession,
                STAFF_UID, LOCAL_ZONE_ID, RANK);
        doReturn(EMPTY).when(underTest).insertConversionWithClickParameters(insertRequests,
                additionalParameters);

        ConversionSqsRegistrationRequest sqsRegistrationRequest = mock(
                ConversionSqsRegistrationRequest.class);
        when(commonConversionService.createSqsRegistrationRequest(insertRequest,
                MERCHANT_COUNTRY_CODE, LOCAL_ZONE_ID)).thenReturn(sqsRegistrationRequest);

        // when
        ConversionImportResult actual = underTest.insertConversion(importDetails,
                CLOSED_DATE, STAFF_UID, ZONE_ID, CAMPAIGN_CLOSURE_PERIODS);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.getSuccessfulConversionCount());
        assertEquals(0, actual.getFailedConversionCount());
        assertNotNull(actual.getErrorDetails());
        assertTrue(actual.getErrorDetails().isEmpty());
        assertNotNull(actual.getUpdateRequest());
        assertEquals(1, actual.getUpdateRequest().size());
        ConversionSqsRegistrationRequest actualSqsRegistrationRequest =
                (ConversionSqsRegistrationRequest) actual.getUpdateRequest().get(0);
        assertSame(sqsRegistrationRequest, actualSqsRegistrationRequest);
        assertEquals(conversions.size(), actual.getTotalConversionCount());
        verify(underTest).createInsertRequest(CAMPAIGN_ID, CONFIRMATION_DATE, conversion,
                campaignSettingDetails, clickSession, STAFF_UID, LOCAL_ZONE_ID, RANK);
    }

    @Test
    public void testInsertConversionShouldReturnCorrectDataWhenValidationErrorOccursAndInsertFails() {
        // given
        ConversionRegistrationDetails conversion = mock(
                ConversionRegistrationDetails.class);
        when(conversion.getClickId()).thenReturn(CLICK_ID);
        when(conversion.getConversionTime()).thenReturn(CONVERSION_TIME);
        List<ConversionRegistrationDetails> conversions = Arrays.asList(conversion);
        ConversionImportDetails importDetails = createConversionImportDetails(0L,
                CONFIRMATION_DATE, conversions);

        CampaignSettingDuplicationCutDetails campaignSettingDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(campaignSettingsMapper.findDuplicationCutDetailsBy(CAMPAIGN_ID))
                .thenReturn(campaignSettingDetails);
        when(merchantAccountMapper.findCountryCodeBy(CAMPAIGN_ID))
                .thenReturn(MERCHANT_COUNTRY_CODE);

        ClickSession clickSession = mock(ClickSession.class);
        Map<String, String> additionalParameters = Collections.emptyMap();
        when(clickSession.getAdditionalParameters()).thenReturn(additionalParameters);
        when(clickSessionService.findClickSession(CAMPAIGN_ID, CLICK_ID))
                .thenReturn(clickSession);

        when(conversionRegisterValidator.validateEligibleForInsert(conversion,
                clickSession, CONFIRMATION_DATE, CLOSED_DATE, RANK,
                CAMPAIGN_CLOSURE_PERIODS, CAMPAIGN_ID)).thenReturn(EMPTY);

        when(affiliationService.findRank(CAMPAIGN_ID, clickSession.getSiteId(),
                CONVERSION_TIME_LOCAL)).thenReturn(RANK);

        List<ConversionInsertRequest> insertRequests = new LinkedList<>();
        ConversionInsertRequest insertRequest = mock(ConversionInsertRequest.class);
        insertRequests.add(insertRequest);
        doReturn(insertRequests).when(underTest).createInsertRequest(CAMPAIGN_ID,
                CONFIRMATION_DATE, conversion, campaignSettingDetails, clickSession,
                STAFF_UID, LOCAL_ZONE_ID, RANK);
        doReturn(ERROR_MESSAGE_INSERT_FAILED).when(underTest)
                .insertConversionWithClickParameters(insertRequests, additionalParameters);

        ConversionImportErrorDetails importErrorDetails = mock(
                ConversionImportErrorDetails.class);
        doReturn(importErrorDetails).when(underTest).createImportErrorDetails(conversion,
                ERROR_MESSAGE_INSERT_FAILED);

        // when
        ConversionImportResult actual = underTest.insertConversion(importDetails,
                CLOSED_DATE, STAFF_UID, ZONE_ID, CAMPAIGN_CLOSURE_PERIODS);

        // then
        assertNotNull(actual);
        assertEquals(0, actual.getSuccessfulConversionCount());
        assertEquals(1, actual.getFailedConversionCount());
        assertNotNull(actual.getErrorDetails());
        assertEquals(1, actual.getErrorDetails().size());
        ConversionImportErrorDetails actualErrorDetails = (ConversionImportErrorDetails) actual
                .getErrorDetails().get(0);
        assertEquals(importErrorDetails, actualErrorDetails);
        assertNotNull(actual.getUpdateRequest());
        assertTrue(actual.getUpdateRequest().isEmpty());
        assertEquals(conversions.size(), actual.getTotalConversionCount());
    }

    @Test
    public void testInsertConversionShouldUseClickFromCampaignIdWhenValueIsGreaterThanZero() {
        // given
        ConversionRegistrationDetails conversion = mock(
                ConversionRegistrationDetails.class);
        when(conversion.getClickId()).thenReturn(CLICK_ID);
        when(conversion.getConversionTime()).thenReturn(CONVERSION_TIME);
        List<ConversionRegistrationDetails> conversions = Arrays.asList(conversion);
        ConversionImportDetails importDetails = createConversionImportDetails(
                CLICK_FROM_CAMPAIGN_ID, CONFIRMATION_DATE, conversions);

        CampaignSettingDuplicationCutDetails campaignSettingDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(campaignSettingsMapper.findDuplicationCutDetailsBy(CAMPAIGN_ID))
                .thenReturn(campaignSettingDetails);
        when(merchantAccountMapper.findCountryCodeBy(CAMPAIGN_ID))
                .thenReturn(MERCHANT_COUNTRY_CODE);

        ClickSession clickSession = mock(ClickSession.class);
        Map<String, String> additionalParameters = Collections.emptyMap();
        when(clickSession.getAdditionalParameters()).thenReturn(additionalParameters);
        when(clickSession.getSiteId()).thenReturn(SITE_ID);

        when(clickSessionService.findClickSession(CLICK_FROM_CAMPAIGN_ID, CLICK_ID))
                .thenReturn(clickSession);

        when(affiliationService.findRank(CAMPAIGN_ID, clickSession.getSiteId(),
                CONVERSION_TIME_LOCAL)).thenReturn(RANK);

        when(conversionRegisterValidator.validateEligibleForInsert(conversion,
                clickSession, CONFIRMATION_DATE, CLOSED_DATE, RANK,
                CAMPAIGN_CLOSURE_PERIODS, CAMPAIGN_ID)).thenReturn(EMPTY);

        List<ConversionInsertRequest> insertRequests = new LinkedList<>();
        ConversionInsertRequest insertRequest = mock(ConversionInsertRequest.class);
        insertRequests.add(insertRequest);
        doReturn(insertRequests).when(underTest).createInsertRequest(CAMPAIGN_ID,
                CONFIRMATION_DATE, conversion, campaignSettingDetails, clickSession,
                STAFF_UID, LOCAL_ZONE_ID, RANK);
        doReturn(EMPTY).when(underTest).insertConversionWithClickParameters(insertRequests,
                additionalParameters);

        ConversionSqsRegistrationRequest sqsRegistrationRequest = mock(
                ConversionSqsRegistrationRequest.class);
        when(commonConversionService.createSqsRegistrationRequest(insertRequest,
                MERCHANT_COUNTRY_CODE, LOCAL_ZONE_ID)).thenReturn(sqsRegistrationRequest);

        // when
        ConversionImportResult actual = underTest.insertConversion(importDetails,
                CLOSED_DATE, STAFF_UID, ZONE_ID, CAMPAIGN_CLOSURE_PERIODS);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.getSuccessfulConversionCount());
        assertEquals(0, actual.getFailedConversionCount());
        assertNotNull(actual.getErrorDetails());
        assertTrue(actual.getErrorDetails().isEmpty());
        assertNotNull(actual.getUpdateRequest());
        assertEquals(1, actual.getUpdateRequest().size());

        verify(clickSessionService).findClickSession(CLICK_FROM_CAMPAIGN_ID, CLICK_ID);
        verify(clickSessionService, never()).findClickSession(CAMPAIGN_ID, CLICK_ID);
    }

    @Test
    public void testInsertConversionShouldUseCampaignIdWhenClickFromCampaignIdIsZero() {
        // given
        ConversionRegistrationDetails conversion = mock(
                ConversionRegistrationDetails.class);
        when(conversion.getClickId()).thenReturn(CLICK_ID);
        when(conversion.getConversionTime()).thenReturn(CONVERSION_TIME);
        List<ConversionRegistrationDetails> conversions = Arrays.asList(conversion);
        ConversionImportDetails importDetails = createConversionImportDetails(0L,
                CONFIRMATION_DATE, conversions);

        CampaignSettingDuplicationCutDetails campaignSettingDetails = mock(
                CampaignSettingDuplicationCutDetails.class);
        when(campaignSettingsMapper.findDuplicationCutDetailsBy(CAMPAIGN_ID))
                .thenReturn(campaignSettingDetails);
        when(merchantAccountMapper.findCountryCodeBy(CAMPAIGN_ID))
                .thenReturn(MERCHANT_COUNTRY_CODE);

        ClickSession clickSession = mock(ClickSession.class);
        Map<String, String> additionalParameters = Collections.emptyMap();
        when(clickSession.getAdditionalParameters()).thenReturn(additionalParameters);
        when(clickSession.getSiteId()).thenReturn(SITE_ID);

        when(clickSessionService.findClickSession(CAMPAIGN_ID, CLICK_ID))
                .thenReturn(clickSession);

        when(affiliationService.findRank(CAMPAIGN_ID, clickSession.getSiteId(),
                CONVERSION_TIME_LOCAL)).thenReturn(RANK);

        when(conversionRegisterValidator.validateEligibleForInsert(conversion,
                clickSession, CONFIRMATION_DATE, CLOSED_DATE, RANK,
                CAMPAIGN_CLOSURE_PERIODS, CAMPAIGN_ID)).thenReturn(EMPTY);

        List<ConversionInsertRequest> insertRequests = new LinkedList<>();
        ConversionInsertRequest insertRequest = mock(ConversionInsertRequest.class);
        insertRequests.add(insertRequest);
        doReturn(insertRequests).when(underTest).createInsertRequest(CAMPAIGN_ID,
                CONFIRMATION_DATE, conversion, campaignSettingDetails, clickSession,
                STAFF_UID, LOCAL_ZONE_ID, RANK);
        doReturn(EMPTY).when(underTest).insertConversionWithClickParameters(insertRequests,
                additionalParameters);

        ConversionSqsRegistrationRequest sqsRegistrationRequest = mock(
                ConversionSqsRegistrationRequest.class);
        when(commonConversionService.createSqsRegistrationRequest(insertRequest,
                MERCHANT_COUNTRY_CODE, LOCAL_ZONE_ID)).thenReturn(sqsRegistrationRequest);

        // when
        ConversionImportResult actual = underTest.insertConversion(importDetails,
                CLOSED_DATE, STAFF_UID, ZONE_ID, CAMPAIGN_CLOSURE_PERIODS);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.getSuccessfulConversionCount());
        assertEquals(0, actual.getFailedConversionCount());
        assertNotNull(actual.getErrorDetails());
        assertTrue(actual.getErrorDetails().isEmpty());
        assertNotNull(actual.getUpdateRequest());
        assertEquals(1, actual.getUpdateRequest().size());

        verify(clickSessionService).findClickSession(CAMPAIGN_ID, CLICK_ID);
        verify(clickSessionService, never()).findClickSession(CLICK_FROM_CAMPAIGN_ID, CLICK_ID);
    }

    @Test
    public void testCreateInsertRequestShouldReturnCorrectDataWhenCalled() {
        // given
        ConversionRegistrationDetails conversion = createConversionRegistrationDetails(
                CONVERSION_TIME, TRANSACTION_ID, RESULT_ID, CUSTOMER_TYPE,
                PRODUCT_CATEGORY_ID, PRODUCT_ID, PRODUCT_QUANTITY, PRODUCT_UNIT_PRICE,
                DISCOUNT, APPROVED, CLICK_ID);
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(true, PERMANENT);
        ClickSession clickSession = new ClickSession(CREATIVE_ID, SITE_ID, CLICK_IP,
                LANGUAGE, UUID, CLICK_TIME, IPAD, null);

        RewardSettings rewardSettings = mock(RewardSettings.class);
        when(rewardSettings.getRewardType()).thenReturn(CPA_FIXED);
        when(rewardSettings.getCommissionType()).thenReturn(GROSS_FIXED_AMOUNT);
        when(rewardService.findRewardSettings(conversion, CAMPAIGN_ID, RANK, IPAD))
                .thenReturn(rewardSettings);

        doReturn(CONFIRMATION_TIME).when(underTest).getConfirmationTimeFrom(
                CONFIRMATION_DATE, APPROVED);
        when(commonConversionService.createInternalTransactionId(conversion, CAMPAIGN_ID,
                duplicationCutDetails)).thenReturn(INTERNAL_TRANSACTION_ID);
        when(commonConversionService.getUtcZonedDateTimeWith(LOCAL_ZONE_ID))
                .thenReturn(CREATION_TIME);
        when(commonConversionService.getPostbackStatusFrom(SITE_ID, CREATIVE_ID)).thenReturn(
                NEEDED);
        doReturn(EXPECTED_CLICK_TIME).when(underTest).createDateFrom(LOCAL_ZONE_ID,
                CLICK_TIME);
        doReturn(EXPECTED_LANGUAGE).when(underTest).getLanguageFrom(LANGUAGE);
        List<String> transactionIds = Arrays.asList(CONVERSION_TRANSACTION_ID);
        when(conversionService.generateTransactionIdsFrom(conversion.getProductQuantity(),
                conversion.getConversionTime().toLocalDateTime(),
                conversion.getTransactionId(), conversion.getProductId(),
                conversion.getResultId(), CAMPAIGN_ID, conversion.getCustomerType()))
                .thenReturn(transactionIds);

        // when
        List<ConversionInsertRequest> actual = underTest.createInsertRequest(CAMPAIGN_ID,
                CONFIRMATION_DATE, conversion, duplicationCutDetails, clickSession,
                STAFF_UID, LOCAL_ZONE_ID, RANK);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertFields(actual.get(0), 0L, CREATIVE_ID, CAMPAIGN_ID, EXPECTED_CLICK_TIME,
                CONVERSION_TIME_LOCAL, CONFIRMATION_TIME, CONVERSION_TRANSACTION_ID,
                INTERNAL_TRANSACTION_ID, SITE_ID, RANK, TRANSACTION_ID, RESULT_ID,
                PRODUCT_ID, APPROVED, 1, new BigDecimal("6.11"), new BigDecimal("6.11"),
                CPA_FIXED, GROSS_FIXED_AMOUNT, IPAD, PRODUCT_CATEGORY_ID, new BigDecimal("5.00"),
                STAFF_UID, CREATION_TIME, NEEDED, CUSTOMER_TYPE, CLICK_IP,
                EXPECTED_LANGUAGE, UUID);
    }

    @Test
    public void testCreateInsertRequestShouldReturnCorrectDataWhenDiscountIsHigherThanUnitPrice() {
        // given
        ConversionRegistrationDetails conversion = createConversionRegistrationDetails(
                CONVERSION_TIME, TRANSACTION_ID, RESULT_ID, CUSTOMER_TYPE,
                PRODUCT_CATEGORY_ID, PRODUCT_ID, PRODUCT_QUANTITY, PRODUCT_UNIT_PRICE,
                new BigDecimal(1000), APPROVED, CLICK_ID);
        CampaignSettingDuplicationCutDetails duplicationCutDetails =
                new CampaignSettingDuplicationCutDetails(true, PERMANENT);
        ClickSession clickSession = new ClickSession(CREATIVE_ID, SITE_ID, CLICK_IP,
                LANGUAGE, UUID, CLICK_TIME, IPAD, null);

        RewardSettings rewardSettings = mock(RewardSettings.class);
        when(rewardSettings.getRewardType()).thenReturn(CPA_FIXED);
        when(rewardSettings.getCommissionType()).thenReturn(GROSS_FIXED_AMOUNT);
        when(rewardService.findRewardSettings(conversion, CAMPAIGN_ID, RANK, IPAD))
                .thenReturn(rewardSettings);

        doReturn(CONFIRMATION_TIME).when(underTest).getConfirmationTimeFrom(
                CONFIRMATION_DATE, APPROVED);
        when(commonConversionService.createInternalTransactionId(conversion, CAMPAIGN_ID,
                duplicationCutDetails)).thenReturn(INTERNAL_TRANSACTION_ID);
        when(commonConversionService.getUtcZonedDateTimeWith(LOCAL_ZONE_ID))
                .thenReturn(CREATION_TIME);
        when(commonConversionService.getPostbackStatusFrom(SITE_ID, CREATIVE_ID)).thenReturn(
                NEEDED);
        doReturn(EXPECTED_LANGUAGE).when(underTest).getLanguageFrom(LANGUAGE);
        List<String> transactionIds = Arrays.asList(CONVERSION_TRANSACTION_ID);
        when(conversionService.generateTransactionIdsFrom(conversion.getProductQuantity(),
                conversion.getConversionTime().toLocalDateTime(),
                conversion.getTransactionId(), conversion.getProductId(),
                conversion.getResultId(), CAMPAIGN_ID, conversion.getCustomerType()))
                .thenReturn(transactionIds);
        doReturn(EXPECTED_CLICK_TIME).when(underTest).createDateFrom(LOCAL_ZONE_ID,
                CLICK_TIME);

        // when
        List<ConversionInsertRequest> actual = underTest.createInsertRequest(CAMPAIGN_ID,
                CONFIRMATION_DATE, conversion, duplicationCutDetails, clickSession,
                STAFF_UID, LOCAL_ZONE_ID, RANK);

        // then
        assertNotNull(actual);
        assertEquals(1, actual.size());
        assertFields(actual.get(0), 0L, CREATIVE_ID, CAMPAIGN_ID, EXPECTED_CLICK_TIME,
                CONVERSION_TIME_LOCAL, CONFIRMATION_TIME, CONVERSION_TRANSACTION_ID,
                INTERNAL_TRANSACTION_ID, SITE_ID, RANK, TRANSACTION_ID, RESULT_ID,
                PRODUCT_ID, APPROVED, 1, ZERO, ZERO, CPA_FIXED, GROSS_FIXED_AMOUNT, IPAD,
                PRODUCT_CATEGORY_ID, new BigDecimal("500.00"), STAFF_UID, CREATION_TIME,
                NEEDED, CUSTOMER_TYPE, CLICK_IP, EXPECTED_LANGUAGE, UUID);
    }

    @Test
    public void testInsertConversionWithClickParametersShouldReturnEmptyWhenInsertSuccessful() {
        // given
        List<ConversionInsertRequest> insertRequests = new LinkedList<>();
        ConversionInsertRequest insertRequest = mock(ConversionInsertRequest.class);
        when(insertRequest.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(insertRequest.getInternalTransactionId()).thenReturn(
                INTERNAL_TRANSACTION_ID);
        when(insertRequest.getCreator()).thenReturn(STAFF_UID);
        insertRequests.add(insertRequest);
        Map<String, String> additionalParameters = Collections.emptyMap();

        when(conversionLogMapper.insert(insertRequest)).thenReturn(1);

        // when
        String actual = underTest.insertConversionWithClickParameters(insertRequests,
                additionalParameters);

        // then
        assertEquals(EMPTY, actual);
        verify(commonConversionService).insertClickParameters(additionalParameters, CAMPAIGN_ID,
                INTERNAL_TRANSACTION_ID, STAFF_UID);
    }

    @Test
    public void testInsertConversionWithClickParametersShouldReturnErrorMessageWhenInsertFailed() {
        // given
        List<ConversionInsertRequest> insertRequests = new LinkedList<>();
        ConversionInsertRequest insertRequest = mock(ConversionInsertRequest.class);
        when(insertRequest.getCampaignId()).thenReturn(CAMPAIGN_ID);
        when(insertRequest.getTransactionId()).thenReturn(CONVERSION_TRANSACTION_ID);
        when(insertRequest.getCreator()).thenReturn(STAFF_UID);
        Map<String, String> additionalParameters = Collections.emptyMap();
        insertRequests.add(insertRequest);

        when(conversionLogMapper.insert(insertRequests.get(0))).thenReturn(0);

        // when
        String actual = underTest.insertConversionWithClickParameters(insertRequests,
                additionalParameters);

        // then
        assertEquals("Insert failed", actual);
        verify(commonConversionService, never()).insertClickParameters(anyMap(), anyLong(),
                anyString(), anyString());
    }

    @Test
    public void testInsertConversionWithClickParametersShouldLogAnErrorAndReturnErrorMessageWhenInsertErrorOccurs() {
        // given
        List<ConversionInsertRequest> insertRequests = new LinkedList<>();
        ConversionInsertRequest insertRequest = mock(ConversionInsertRequest.class);
        when(insertRequest.toString()).thenReturn("insertRequest");
        insertRequests.add(insertRequest);
        Map<String, String> additionalParameters = Collections.emptyMap();

        when(conversionLogMapper.insert(insertRequest)).thenThrow(exception);
        doReturn(logger).when(underTest).getLogger();

        // when
        String actual = underTest.insertConversionWithClickParameters(insertRequests,
                additionalParameters);

        // then
        assertEquals(ERROR_MESSAGE_INSERT_FAILED, actual);
        verify(logger).error(ERROR_MESSAGE_OUTPUT_LOG);
        verify(commonConversionService, never()).insertClickParameters(anyMap(), anyLong(),
                anyString(), anyString());
    }

    @Test
    public void testCreateImportErrorDetailsShouldReturnCorrectDataWhenCalled() {
        // given
        ConversionRegistrationDetails conversion = createConversionRegistrationDetails(
                CONVERSION_TIME, TRANSACTION_ID, RESULT_ID, CUSTOMER_TYPE,
                PRODUCT_CATEGORY_ID, PRODUCT_ID, PRODUCT_QUANTITY, PRODUCT_UNIT_PRICE,
                DISCOUNT, APPROVED, CLICK_ID);

        // when
        ConversionImportErrorDetails actual = underTest.createImportErrorDetails(
                conversion, ERROR_MESSAGE);

        // then
        assertFields(actual, CONVERSION_TIME_LOCAL, TRANSACTION_ID, RESULT_ID,
                CUSTOMER_TYPE, PRODUCT_CATEGORY_ID, PRODUCT_ID, PRODUCT_QUANTITY,
                PRODUCT_UNIT_PRICE, DISCOUNT, APPROVED, CLICK_ID, ERROR_MESSAGE);
    }

    @Test
    public void testMergeResultShouldReturnCorrectDataWhenCalled() {
        // given
        ConversionUpdateErrorDetails errorDetails1 = mock(
                ConversionUpdateErrorDetails.class);
        ConversionUpdateRequest request1 = mock(ConversionUpdateRequest.class);
        ConversionImportResult result1 = createConversionImportResult(1, 2,
                Arrays.asList(errorDetails1), Arrays.asList(request1), 3);

        ConversionUpdateErrorDetails errorDetails2 = mock(
                ConversionUpdateErrorDetails.class);
        ConversionUpdateRequest request2 = mock(ConversionUpdateRequest.class);
        ConversionImportResult result2 = createConversionImportResult(4, 5,
                Arrays.asList(errorDetails2), Arrays.asList(request2), 6);

        List<ConversionImportResult> results = Arrays.asList(result1, result2);

        // when
        ConversionImportResult actual = underTest.mergeResult(results);

        // then
        assertNotNull(actual);
        assertEquals(5, actual.getSuccessfulConversionCount());
        assertEquals(7, actual.getFailedConversionCount());
        List<ConversionUpdateErrorDetails> actualErrorDetails = actual.getErrorDetails();
        assertNotNull(actualErrorDetails);
        assertEquals(2, actualErrorDetails.size());
        assertSame(errorDetails1, actualErrorDetails.get(0));
        assertSame(errorDetails2, actualErrorDetails.get(1));
        List<ConversionUpdateRequest> actualErrorRequests = actual.getUpdateRequest();
        assertNotNull(actualErrorRequests);
        assertEquals(2, actualErrorRequests.size());
        assertSame(request1, actualErrorRequests.get(0));
        assertSame(request2, actualErrorRequests.get(1));
        assertEquals(9, actual.getTotalConversionCount());
    }

    @Test
    public void testGetConfirmationTimeFromShouldReturnNullWhenConversionStatusIsPending() {
        // when
        LocalDateTime actual = underTest.getConfirmationTimeFrom(CONFIRMATION_DATE,
                PENDING);

        // then
        assertNull(actual);
    }

    @Test
    public void testGetConfirmationTimeFromShouldReturnCorrectConfirmationTimeWhenConversionStatusIsNotPending() {
        // when
        LocalDateTime actual = underTest.getConfirmationTimeFrom(CONFIRMATION_DATE,
                APPROVED);

        // then
        assertEquals(CONFIRMATION_TIME, actual);
    }

    @Test
    public void testCreateDateFromShouldReturnCorrectDateTimeWhenCalled() {
        //given

        ZonedDateTime convertedByTimezone = ZonedDateTime.of(2020, 1, 20, 23, 33,
                44, 0, LOCAL_ZONE_ID);
        when(dateUtils.convertByTimeZone(CLICK_TIME, ZONE_ID))
                .thenReturn(convertedByTimezone);

        // when
        LocalDateTime actual = underTest.createDateFrom(LOCAL_ZONE_ID,
                CLICK_TIME);

        // then
        assertEquals(EXPECTED_CLICK_TIME, actual);
    }

    private ConversionImportDetails createConversionImportDetails(
            long campaignIdFromClick, LocalDate confirmationDate,
            List<ConversionRegistrationDetails> conversions) {
        return new ConversionImportDetails(CAMPAIGN_ID, campaignIdFromClick,
                CAMPAIGN_NAME, confirmationDate, conversions);
    }

    private ConversionRegistrationDetails createConversionRegistrationDetails(
            ZonedDateTime conversionTime, String transactionId, int resultId,
            String customerType, String productCategoryId, String productId,
            int productQuantity, BigDecimal productUnitPrice, BigDecimal discount,
            ConversionStatus status, String clickId) {
        return new ConversionRegistrationDetails(conversionTime, transactionId, resultId,
                customerType, productCategoryId, productId, productQuantity,
                productUnitPrice, discount, status, clickId);
    }

    private ConversionImportResult createConversionImportResult(
            int successfulConversionCount, int failedConversionCount,
            List<ConversionUpdateErrorDetails> errorDetails,
            List<ConversionUpdateRequest> updateRequest, int totalConversionCount) {
        return new ConversionImportResult(successfulConversionCount,
                failedConversionCount, errorDetails, updateRequest, totalConversionCount);
    }

    private void assertFields(ConversionImportErrorDetails actual,
            LocalDateTime expectedConversionTime, String expectedTransactionId,
            int expectedResultId, String expectedCustomerType,
            String expectedProductCategoryId, String expectedProductId,
            int expectedProductQuantity, BigDecimal expectedProductUnitPrice,
            BigDecimal expectedDiscount, ConversionStatus expectedStatus,
            String expectedClickId, String expectedExpectedErrorMessage) {
        assertNotNull(actual);
        assertEquals(expectedConversionTime, actual.getConversionTime());
        assertEquals(expectedTransactionId, actual.getTransactionId());
        assertEquals(expectedResultId, actual.getResultId());
        assertEquals(expectedCustomerType, actual.getCustomerType());
        assertEquals(expectedProductCategoryId, actual.getProductCategoryId());
        assertEquals(expectedProductId, actual.getProductId());
        assertEquals(expectedProductQuantity, actual.getProductQuantity());
        assertEquals(expectedProductUnitPrice, actual.getProductUnitPrice());
        assertEquals(expectedDiscount, actual.getDiscount());
        assertEquals(expectedStatus, actual.getStatus());
        assertEquals(expectedClickId, actual.getClickId());
        assertEquals(expectedExpectedErrorMessage, actual.getErrorMessage());
    }

    private void assertFields(ConversionInsertRequest actual, long expectedConversionId,
            long expectedCreativeId, long expectedCampaignId,
            LocalDateTime expectedClickTime, LocalDateTime expectedConversionTime,
            LocalDateTime expectedConfirmationTime, String expectedTransactionId,
            String expectedInternalTransactionId, long expectedSiteId, int expectedRank,
            String expectedVerificationId, int expectedResultId, String expectedProductId,
            ConversionStatus expectedStatus, int expectedProductQuantity,
            BigDecimal expectedProductUnitPrice, BigDecimal expectedTransactionAmount,
            RewardType expectedRewardType, CommissionType expectedCommissionType,
            DeviceType expectedDeviceType, String expectedProductCategoryId,
            BigDecimal expectedDiscountAmount, String expectedCreator,
            ZonedDateTime expectedCreationTime, PostbackStatus expectedPostbackStatus,
            String expectedCustomerType, String expectedClickIp, String expectedLanguage,
            String expectedUuid) {
        assertNotNull(actual);
        assertEquals(expectedConversionId, actual.getConversionId());
        assertEquals(expectedCreativeId, actual.getCreativeId());
        assertEquals(expectedCampaignId, actual.getCampaignId());
        assertEquals(expectedClickTime, actual.getClickTime());
        assertEquals(expectedConversionTime, actual.getConversionTime());
        assertEquals(expectedConfirmationTime, actual.getConfirmationTime());
        assertEquals(expectedTransactionId, actual.getTransactionId());
        assertEquals(expectedInternalTransactionId, actual.getInternalTransactionId());
        assertEquals(expectedSiteId, actual.getSiteId());
        assertEquals(expectedRank, actual.getRank());
        assertEquals(expectedVerificationId, actual.getVerificationId());
        assertEquals(expectedResultId, actual.getResultId());
        assertEquals(expectedProductId, actual.getProductId());
        assertEquals(expectedStatus, actual.getStatus());
        assertEquals(expectedProductQuantity, actual.getProductQuantity());
        assertEquals(expectedProductUnitPrice, actual.getProductUnitPrice());
        assertEquals(expectedTransactionAmount, actual.getTransactionAmount());
        assertEquals(expectedRewardType, actual.getRewardType());
        assertEquals(expectedCommissionType, actual.getCommissionType());
        assertEquals(expectedDeviceType, actual.getDeviceType());
        assertEquals(expectedProductCategoryId, actual.getProductCategoryId());
        assertEquals(expectedDiscountAmount, actual.getDiscountAmount());
        assertEquals(expectedCreator, actual.getCreator());
        assertEquals(expectedCreationTime, actual.getCreationTime());
        assertEquals(expectedPostbackStatus, actual.getPostbackStatus());
        assertEquals(expectedCustomerType, actual.getCustomerType());
        assertEquals(expectedClickIp, actual.getClickIp());
        assertEquals(expectedLanguage, actual.getLanguage());
        assertEquals(expectedUuid, actual.getUuid());
    }
}
