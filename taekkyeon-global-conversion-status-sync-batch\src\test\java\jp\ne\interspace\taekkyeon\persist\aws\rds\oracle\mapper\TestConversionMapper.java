/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEFAULT_ORACLE_FETCH_SIZE;

/**
 * MyBatis mapper for the {@code SALES_LOG} table for test.
 *
 * <AUTHOR> Mayur
 */
public interface TestConversionMapper {

    /**
        SELECT
            seq_no AS conversionId,
            confirmed_date AS confirmationTime,
            sales_log_status AS status,
            transaction_id AS transactionId,
            merchant_campaign_no AS campaignId,
            updated_by AS updatedBy,
            1 as shouldSyncStatus
        FROM
            sales_log
        WHERE
            updated_by = 'GLOBAL_CONVERSION_STATUS_SYNC_BATCH'
        ORDER BY
            seq_no;
     */
    @Multiline String SELECT_ALL_SYNCED_CONVERSION = "";

    /**
     * Returns the conversions for synchronized data.
     *
     * @return the conversions for synchronized data
     */
    @Select(SELECT_ALL_SYNCED_CONVERSION)
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "status", javaType = ConversionStatus.class),
            @Arg(column = "transactionId", javaType = String.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "updatedBy", javaType = String.class),
            @Arg(column = "shouldSyncStatus", javaType = boolean.class) })
    List<Conversion> findAllSyncedConversions();

    /**
        SELECT
            redshift_sync_required
        FROM
            sales_log
        WHERE
            seq_no = #{conversionId}
     */
    @Multiline String SELECT_REDSHIFT_SYNC_REQUIRED_BY_CONVERSION_ID = "";

    /**
     * Returns the redshift_sync_required flag for a specific conversion.
     *
     * @param conversionId the ID of the conversion
     * @return Integer value of redshift_sync_required (1 or 0)
     */
    @Select(SELECT_REDSHIFT_SYNC_REQUIRED_BY_CONVERSION_ID)
    Integer getRedshiftSyncRequiredByConversionId(@Param("conversionId") long conversionId);
}
