/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.inject.Inject;

import jp.ne.interspace.taekkyeon.model.ApprovedRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.OccurredRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelAccumulatedRewardResult;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogMapper;

/**
 * Service for gathering reward funnel details with time range conditions.
 *
 * <AUTHOR> Tran
 */
public class PublisherFunnelTrendAccumulatedRewardService {

    @Inject
    private ConversionLogMapper conversionsMapper;

    /**
    * Collects data for publisher funnel details by account IDs and time range.
    *
    * @param siteIdsByLatestTime Map of latest time to list of site IDs.
    * @param targetTimeEnd Target time end.
    * @return PublisherFunnelAccumulatedRewardResult containing approved and occurred rewards.
    */
    public PublisherFunnelAccumulatedRewardResult collectDataForAccountIdsWithTimeRange(
            Map<LocalDateTime, List<Long>> siteIdsByLatestTime,
            LocalDateTime targetTimeEnd) {

        Map<Long, ApprovedRewardFunnelDetails> allApprovedRewards = new HashMap<>();
        Map<Long, OccurredRewardFunnelDetails> allOccurredRewards = new HashMap<>();

        for (Map.Entry<LocalDateTime, List<Long>> entry : siteIdsByLatestTime.entrySet()) {
            LocalDateTime targetTimeFrom = entry.getKey();

            Map<Long, ApprovedRewardFunnelDetails> timeRangeApprovedRewards =
                    conversionsMapper.findApprovedRewardFunnelDetailsWithTimeRange(
                            entry.getValue(), targetTimeFrom, targetTimeEnd);
            Map<Long, OccurredRewardFunnelDetails> timeRangeOccurredRewards =
                    conversionsMapper.findOccurredRewardFunnelDetailsWithTimeRange(
                            entry.getValue(), targetTimeFrom, targetTimeEnd);

            allApprovedRewards.putAll(timeRangeApprovedRewards);
            allOccurredRewards.putAll(timeRangeOccurredRewards);
        }

        return new PublisherFunnelAccumulatedRewardResult(allApprovedRewards, allOccurredRewards);
    }
}
