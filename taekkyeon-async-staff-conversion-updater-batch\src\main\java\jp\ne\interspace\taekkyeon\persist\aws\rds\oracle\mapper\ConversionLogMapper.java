/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionBonusRegistrationDetails;
import jp.ne.interspace.taekkyeon.model.ConversionDetails;
import jp.ne.interspace.taekkyeon.model.ConversionInsertRequest;
import jp.ne.interspace.taekkyeon.model.ConversionRank;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.UpdateConversionByIdDetails;
import jp.ne.interspace.taekkyeon.model.UpdateConversionByIdRequest;
import jp.ne.interspace.taekkyeon.model.UpdateConversionRankDetails;
import jp.ne.interspace.taekkyeon.model.UpdateConversionRewardEditDateDetails;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for handling conversion log database operations.
 *
 * <AUTHOR>
 */
public interface ConversionLogMapper {

    String START_TAG_SCRIPT = "<script>";
    String END_TAG_SCRIPT = "</script>";
    /**
        SELECT
            seq_no
        FROM
            sales_log
        WHERE
            verify = #{transactionId}
        AND
            merchant_campaign_no = #{campaignId}
     */
    @Multiline String SELECT_CONVERSION_IDS_BY_VERIFY_AND_CAMPAIGN_ID = "";

    /**
        SELECT
            seq_no conversionId,
            confirmed_date confirmationTime,
            sales_log_status status,
            verify transactionId,
            merchant_campaign_no campaignId,
            sales_date conversionTime
        FROM
            sales_log
     */
    @Multiline String SELECT_CONVERSION = "";

    /**
        SELECT
            sl.seq_no conversionId,
            sl.merchant_campaign_no campaignId,
            sl.confirmed_date confirmationDate,
            sl.result_id resultId,
            sl.sales_count salesCount,
            ma.country_code countryCode
        FROM
            sales_log sl
        INNER JOIN
            merchant_campaign mc
        ON
            mc.campaign_no = sl.merchant_campaign_no
        INNER JOIN
            merchant_account ma
        ON
            ma.account_no = mc.account_no
        WHERE
            sl.seq_no IN
            <foreach item="item" index="index" collection="conversionIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        ORDER BY
           sl.seq_no
     */
    @Multiline String SELECT_CONVERSIONS_BY_IDS = "";

    /**
        UPDATE
            sales_log
        SET
            sales_log_status = #{status},
            <choose>
                 <when test = "status.name() != 'PENDING'">
                     confirmed_date = #{confirmationDate},
                 </when>
                 <otherwise>
                     confirmed_date = null,
                 </otherwise>
             </choose>
            redshift_sync_required = 1,
            updated_by = #{updater},
            updated_on = SYSDATE
     */
    @Multiline String UPDATE_CONVERSION_STATUS = "";

    /**
        WHERE
            seq_no = #{conversionId}
     */
    @Multiline String WHERE_CONVERSION_ID = "";

    /**
        INSERT INTO
            sales_log (
                seq_no,
                banner_id,
                merchant_campaign_no,
                click_date,
                sales_date,
                log_date,
                confirmed_date,
                transaction_id,
                internal_transaction_id,
                partner_site_no,
                rank,
                verify,
                result_id,
                goods_id,
                sales_log_status,
                sales_count,
                price,
                total_price,
                reward_type,
                sales_reward,
                total_price_reward,
                commission_type,
                at_commission,
                agent_commission,
                default_sales_count,
                default_price,
                default_result_id,
                device_type,
                pb_id_duplicative_flag,
                category_id,
                discount,
                created_by,
                created_on,
                postback_status,
                customer_type,
                click_ip,
                language,
                uuid,
                new_flag)
        VALUES (
                sales_log_seq.nextval,
                #{creativeId},
                #{campaignId},
                #{clickTime},
                #{conversionTime},
                #{conversionTime},
                #{confirmationTime, jdbcType=DATE,
                    typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.LocalDateTimeTypeHandler},
                #{transactionId},
                #{internalTransactionId},
                #{siteId},
                #{rank},
                #{verificationId},
                #{resultId},
                #{productId, jdbcType=VARCHAR},
                #{status},
                #{productQuantity},
                #{productUnitPrice},
                #{transactionAmount},
                #{rewardType},
                0,
                0,
                #{commissionType},
                0,
                0,
                #{productQuantity},
                #{productUnitPrice},
                #{resultId},
                #{deviceType},
                0,
                #{productCategoryId, jdbcType=VARCHAR},
                #{discountAmount},
                #{creator},
                #{creationTime},
                #{postbackStatus},
                #{customerType, jdbcType=VARCHAR},
                #{clickIp, jdbcType=VARCHAR},
                #{language, jdbcType=VARCHAR},
                #{uuid, jdbcType=VARCHAR},
                #{newFlag})
     */
    @Multiline String INSERT = "";

    /**
        UPDATE
            sales_log
        SET
            reward_edit_date = NULL,
            updated_by = #{updater},
            updated_on = SYSDATE
        WHERE
            merchant_campaign_no = #{updateDetail.campaignId}
        AND
            result_id = #{updateDetail.resultId}
        AND
            sales_date BETWEEN #{updateDetail.from} AND #{updateDetail.to}
        AND
            (sales_log_status != 1
            OR
                (
                    TRUNC(confirmed_date, 'MM') > #{targetMonth, jdbcType=DATE,
                typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.YearMonthTypeHandler}
                    <if test = "maxClosedTo != null">
                        AND confirmed_date > #{maxClosedTo}
                    </if>
                )
            )
        AND
            reward_edit_date IS NOT NULL
        AND
            rank = #{updateDetail.rank}
        <if test = "updateDetail.productIds != null
                        and !updateDetail.productIds.isEmpty()">
            AND goods_id IN
            <foreach item="item" index="index" collection="updateDetail.productIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test = "updateDetail.categoryIds != null
                        and !updateDetail.categoryIds.isEmpty()">
            AND category_id IN
            <foreach item="item" index="index" collection="updateDetail.categoryIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    */
    @Multiline String UPDATE_REWARD_EDIT_DATE = "";

    /**
              UPDATE sales_log
              <set>
                  <if test = "updateDetails.transactionAmount != null">
                      price = #{unitPrice},
                      total_price = #{updateDetails.transactionAmount},
                  </if>
                  <if test = "updateDetails.resultId != null">
                      result_id = #{updateDetails.resultId},
                  </if>
                  <if test = "updateDetails.categoryId != null">
                      category_id = #{updateDetails.categoryId},
                  </if>
                  <if test = "updateDetails.productId != null">
                      goods_id = #{updateDetails.productId},
                  </if>
                  <if test = "updateDetails.customerType != null">
                      <if test = "updateDetails.customerType != 'empty'">
                          customer_type = #{updateDetails.customerType},
                      </if>
                      <if test = "updateDetails.customerType == 'empty'">
                          customer_type = null,
                      </if>
                  </if>
                  <if test = "updateDetails.bonusSettingId != null">
                      bonus_setting_id = #{updateDetails.bonusSettingId},
                  </if>
                  <if test = "updateDetails.publisherBonus != null">
                      publisher_bonus = #{updateDetails.publisherBonus},
                  </if>
                  <if test = "updateDetails.publisherAgentBonus != null">
                      publisher_agent_bonus = #{updateDetails.publisherAgentBonus},
                  </if>
                  <if test = "updateDetails.merchantAgentBonus != null">
                      merchant_agent_bonus = #{updateDetails.merchantAgentBonus},
                  </if>
                  <if test = "updateDetails.atBonus != null">
                      at_bonus = #{updateDetails.atBonus},
                  </if>
                  <if test = "updateDetails.publisherBonusInUsd > 0">
                      publisher_bonus_in_usd = #{updateDetails.publisherBonusInUsd},
                  </if>
                  <if test = "updateDetails.publisherAgentBonusInUsd > 0">
                      publisher_agent_bonus_in_usd = #{updateDetails.publisherAgentBonusInUsd},
                  </if>
                  <if test = "updateDetails.merchantAgentBonusInUsd > 0">
                      merchant_agent_bonus_in_usd = #{updateDetails.merchantAgentBonusInUsd},
                  </if>
                  <if test = "updateDetails.atBonusInUsd > 0">
                      at_bonus_in_usd = #{updateDetails.atBonusInUsd},
                  </if>
                  reward_edit_date = null,
                  updated_on = #{latestUpdateTime},
                  updated_by = #{updater}
              </set>
              WHERE seq_no = #{updateDetails.conversionId}
    */
    @Multiline String UPDATE_CONVERSION_BY_ID = "";

    /**
        UPDATE sales_log
        SET
            postback_status = 0,
            updated_on = #{latestUpdateTime},
            updated_by = #{updater}
        WHERE
             seq_no IN
             <foreach item="item" index="index" collection="conversionIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
    */
    @Multiline String UPDATE_POSTBACK_STATUS = "";

    /**
        SELECT
            sl.seq_no
        FROM sales_log sl
        INNER JOIN
            partner_site ps
        ON
            ps.site_no = sl.partner_site_no
        WHERE
            sl.seq_no IN
            <foreach item="item" index="index" collection="conversionIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        AND
            ps.all_banners_flg = 1
        AND EXISTS (
            SELECT 1
            FROM postback_url postback
            WHERE
                ps.site_no = postback.partner_site_no
            AND (
                postback.banner_id = sl.banner_id
                OR (
                    postback.banner_id = 0
                    AND
                    ps.site_no = sl.partner_site_no)
            ) AND
                approve_flag = 1
        )
        AND sl.sales_log_status != 0
        ORDER BY
            sl.seq_no
    */
    @Multiline String SELECT_POSTBACKABLE_CONVERSION_IDS = "";

    /**
        SELECT
            seq_no as id,
            rank as rank
        FROM
            sales_log
        WHERE
            sales_date BETWEEN #{updateDetails.from} AND #{updateDetails.to}
          AND
            partner_site_no = #{updateDetails.siteId}
          AND
            merchant_campaign_no = #{updateDetails.campaignId}
          AND
            (sales_log_status != 1
            OR
                (
                    TRUNC(confirmed_date, 'MM') > #{targetMonth, jdbcType=DATE,
                typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.YearMonthTypeHandler}
                    <if test = "maxClosedTo != null">
                        AND confirmed_date > #{maxClosedTo}
                    </if>
                )
            )
     */
    @Multiline String SELECT_CONVERSION_IDS_AND_RANKS = "";

    /**
         UPDATE sales_log
         SET
             rank = #{updateDetails.rank},
             reward_edit_date = null,
             updated_by = #{updater},
             updated_on = #{latestUpdateTime}
         WHERE
             sales_date BETWEEN #{updateDetails.from} AND #{updateDetails.to}
         AND
             partner_site_no = #{updateDetails.siteId}
         AND
             merchant_campaign_no = #{updateDetails.campaignId}
         AND
            (sales_log_status != 1
            OR
                (
                    TRUNC(confirmed_date, 'MM') > #{targetMonth, jdbcType=DATE,
                typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.YearMonthTypeHandler}
                    <if test = "maxClosedTo != null">
                        AND confirmed_date > #{maxClosedTo}
                    </if>
                )
            )
     */
    @Multiline String UPDATE_RANK_AND_REWARD_EDIT_DATE_NULL = "";

    /**
        UPDATE sales_log
        SET
            bonus_setting_id = #{updateDetails.bonusSettingId},
            publisher_bonus = #{updateDetails.publisherBonus},
            publisher_agent_bonus = #{updateDetails.publisherAgentBonus},
            merchant_agent_bonus = #{updateDetails.merchantAgentBonus},
            at_bonus = #{updateDetails.atBonus},
            publisher_bonus_in_usd = #{updateDetails.publisherBonusInUsd},
            publisher_agent_bonus_in_usd = #{updateDetails.publisherAgentBonusInUsd},
            merchant_agent_bonus_in_usd = #{updateDetails.merchantAgentBonusInUsd},
            at_bonus_in_usd = #{updateDetails.atBonusInUsd},
            bonus_created_by = #{updater},
            bonus_created_on = #{latestUpdateTime},
            redshift_sync_required = 1,
            updated_by = #{updater},
            updated_on = #{latestUpdateTime}
        WHERE
            seq_no = #{updateDetails.conversionId}
     */
    @Multiline String UPDATE_CONVERSION_BONUS = "";

    /**
     * Returns a conversion by the ID of given conversion.
     *
     * @param conversionId
     *            the ID of given conversion
     * @return a conversion by the ID of given conversion
     * @see #SELECT_CONVERSION
     */
    @Select(SELECT_CONVERSION + WHERE_CONVERSION_ID)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "status", javaType = ConversionStatus.class),
            @Arg(column = "transactionId", javaType = String.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "conversionTime", javaType = LocalDateTime.class) })
    Conversion findConversionBy(long conversionId);

    /**
     * Returns the conversion IDs by the verify of given conversion and the ID of given
     * campaign.
     *
     * @param transactionId
     *            the verify of given conversion
     * @param campaignId
     *            the ID of given campaign
     * @return the conversion IDs by the verify of given conversion and the ID of given
     *         campaign
     * @see #SELECT_CONVERSION_IDS_BY_VERIFY_AND_CAMPAIGN_ID
     */
    @Select(SELECT_CONVERSION_IDS_BY_VERIFY_AND_CAMPAIGN_ID)
    Set<Long> findConversionIds(@Param("transactionId") String transactionId,
            @Param("campaignId") long campaignId);

    /**
     * Updates the status, confirmation date and updater by the ID of given conversion.
     *
     * @param conversionId
     *            the Id of given conversion
     * @param status
     *            the status of given conversion
     * @param confirmationDate
     *            the confirmation date of given conversion
     * @param updater
     *            the updater of given conversion
     * @return update row count
     * @see #UPDATE_CONVERSION_STATUS
     */
    @Update(START_TAG_SCRIPT + UPDATE_CONVERSION_STATUS + WHERE_CONVERSION_ID
            + END_TAG_SCRIPT)
    int updateStatus(@Param("conversionId") long conversionId,
            @Param("status") ConversionStatus status,
            @Param("confirmationDate") LocalDateTime confirmationDate,
            @Param("updater") String updater);

    /**
     * Inserts a conversion by {@link ConversionInsertRequest}.
     *
     * @param insertRequest
     *            {@link ConversionInsertRequest} containing the conversion to be inserted
     * @return inserted conversion ID
     * @see #INSERT
     */
    @Insert(INSERT)
    @Options(useGeneratedKeys = true, keyProperty = "conversionId", keyColumn = "SEQ_NO")
    int insert(ConversionInsertRequest insertRequest);

    /**
     * Update conversions by {@link UpdateConversionRewardEditDateDetails}.
     *
     * @param updateDetail
     *            {@link UpdateConversionRewardEditDateDetails} containing the conversion to be updated
     * @param updater
     *            the updater of given conversion
     * @param targetMonth
     *            the given closed month
     * @param maxClosedTo
     *            the given campaign closure max closed to
     * @return count updated conversions
     * @see #UPDATE_REWARD_EDIT_DATE
     */
    @Update(START_TAG_SCRIPT + UPDATE_REWARD_EDIT_DATE + END_TAG_SCRIPT)
    int updateRewardEditDateToNullBy(
            @Param("updateDetail") UpdateConversionRewardEditDateDetails updateDetail,
            @Param("updater") String updater, @Param("targetMonth") YearMonth targetMonth,
            @Param("maxClosedTo") LocalDateTime maxClosedTo);

    /**
     * Update conversions by {@link UpdateConversionByIdRequest}.
     *
     * @param updateDetails
     *            {@link UpdateConversionByIdDetails} containing the conversion to be updated
     * @param updater
     *            the updater of given conversion
     * @param latestUpdateTime
     *            the updated time
     * @param unitPrice
     *            the unit price
     * @return count updated conversions
     * @see #UPDATE_CONVERSION_BY_ID
     */
    @Update(START_TAG_SCRIPT + UPDATE_CONVERSION_BY_ID + END_TAG_SCRIPT)
    int updateConversionBy(@Param("updateDetails") UpdateConversionByIdDetails updateDetails,
            @Param("updater") String updater,
            @Param("latestUpdateTime") ZonedDateTime latestUpdateTime,
            @Param("unitPrice") BigDecimal unitPrice);

    /**
     * Update conversions postback status by id.
     *
     * @param conversionIds
     *            containing the conversion to be updated
     * @param updater
     *            the updater of given conversion
     * @param latestUpdateTime
     *            the updated time
     * @return count updated conversions
     * @see #UPDATE_POSTBACK_STATUS
     */
    @Update(START_TAG_SCRIPT + UPDATE_POSTBACK_STATUS + END_TAG_SCRIPT)
    int updatePostbackStatus(@Param("conversionIds") List<Long> conversionIds,
            @Param("updater") String updater,
            @Param("latestUpdateTime") ZonedDateTime latestUpdateTime);

    /**
     * Find postbackable conversion ids by conversionIds.
     *
     * @param conversionIds
     *            containing the conversion to be found
     * @return conversion ID
     * @see #SELECT_POSTBACKABLE_CONVERSION_IDS
     */
    @Select(START_TAG_SCRIPT + SELECT_POSTBACKABLE_CONVERSION_IDS + END_TAG_SCRIPT)
    List<Long> findPostbackableConversionIds(
            @Param("conversionIds") List<Long> conversionIds);

    /**
     * Returns ids and ranks by the conversion rank details.
     *
     * @param updateDetails
     *            the details of given conversion
     * @param targetMonth
     *            the given closed month
     * @param maxClosedTo
     *            the given campaign closure max closed to
     * @return ids and ranks by the conversion rank details.
     * @see #SELECT_CONVERSION_IDS_AND_RANKS
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSION_IDS_AND_RANKS + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = long.class),
            @Arg(column = "rank", javaType = int.class) })
    List<ConversionRank> findIdsAndRanksBy(
            @Param("updateDetails") UpdateConversionRankDetails updateDetails,
            @Param("targetMonth") YearMonth targetMonth,
            @Param("maxClosedTo") LocalDateTime maxClosedTo);

    /**
     * Update rank and reward edit date null by conversion details.
     *
     * @param updateDetails
     *            containing the conversion to be updated
     * @param updater
     *            the updater of given conversion
     * @param latestUpdateTime
     *            the updated time
     * @param targetMonth
     *            the given closed month
     * @param maxClosedTo
     *            the given campaign closure max closed to
     * @return count updated conversions
     * @see #UPDATE_RANK_AND_REWARD_EDIT_DATE_NULL
     */
    @Update(START_TAG_SCRIPT + UPDATE_RANK_AND_REWARD_EDIT_DATE_NULL + END_TAG_SCRIPT)
    int updateRankAndRewardEditDateNullBy(
            @Param("updateDetails") UpdateConversionRankDetails updateDetails,
            @Param("updater") String updater,
            @Param("latestUpdateTime") ZonedDateTime latestUpdateTime,
            @Param("targetMonth") YearMonth targetMonth,
            @Param("maxClosedTo") LocalDateTime maxClosedTo);

    /**
     * Update conversion bonus by details.
     *
     * @param updateDetails
     *            containing the conversion bonus to be updated
     * @param updater
     *            the updater of given conversion
     * @param latestUpdateTime
     *            the updated time
     * @return count updated conversions
     * @see #UPDATE_CONVERSION_BONUS
     */
    @Update(UPDATE_CONVERSION_BONUS)
    int updateConversionBonus(
            @Param("updateDetails") ConversionBonusRegistrationDetails updateDetails,
            @Param("updater") String updater,
            @Param("latestUpdateTime") ZonedDateTime latestUpdateTime);

    /**
     * Returns the conversions by the IDs of given conversion.
     *
     * @param conversionIds
     *            the IDs of given conversion
     * @return the conversions by the IDs of given conversion
     * @see #SELECT_CONVERSIONS_BY_IDS
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSIONS_BY_IDS + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = long.class),
            @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "confirmationDate", javaType = LocalDateTime.class),
            @Arg(column = "resultId", javaType = Integer.class),
            @Arg(column = "salesCount", javaType = long.class),
            @Arg(column = "countryCode", javaType = String.class) })
    List<ConversionDetails> findConversionsByIds(
            @Param("conversionIds") List<Long> conversionIds);
}
