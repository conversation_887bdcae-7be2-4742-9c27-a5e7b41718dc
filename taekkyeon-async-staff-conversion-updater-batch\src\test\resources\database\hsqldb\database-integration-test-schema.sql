SET DATABASE SQL SYNTAX ORA TRUE;

DROP SEQUENCE IF EXISTS SALES_LOG_SEQ;
CREATE SEQUENCE  "SALES_LOG_SEQ" AS BIGINT INCREMENT BY 1 START WITH 1 ;

DROP TABLE IF EXISTS SALES_LOG;
CREATE TABLE SALES_LOG (
    "SEQ_NO" NUMBER(10,0) NOT NULL,
    "BANNER_ID" NUMBER(10,0),
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "CLICK_DATE" DATE,
    "SALES_DATE" DATE,
    "LOG_DATE" DATE,
    "CONFIRMED_DATE" DATE,
    "TRANSACTION_ID" VARCHAR2(512),
    "PARTNER_SITE_NO" NUMBER(10,0),
    "RANK" NUMBER(2,0),
    "VERIFY" VARCHAR2(256),
    "RESULT_ID" NUMBER(4,0),
    "GOODS_ID" VARCHAR2(256),
    "SALES_LOG_STATUS" NUMBER(2,0),
    "SALES_COUNT" NUMBER(10,0),
    "PRICE" NUMBER(12,2),
    "TOTAL_PRICE" NUMBER(12,2),
    "REWARD_TYPE" NUMBER(1,0),
    "SALES_REWARD" NUMBER(12,2),
    "TOTAL_PRICE_REWARD" NUMBER(12,2),
    "COMMISSION_TYPE" NUMBER(1,0),
    "AT_COMMISSION" NUMBER(12,2),
    "AGENT_COMMISSION" NUMBER(12,2),
    "P_AGENT_COMMISSION" NUMBER(12,2),
    "IP" VARCHAR2(256),
    "MEDIA_URL" VARCHAR2(512),
    "REFERER" VARCHAR2(2048),
    "REPEAT_COUNT" NUMBER(10,0),
    "USER_AGENT" VARCHAR2(512),
    "REWARD_EDIT_DATE" DATE,
    "DEFAULT_SALES_COUNT" NUMBER(10,0),
    "DEFAULT_PRICE" NUMBER(12,2),
    "DEFAULT_RESULT_ID" NUMBER(4,0),
    "LP_URL" VARCHAR2(512),
    "DEVICE_TYPE" NUMBER(2,0),
    "POINTBACK_ID" VARCHAR2(64),
    "PB_ID_DUPLICATIVE_FLAG" NUMBER(1,0),
    "PB_ID_OLDEST_SALES_DATE" DATE,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "NEW_FLAG" NUMBER(1,0) DEFAULT 0,
    "SESSION_ID" VARCHAR2(256),
    "CURRENCY_ID" NUMBER(4,0),
    "ORIGIN_PRICE" NUMBER(12,2),
    "ORIGIN_TOTAL_PRICE" NUMBER(12,2),
    "UUID" VARCHAR2(80),
    "DEVICE_OS" NUMBER(2,0) DEFAULT 0,
    "CATEGORY_ID" VARCHAR2(256),
    "DISCOUNT" NUMBER(12,2) DEFAULT 0,
    "INTERNAL_TRANSACTION_ID" VARCHAR2(512),
    "ORIGINAL_CURRENCY_TOTAL_PRICE" NUMBER(12,2),
    "ORIGINAL_CURRENCY" VARCHAR2(3),
    "CLICK_REFERER" VARCHAR2(2048),
    "CLICK_URL" VARCHAR2(2048),
    "CLICK_USER_AGENT" VARCHAR2(2048),
    "POSTBACK_STATUS" NUMBER(1,0) DEFAULT 1,
    "POSTBACK_ERROR_COUNT" NUMBER(1,0) DEFAULT 0,
    "LATEST_POSTBACK_TIME" DATE,
    "CUSTOMER_TYPE" VARCHAR2(64),
    "POSTBACK_URL" VARCHAR2(2048),
    "POSTBACK_ERROR_DETAILS" VARCHAR2(2048),
    "POSTBACK_ERROR_RESPONSE_CODE" NUMBER(3,0),
    "CLICK_IP" VARCHAR2(256),
    "LANGUAGE" VARCHAR2(40),
    "BONUS_SETTING_ID" NUMBER(12,2), 
    "PUBLISHER_BONUS" NUMBER(12,2), 
    "PUBLISHER_AGENT_BONUS" NUMBER(12,2), 
    "MERCHANT_AGENT_BONUS" NUMBER(12,2), 
    "AT_BONUS" NUMBER(12,2), 
    "PUBLISHER_BONUS_IN_USD" NUMBER(12,2), 
    "PUBLISHER_AGENT_BONUS_IN_USD" NUMBER(12,2),
    "MERCHANT_AGENT_BONUS_IN_USD" NUMBER(12,2),
    "AT_BONUS_IN_USD" NUMBER(12,2),
    "BONUS_CREATED_BY" VARCHAR2(256), 
    "BONUS_CREATED_ON" DATE,
    "REDSHIFT_SYNC_REQUIRED" NUMBER(1,0) DEFAULT 0
);

DROP TABLE IF EXISTS STAFF_ACCOUNT;
CREATE TABLE STAFF_ACCOUNT (
    "STAFF_NO" NUMBER(10,0),
    "LASTNAME" VARCHAR2(64),
    "FIRSTNAME" VARCHAR2(64),
    "EMAIL" VARCHAR2(64),
    "PHONE" VARCHAR2(32),
    "LOGIN_NAME" VARCHAR2(64),
    "LOGIN_PASSWORD" VARCHAR2(32),
    "ACCOUNT_STATE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "U_ID" CHAR(32),
    "COUNTRY_CODE" VARCHAR2(2)
);

DROP TABLE IF EXISTS MONTHLY_CLOSING;
CREATE TABLE MONTHLY_CLOSING (
    "CLOSED_MONTH" CHAR(6),
    "TARGET_MONTH" CHAR(6),
    "TEMPORARY_CLOSING_FLAG" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "COUNTRY_CODE" VARCHAR2(2)
);

DROP TABLE IF EXISTS COUNTRY;
CREATE TABLE COUNTRY (
    "CODE" CHAR(2),
    "NAME" VARCHAR2(256),
    "CURRENCY" VARCHAR2(3 BYTE),
    "SUPPORT_EMAIL" VARCHAR2(64),
    "ACCESSTRADE_URL" VARCHAR2(64),
    "PHONE" VARCHAR2(32),
    "MINIMUM_AMOUNT" NUMBER(10,0),
    "ZONE_ID" VARCHAR2(40),
    "IS_TAX_CALCULATION_REQUIRED" NUMBER(1,0),
    "MONTHLY_CLOSURE_REPORT_FLAG" NUMBER(1,0)
);

DROP TABLE IF EXISTS AFFILIATION;
CREATE TABLE AFFILIATION (
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL,
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "AFFILIATION_STATUS" NUMBER(2,0) NOT NULL,
    "REGISTRATION_DATE" DATE,
    "COMPLETION_DATE" DATE,
    "RANK" NUMBER(2,0) NOT NULL,
    "NEXT_RANK" NUMBER(2,0),
    "RANK_EDIT_DATE" DATE,
    "SYNC_STATUS" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256 BYTE),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256 BYTE),
    "UPDATED_ON" DATE,
    "APPROVE_DATE" DATE,
    "REJECT_DATE" DATE,
    "AFFILIATION_STATUS_UPDATER" VARCHAR2(256)
);

DROP TABLE IF EXISTS AFFILIATION_RANK_HISTORY;
CREATE TABLE AFFILIATION_RANK_HISTORY (
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL,
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "TARGET_MONTH" DATE NOT NULL,
    "RANK" NUMBER(3,0) NOT NULL,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN_SETTING;
CREATE TABLE MERCHANT_CAMPAIGN_SETTING (
    "CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "COOKIE_EXPIRATION_DATE_VIEW" NUMBER(10,0) DEFAULT 86400,
    "VERIFY_CUT_FLAG" NUMBER(1,0) DEFAULT 0 NOT NULL,
    "VERIFY_CUT_TARGET" NUMBER(1,0),
    "VERIFY_CUT_CONDITION" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "SELF_CONVERSION_FLAG" NUMBER(1,0),
    "CV_ONLY_ONCE_FLAG" NUMBER DEFAULT 0
);

DROP TABLE IF EXISTS REWARD_CATEGORY_HISTORY_HOURLY;
CREATE TABLE REWARD_CATEGORY_HISTORY_HOURLY (
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "TARGET_MONTH" DATE,
    "TARGET_TIME_FROM" DATE,
    "TARGET_TIME_TO" DATE,
    "RANK" NUMBER(2,0),
    "CATEGORY_ID" VARCHAR2(256),
    "REWARD_TYPE" NUMBER(1,0),
    "CLICK_PRICE" NUMBER(10,2),
    "SALES_PRICE" NUMBER(10,2),
    "SALES_RATIO" NUMBER(10,2),
    "COMMISSION_TYPE" NUMBER(1,0),
    "AT_COMMISSION" NUMBER(10,2),
    "AT_COMMISSION_RATIO" NUMBER(10,2),
    "AGENT_COMMISSION" NUMBER(10,2),
    "AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "P_AGENT_COMMISSION" NUMBER(10,2),
    "P_AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "BUDGET_CAP" NUMBER(10,2),
    "REWARD_EDIT_DATE" DATE,
    "RECORD_NUMBER" NUMBER(10,0),
    "LATEST_FLAG" NUMBER(1,0),
    "TARGET_DEVICE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "CATEGORY_NAME" VARCHAR2(256),
    "CUSTOMER_TYPE" VARCHAR2(64),
    "BUDGET_SCOPE" VARCHAR2(10),
    "MONTHLY_REWARD_OFFSET_FLAG" NUMBER(1,0)
);

DROP TABLE IF EXISTS CLICK_PARAMETERS;
CREATE TABLE CLICK_PARAMETERS  (
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0), 
    "INTERNAL_TRANSACTION_ID" VARCHAR2(512), 
    "PARAM_NAME" VARCHAR2(128), 
    "PARAM_VALUE" VARCHAR2(2048), 
    "CREATED_BY" VARCHAR2(256), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS POSTBACK_URL;
CREATE TABLE POSTBACK_URL (
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL,
    "BANNER_ID" NUMBER(10,0) NOT NULL,
    "POSTBACK_URL" VARCHAR2(1024 BYTE),
    "APPROVE_FLAG" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256 BYTE),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256 BYTE),
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS PARTNER_SITE;
CREATE TABLE PARTNER_SITE (
    "SITE_NO" NUMBER(10,0),
    "ACCOUNT_NO" NUMBER(10,0),
    "SITE_NAME" VARCHAR2(1024) DEFAULT 0,
    "URL" VARCHAR2(1024) DEFAULT 0,
    "DESCRIPTION" VARCHAR2(2000) DEFAULT 0,
    "SITE_TYPE" NUMBER(2,0) DEFAULT 0,
    "SITE_STATE" NUMBER(1,0) DEFAULT 0,
    "CATEGORY_LOW_ID1" NUMBER(10,0),
    "CATEGORY_LOW_ID2" NUMBER(10,0),
    "CATEGORY_LOW_ID3" NUMBER(10,0),
    "MAIN_SITE_FLAG" NUMBER(1,0),
    "POINTBACK_FLAG" NUMBER(1,0),
    "ALL_BANNERS_FLG" NUMBER(1,0) DEFAULT 0,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "FIRST_APPROVED_TIME" DATE,
    "SITE_TRAFFIC" NUMBER(2,0),
    "SITE_LEAD_GENERATION" NUMBER(2,0)
);

DROP TABLE IF EXISTS PARTNER_ACCOUNT;
CREATE TABLE PARTNER_ACCOUNT (
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL,
    "ACCOUNT_TYPE_ID" NUMBER(1,0) DEFAULT 0,
    "PARTNER_TYPE_ID" NUMBER(2,0),
    "CORPORATE_NAME" VARCHAR2(128),
    "SECTION_NAME" VARCHAR2(128),
    "POST_NAME" VARCHAR2(128),
    "LASTNAME" VARCHAR2(64),
    "FIRSTNAME" VARCHAR2(64),
    "EMAIL" VARCHAR2(64),
    "ZIP_CODE" VARCHAR2(8),
    "PREFECTURE" VARCHAR2(128),
    "CITY" VARCHAR2(128),
    "ADDRESS" VARCHAR2(512),
    "ADDRESS2" VARCHAR2(512),
    "PHONE" VARCHAR2(32),
    "BIRTHDAY" DATE,
    "SEX" NUMBER(1,0),
    "COMMERCIAL_REGISTRATION_NUMBER" VARCHAR2(128),
    "VAT_NUMBER" VARCHAR2(128),
    "BANK_ID" VARCHAR2(5),
    "BANK_NAME" VARCHAR2(128),
    "BANK_BRANCH_ID" VARCHAR2(8),
    "BANK_BRANCH_NAME" VARCHAR2(128),
    "BANK_ACCOUNT_TYPE_ID" NUMBER(2,0),
    "BANK_ACCOUNT_NUMBER" VARCHAR2(30),
    "BANK_ACCOUNT_OWNER_LASTNAME" VARCHAR2(128),
    "BANK_ACCOUNT_OWNER_FIRSTNAME" VARCHAR2(128),
    "URL" VARCHAR2(256),
    "LOGIN_NAME" VARCHAR2(64),
    "LOGIN_PASSWORD" VARCHAR2(32),
    "ACCOUNT_STATE" NUMBER(1,0) ,
    "APPLIED_DATE" DATE ,
    "QUIT_DATE" DATE,
    "ORIGIN_NO" NUMBER(10,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "BLACKLIST_FLAG" NUMBER(1,0),
    "AGENCY_ID" NUMBER(10,0) DEFAULT 0 NOT NULL,
    "MEDIA_SOURCE" VARCHAR2(256),
    "U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
    "COUNTRY_CODE" VARCHAR2(2) DEFAULT 'TH' NOT NULL,
    "FIRST_ACTIVATION_TIME" DATE,
    "ONBOARDING_STATUS" NUMBER(2,0) DEFAULT 0,
    "PROFILE_PICTURE_URL" VARCHAR2(1024),
    "MIDDLENAME" VARCHAR2(256)
); 

DROP TABLE IF EXISTS REWARD_CATEGORY_HISTORY;
CREATE TABLE REWARD_CATEGORY_HISTORY (
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "TARGET_MONTH" DATE,
    "RANK" NUMBER(2,0),
    "CATEGORY_ID" VARCHAR2(256),
    "REWARD_TYPE" NUMBER(1,0),
    "CLICK_PRICE" NUMBER(10,2),
    "SALES_PRICE" NUMBER(10,2),
    "SALES_RATIO" NUMBER(10,2),
    "COMMISSION_TYPE" NUMBER(1,0),
    "AT_COMMISSION" NUMBER(10,2),
    "AT_COMMISSION_RATIO" NUMBER(10,2) ,
    "AGENT_COMMISSION" NUMBER(10,2),
    "AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "P_AGENT_COMMISSION" NUMBER(10,2),
    "P_AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "BUDGET_CAP" NUMBER(10,2),
    "REWARD_EDIT_DATE" DATE,
    "RECORD_NUMBER" NUMBER(10,0),
    "LATEST_FLAG" NUMBER(1,0),
    "TARGET_DEVICE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "CATEGORY_NAME" VARCHAR2(256),
    "CUSTOMER_TYPE" VARCHAR2(64),
    "BUDGET_SCOPE" VARCHAR2(10)
);

DROP TABLE IF EXISTS REWARD_GOODS_HISTORY_HOURLY;
CREATE TABLE REWARD_GOODS_HISTORY_HOURLY (
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "TARGET_MONTH" DATE,
    "TARGET_TIME_FROM" DATE,
    "TARGET_TIME_TO" DATE,
    "RANK" NUMBER(2,0),
    "GOODS_ID" VARCHAR2(256),
    "REWARD_TYPE" NUMBER(1,0),
    "CLICK_PRICE" NUMBER(10,2),
    "SALES_PRICE" NUMBER(10,2),
    "SALES_RATIO" NUMBER(10,2),
    "COMMISSION_TYPE" NUMBER(1,0),
    "AT_COMMISSION" NUMBER(10,2),
    "AT_COMMISSION_RATIO" NUMBER(10,2),
    "AGENT_COMMISSION" NUMBER(10,2),
    "AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "P_AGENT_COMMISSION" NUMBER(10,2),
    "P_AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "REWARD_EDIT_DATE" DATE,
    "RECORD_NUMBER" NUMBER(10,0),
    "LATEST_FLAG" NUMBER(1,0),
    "TARGET_DEVICE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "CUSTOMER_TYPE" VARCHAR2(64),
    "MONTHLY_REWARD_OFFSET_FLAG" NUMBER(1,0)
);

DROP TABLE IF EXISTS REWARD_GOODS_HISTORY;
CREATE TABLE REWARD_GOODS_HISTORY (
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "TARGET_MONTH" DATE,
    "RANK" NUMBER(2,0),
    "GOODS_ID" VARCHAR2(256),
    "REWARD_TYPE" NUMBER(1,0),
    "CLICK_PRICE" NUMBER(10,2),
    "SALES_PRICE" NUMBER(10,2),
    "SALES_RATIO" NUMBER(10,2),
    "COMMISSION_TYPE" NUMBER(1,0),
    "AT_COMMISSION" NUMBER(10,2),
    "AT_COMMISSION_RATIO" NUMBER(10,2),
    "AGENT_COMMISSION" NUMBER(10,2),
    "AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "P_AGENT_COMMISSION" NUMBER(10,2),
    "P_AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "REWARD_EDIT_DATE" DATE,
    "RECORD_NUMBER" NUMBER(10,0),
    "LATEST_FLAG" NUMBER(1,0),
    "TARGET_DEVICE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "CUSTOMER_TYPE" VARCHAR2(64)
);

DROP TABLE IF EXISTS REWARD_PRICE_HISTORY_HOURLY;
CREATE TABLE REWARD_PRICE_HISTORY_HOURLY (
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "TARGET_MONTH" DATE,
    "TARGET_TIME_FROM" DATE,
    "TARGET_TIME_TO" DATE,
    "RANK" NUMBER(2,0),
    "RESULT_ID" NUMBER(4,0),
    "REWARD_TYPE" NUMBER(1,0),
    "CLICK_PRICE" NUMBER(10,2),
    "SALES_PRICE" NUMBER(10,2),
    "SALES_RATIO" NUMBER(10,2),
    "COMMISSION_TYPE" NUMBER(1,0),
    "AT_COMMISSION" NUMBER(10,2),
    "AT_COMMISSION_RATIO" NUMBER(10,2),
    "AGENT_COMMISSION" NUMBER(10,2),
    "AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "P_AGENT_COMMISSION" NUMBER(10,2),
    "P_AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "TRANSACTION_BUDGET_CAP" NUMBER(10,2),
    "PRODUCT_CATEGORY_BUDGET_CAP" NUMBER(10,2),
    "REWARD_EDIT_DATE" DATE,
    "RECORD_NUMBER" NUMBER(10,0),
    "LATEST_FLAG" NUMBER(1,0),
    "TARGET_DEVICE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "CUSTOMER_TYPE" VARCHAR2(64),
    "MONTHLY_REWARD_OFFSET_FLAG" NUMBER(1,0)
);

DROP TABLE IF EXISTS REWARD_PRICE_HISTORY;
CREATE TABLE REWARD_PRICE_HISTORY (
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "TARGET_MONTH" DATE,
    "RANK" NUMBER(2,0),
    "RESULT_ID" NUMBER(4,0),
    "REWARD_TYPE" NUMBER(1,0),
    "CLICK_PRICE" NUMBER(10,2),
    "SALES_PRICE" NUMBER(10,2),
    "SALES_RATIO" NUMBER(10,2),
    "COMMISSION_TYPE" NUMBER(1,0),
    "AT_COMMISSION" NUMBER(10,2),
    "AT_COMMISSION_RATIO" NUMBER(10,2),
    "AGENT_COMMISSION" NUMBER(10,2),
    "AGENT_COMMISSION_RATIO" NUMBER(10,2),
    "P_AGENT_COMMISSION" NUMBER(10,2) DEFAULT 0,
    "P_AGENT_COMMISSION_RATIO" NUMBER(10,2) DEFAULT 0,
    "TRANSACTION_BUDGET_CAP" NUMBER(10,2),
    "PRODUCT_CATEGORY_BUDGET_CAP" NUMBER(10,2),
    "REWARD_EDIT_DATE" DATE,
    "RECORD_NUMBER" NUMBER(10,0),
    "LATEST_FLAG" NUMBER(1,0),
    "TARGET_DEVICE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "CUSTOMER_TYPE" VARCHAR2(64)
);

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN;
CREATE TABLE MERCHANT_CAMPAIGN (
    "CAMPAIGN_NO" NUMBER(10,0),
    "ACCOUNT_NO" NUMBER(10,0),
    "CAMPAIGN_STATE_ID" NUMBER(2,0),
    "CREATIVE_SYNC_STATUS" NUMBER(1,0),
    "CAMPAIGN_NAME" VARCHAR2(512),
    "IMAGE_URL" VARCHAR2(512),
    "URL" VARCHAR2(512),
    "DESCRIPTION" VARCHAR2(4000),
    "CATEGORY1" NUMBER(10,0),
    "CATEGORY2" NUMBER(10,0),
    "CATEGORY3" NUMBER(10,0),
    "AUTO_AFF_LIMITATION_OPTION" NUMBER(1,0),
    "AUTO_AFF_LIMITATION_DIVISION" NUMBER(1,0),
    "AFF_CONDITION_SPECIAL" VARCHAR2(2048),
    "RESULT_APPROVAL_SPECIAL" VARCHAR2(2000),
    "PR_FOR_PARTNER" VARCHAR2(4000),
    "DEVICE_TYPE" NUMBER(5,0),
    "GET_PARAMETER_FLAG" NUMBER(1,0),
    "POINTBACK_PERMISSION" NUMBER(1,0),
    "SELF_CONVERSION_FLAG" NUMBER(1,0),
    "CAMPAIGN_START_DATE" DATE,
    "CAMPAIGN_END_DATE" DATE,
    "AUTO_AFF_APPR_DURATION" NUMBER(2,0) DEFAULT 3,
    "OEM_FLAG" NUMBER(1,0),
    "AUTO_ACTION_APPR_DURATION" NUMBER(2,0),
    "HIDDEN_FLAG" NUMBER(1,0),
    "START_DATE" DATE,
    "END_DATE" DATE,
    "OVERLAP_FLG" NUMBER(1,0) DEFAULT 0,
    "OFFER_CODE" VARCHAR2(32),
    "DESCRIPTION_EN" VARCHAR2(4000),
    "CAMPAIGN_TYPE" NUMBER(2,0) DEFAULT 0,
    "AD_PLATFORM_ID" NUMBER(1,0) DEFAULT 0,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS MERCHANT_ACCOUNT;
CREATE TABLE MERCHANT_ACCOUNT (
    "ACCOUNT_NO" NUMBER(10,0),
    "MERCHANT_TYPE_ID" NUMBER(1,0),
    "CORPORATE_NAME" VARCHAR2(128),
    "CORPORATE_ZIP_CODE" VARCHAR2(10),
    "CORPORATE_PREFECTURE" VARCHAR2(128),
    "CORPORATE_CITY" VARCHAR2(128),
    "CORPORATE_ADDRESS" VARCHAR2(128),
    "CORPORATE_ADDRESS2" VARCHAR2(128),
    "CORPORATE_PHONE" VARCHAR2(32),
    "CORPORATE_FAX" VARCHAR2(32),
    "CORPORATE_DIRECTOR_NAME" VARCHAR2(128),
    "CORPORATE_REMARK" VARCHAR2(2000),
    "FOSTER_LASTNAME" VARCHAR2(64),
    "FOSTER_FIRSTNAME" VARCHAR2(64),
    "FOSTER_MIDDLENAME" VARCHAR2(64),
    "FOSTER_ZIP_CODE" VARCHAR2(10),
    "FOSTER_PREFECTURE" VARCHAR2(128),
    "FOSTER_CITY" VARCHAR2(128),
    "FOSTER_ADDRESS" VARCHAR2(128),
    "FOSTER_ADDRESS2" VARCHAR2(128),
    "FOSTER_SECTION_NAME" VARCHAR2(128),
    "FOSTER_POST_NAME" VARCHAR2(128),
    "FOSTER_EMAIL" VARCHAR2(64),
    "FOSTER_PHONE" VARCHAR2(32),
    "FOSTER_FAX" VARCHAR2(32),
    "FOSTER_REMARK" VARCHAR2(2000),
    "LOGIN_NAME" VARCHAR2(64),
    "LOGIN_PASSWORD" VARCHAR2(32),
    "ACCOUNT_STATE" NUMBER(1,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "ACCOUNTANT_LASTNAME" VARCHAR2(64),
    "ACCOUNTANT_FIRSTNAME" VARCHAR2(64),
    "ACCOUNTANT_MIDDLENAME" VARCHAR2(64),
    "ACCOUNTANT_EMAIL" VARCHAR2(64),
    "ACCOUNTANT_PHONE" VARCHAR2(32),
    "U_ID" VARCHAR2(32) DEFAULT 'v5542527tvx7w4ts6suvswssss2137xx',
    "COUNTRY_CODE" VARCHAR2(2) DEFAULT 'ID'
);

DROP TABLE IF EXISTS CURRENCY_MASTER;
CREATE TABLE CURRENCY_MASTER (
    "CURRENCY" VARCHAR2(3) NOT NULL,
    "FRACTIONAL_DIGITS" NUMBER(1,0) DEFAULT 0 NOT NULL
);

DROP TABLE IF EXISTS PUBLISHER_INVOICE;
CREATE TABLE PUBLISHER_INVOICE(
    "ID" NUMBER(10,0),
    "PUBLISHER_ID" NUMBER(10,0),
    "PAY_MONTH" DATE,
    "KEY" VARCHAR2(16),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS CONVERSION_RANK_UPDATE_HISTORY;
CREATE TABLE CONVERSION_RANK_UPDATE_HISTORY (
    "FROM_DATE" DATE NOT NULL,
    "TO_DATE" DATE NOT NULL,
    "CAMPAIGN_ID" NUMBER(10,0) NOT NULL,
    "SITE_ID" NUMBER(10,0) NOT NULL,
    "OLD_RANK" NUMBER(2,0) NOT NULL,
    "NEW_RANK" NUMBER(2,0) NOT NULL,
    "RANK_UPDATER" VARCHAR2(128) NOT NULL,
    "RANK_UPDATE_DATE" DATE NOT NULL
);

DROP TABLE IF EXISTS CAMPAIGN_CLOSURE;
CREATE TABLE CAMPAIGN_CLOSURE (
    "ID" NUMBER(10,0) NOT NULL PRIMARY KEY,
    "CAMPAIGN_ID" NUMBER(10,0) NOT NULL,
    "STATUS" NUMBER(2,0) NOT NULL,
    "CLOSED_FROM" DATE,
    "CLOSED_TO" DATE,
    "APPROVED_TOTAL_COMMISSION" NUMBER(20,2) DEFAULT 0,
    "APPROVED_AT_COMMISSION" NUMBER(20,2) DEFAULT 0,
    "APPROVED_PUBLISHER_REWARD" NUMBER(20,2) DEFAULT 0,
    "APPROVED_CONVERSION" NUMBER(20,2) DEFAULT 0,
    "APPROVED_TRANSACTION_ITEMS" NUMBER(20,2) DEFAULT 0,
    "CREATED_ON" DATE,
    "CREATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "UPDATED_BY" VARCHAR(256),
    "IS_PAYMENT_GENERATED" NUMBER(1,0) DEFAULT 0 NOT NULL,
    "IS_PROCESSING" NUMBER(1,0) DEFAULT 0
);

DROP TABLE IF EXISTS PUBLISHER_ACCOUNT_PAYMENT_HISTORY;
CREATE TABLE PUBLISHER_ACCOUNT_PAYMENT_HISTORY (
    "ID" NUMBER(10,0) NOT NULL,
    "PUBLISHER_ID" NUMBER(10,0) NOT NULL,
    "CAMPAIGN_CLOSURE_ID" NUMBER(10,0) NOT NULL,
    "PAY_DATE" DATE,
    "REWARD_MONTH" DATE NOT NULL,
    "AMOUNT" NUMBER(15,2),
    "PAYMENT_STATE" NUMBER(2,0),
    "REQUEST_DATE" DATE,
    "CREATED_BY" VARCHAR2(128),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(128),
    "UPDATED_ON" DATE,
    "INVOICE_ID" VARCHAR2(17),
    "AMOUNT_IN_USD" NUMBER(20,2)
);

DROP TABLE IF EXISTS BANNER;
CREATE TABLE BANNER (
    "BANNER_ID" NUMBER(10,0),
    "BANNER_TYPE_ID" NUMBER(2,0),
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0),
    "IMAGE_URL" VARCHAR2(512),
    "BANNER_TEXT" CLOB,
    "DESCRIPTION" VARCHAR2(2000),
    "LINKED_URL" VARCHAR2(2048),
    "BANNER_STATUS" NUMBER(1,0),
    "VALID_START_DATE" DATE,
    "VALID_END_DATE" DATE,
    "REMARK" VARCHAR2(2000),
    "REGISTERED_DATE" DATE,
    "HEIGHT" NUMBER(8,0),
    "WIDTH" NUMBER(8,0),
    "WEIGHT" NUMBER(4,0),
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "LINKED_URL_ANDROID" VARCHAR2(512),
    "LINKED_URL_IOS" VARCHAR2(512),
    "LINKED_URL_ANDROID_SP" VARCHAR2(512),
    "LINKED_URL_IOS_SP" VARCHAR2(512),
    "LINKED_URL_ANDROID_TAB" VARCHAR2(512),
    "LINKED_URL_IOS_TAB" VARCHAR2(512),
    "HIDDEN_FLAG" NUMBER(1,0) DEFAULT 0,
    "BANNER_NAME" VARCHAR2(512),
    "GROUP_ID" NUMBER(10,0),
    "PROMOTION_STATUS" NUMBER(1,0) DEFAULT 0,
    "PROMOTION_START_DATE" DATE,
    "PROMOTION_END_DATE" DATE,
    "PROMOTION_TARGET_AUDIENCE" NUMBER(1,0) DEFAULT 0,
    "SYNC_STATUS" NUMBER(1,0) DEFAULT 0,
    "ORIGINAL_CREATIVE_ID" NUMBER(10,0),
    "ORIGINAL_COUNTRY_CODE" VARCHAR2(2)
);

DROP SEQUENCE IF EXISTS BONUS_ID_SEQ;
CREATE SEQUENCE "BONUS_ID_SEQ" AS BIGINT INCREMENT BY 1 START WITH 1 ;

DROP TABLE IF EXISTS BONUS;
CREATE TABLE BONUS (
    "ID" NUMBER(10,0),
    "BONUS_SETTING_ID" NUMBER(10,0) DEFAULT 0 NOT NULL,
    "SITE_ID" NUMBER(10,0) NOT NULL,
    "CAMPAIGN_ID"  NUMBER(10,0) NOT NULL,
    "PUBLISHER_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "PUBLISHER_AGENT_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "MERCHANT_AGENT_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "AT_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "PUBLISHER_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "PUBLISHER_AGENT_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "MERCHANT_AGENT_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "AT_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "STATUS" NUMBER(2,0), 
    "CONFIRMED_DATE" DATE, 
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE
);

DROP SEQUENCE IF EXISTS FIXED_BONUS_DETAILS_ID_SEQ;
CREATE SEQUENCE "FIXED_BONUS_DETAILS_ID_SEQ" AS BIGINT INCREMENT BY 1 START WITH 1 ;

DROP TABLE IF EXISTS FIXED_BONUS_DETAILS;
CREATE TABLE FIXED_BONUS_DETAILS (
    "ID" NUMBER(10,0),
    "BONUS_ID" NUMBER(10,0) NOT NULL,
    "CONVERSION_ID" NUMBER(10,0) NOT NULL, 
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
);

DROP TABLE IF EXISTS CURRENCY_EXCHANGE_RATE_HISTORY;
CREATE TABLE CURRENCY_EXCHANGE_RATE_HISTORY (
    "CURRENCY" VARCHAR2(3) NOT NULL,
    "QUOTE_CURRENCY" VARCHAR2(3) NOT NULL,
    "TARGET_MONTH" DATE NOT NULL, 
    "RATE" NUMBER(20,10) NOT NULL, 
    "CREATED_BY" VARCHAR2(128), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(128),
    "UPDATED_ON" DATE
);

DROP TABLE IF EXISTS CURRENCY_MASTER;
CREATE TABLE CURRENCY_MASTER (
    "CURRENCY" VARCHAR2(3) NOT NULL, 
    "FRACTIONAL_DIGITS" NUMBER(1,0) DEFAULT 0 NOT NULL
);

DROP TABLE IF EXISTS RESULT_TARGET_SETTING;
CREATE TABLE RESULT_TARGET_SETTING (
    "RESULT_ID" NUMBER(4,0) NOT NULL,
    "RESULT_NAME" VARCHAR2(128 BYTE),
    "REWARD_TYPE" NUMBER(1,0) NOT NULL,
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "AUTO_ACTION_APPR_DURATION" NUMBER(3,0),
    "CUSTOMER_TYPE" VARCHAR2(64)
);

DROP TABLE IF EXISTS ROLES_PERMISSION;
CREATE TABLE ROLES_PERMISSION (
	"ID" NUMBER(10,0) NOT NULL, 
	"ROLE_ID" NUMBER(10,0) NOT NULL, 
	"PERMISSION_ID" NUMBER(10,0) NOT NULL, 
	"CREATED_BY" VARCHAR2(256 BYTE), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256 BYTE), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS PERMISSION;
CREATE TABLE PERMISSION (
	"PERMISSION_ID" NUMBER(10,0) NOT NULL, 
	"PERMISSION_NAME" VARCHAR2(512 BYTE) NOT NULL, 
	"PERMISSION_STATE" NUMBER(1,0) DEFAULT 0, 
	"ENTITY_TYPE" NUMBER(2,0) DEFAULT 0, 
	"DESCRIPTION" VARCHAR2(2000 BYTE), 
	"CREATED_BY" VARCHAR2(256 BYTE), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256 BYTE), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS ROLES;
CREATE TABLE ROLES (
    "ROLE_ID" NUMBER(10,0) NOT NULL,
    "ROLE_NAME" VARCHAR2(256) NOT NULL,
    "ROLE_STATE" NUMBER(1,0) DEFAULT 0
);

DROP TABLE IF EXISTS STAFF_ROLES;
CREATE TABLE STAFF_ROLES (
	"ID" NUMBER(10,0) NOT NULL,
	"STAFF_NO" NUMBER(10,0) NOT NULL, 
	"ROLE_ID" NUMBER(10,0) NOT NULL, 
	"CREATED_BY" VARCHAR2(256 BYTE), 
	"CREATED_ON" DATE, 
	"UPDATED_BY" VARCHAR2(256 BYTE), 
	"UPDATED_ON" DATE
);

DROP TABLE IF EXISTS INVOICE_PAYMENT_TAX_CALCULATION;
CREATE TABLE INVOICE_PAYMENT_TAX_CALCULATION(
    "ID" NUMBER(10,0) NOT NULL PRIMARY KEY,
    "PUBLISHER_ID" NUMBER(10,0) NOT NULL,
    "INVOICE_ID" VARCHAR2(17) NOT NULL,
    "REQUEST_DATE" DATE NOT NULL, 
    "WHT" NUMBER(20,2) NOT NULL, 
    "VAT" NUMBER(20,2) NOT NULL, 
    "CREATED_BY" VARCHAR2(256) NOT NULL, 
    "CREATED_ON" DATE NOT NULL, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE, 
    "WHT_IN_USD" NUMBER(20,2) NOT NULL, 
    "VAT_IN_USD" NUMBER(20,2) NOT NULL, 
    "NOTE" VARCHAR2(256)
);

DROP SEQUENCE IF EXISTS INVOICE_PAYMENT_TAX_CALCULATION_SEQUENCE;
CREATE SEQUENCE "INVOICE_PAYMENT_TAX_CALCULATION_SEQUENCE" AS BIGINT INCREMENT BY 1 START WITH 1;

DROP TABLE IF EXISTS INVOICE_PAYMENT_TAX_CALCULATION_HISTORY;
CREATE TABLE INVOICE_PAYMENT_TAX_CALCULATION_HISTORY(
    "ID" NUMBER(10,0) NOT NULL PRIMARY KEY,
    "PUBLISHER_ID" NUMBER(10,0) NOT NULL,
    "INVOICE_ID" VARCHAR2(17) NOT NULL,
    "REQUEST_DATE" DATE NOT NULL, 
    "WHT" NUMBER(20,2) NOT NULL, 
    "VAT" NUMBER(20,2) NOT NULL, 
    "CREATED_BY" VARCHAR2(256) NOT NULL, 
    "CREATED_ON" DATE NOT NULL, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE, 
    "WHT_IN_USD" NUMBER(20,2) NOT NULL, 
    "VAT_IN_USD" NUMBER(20,2) NOT NULL, 
    "NOTE" VARCHAR2(256)
);

DROP SEQUENCE IF EXISTS INVOICE_PAYMENT_TAX_CALCULATION_HISTORY_SEQUENCE;
CREATE SEQUENCE "INVOICE_PAYMENT_TAX_CALCULATION_HISTORY_SEQUENCE" AS BIGINT INCREMENT BY 1 START WITH 1;

DROP TABLE IF EXISTS CONVERSION_UPDATE_REQUEST;
CREATE TABLE CONVERSION_UPDATE_REQUEST (
    "FILE_NAME" VARCHAR2(512),
    "ERROR_COUNT" NUMBER(2, 0),
    "CAMPAIGN_ID" NUMBER(10, 0),
    "STAFF_EMAIL" VARCHAR2(64),
    "DATA_COUNT" NUMBER(10, 0)
);

