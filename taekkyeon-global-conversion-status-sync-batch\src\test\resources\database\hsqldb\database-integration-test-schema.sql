SET DATABASE SQL SYNTAX ORA TRUE;

DROP TABLE IF EXISTS MERCHANT_CAMPAIGN;
CREATE TABLE MERCHANT_CAMPAIGN (
    "CAMPAIGN_NO" NUMBER(10,0) NOT NULL, 
    "ACCOUNT_NO" NUMBER(10,0) NOT NULL, 
    "CA<PERSON><PERSON>IGN_STATE_ID" NUMBER(2,0) NOT NULL, 
    "CAMPAIGN_NAME" VARCHAR2(512) NOT NULL,
    "IMAGE_URL" VARCHAR2(512), 
    "URL" VARCHAR2(512) NOT NULL, 
    "DESCRIPTION" VARCHAR2(4000), 
    "CATEGORY1" NUMBER(10,0) NOT NULL, 
    "CATEGORY2" NUMBER(10,0) NOT NULL, 
    "CATEGORY3" NUMBER(10,0) NOT NULL,
    "AUTO_AFF_LIMITATION_OPTION" NUMBER(1,0) NOT NULL, 
    "AUTO_AFF_LIMITATION_DIVISION" NUMBER(1,0) NOT NULL,
    "AFF_CONDITION_SPECIAL" VARCHAR2(2048), 
    "RESULT_APPROVAL_SPECIAL" VARCHAR2(2000), 
    "PR_FOR_PARTNER" VARCHAR2(4000), 
    "GET_PARAMETER_FLAG" NUMBER(1,0) NOT NULL, 
    "POINTBACK_PERMISSION" NUMBER(1,0) NOT NULL, 
    "SELF_CONVERSION_FLAG" NUMBER(1,0), 
    "CAMPAIGN_START_DATE" DATE, 
    "CAMPAIGN_END_DATE" DATE, 
    "AUTO_AFF_APPR_DURATION" NUMBER(2,0) DEFAULT 3, 
    "OEM_FLAG" NUMBER(1,0), 
    "AUTO_ACTION_APPR_DURATION" NUMBER(2,0), 
    "HIDDEN_FLAG" NUMBER(1,0), 
    "START_DATE" DATE, 
    "END_DATE" DATE,
    "OVERLAP_FLG" NUMBER(1,0) DEFAULT 0, 
    "OFFER_CODE" VARCHAR2(32), 
    "DESCRIPTION_EN" VARCHAR2(4000), 
    "CAMPAIGN_TYPE" NUMBER(2,0) DEFAULT 0,
    "CURRENCY" VARCHAR2(3) DEFAULT 'USD',
    "AD_PLATFORM_ID" NUMBER(1,0) DEFAULT 0,
    "GLOBAL_FLAG" NUMBER(1, 0) DEFAULT 0 NOT NULL,
    "CREATED_BY" VARCHAR2(256), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256), 
    "UPDATED_ON" DATE,
    "HIDE_CLICK_REFERRER" NUMBER(1,0),
    "ORIGINAL_CAMPAIGN_ID" NUMBER(10,0),
    "ORIGINAL_COUNTRY_CODE" VARCHAR2(256), 
    "DEVICE_TYPES" VARCHAR2(128)
);

DROP TABLE IF EXISTS SALES_LOG;
CREATE TABLE SALES_LOG (
    "SEQ_NO" NUMBER(10,0) NOT NULL, 
    "BANNER_ID" NUMBER(10,0) NOT NULL, 
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "CLICK_DATE" DATE, 
    "SALES_DATE" DATE NOT NULL, 
    "LOG_DATE" DATE NOT NULL, 
    "CONFIRMED_DATE" DATE, 
    "TRANSACTION_ID" VARCHAR2(256 BYTE) NOT NULL, 
    "INTERNAL_TRANSACTION_ID" VARCHAR2(512 BYTE), 
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL, 
    "RANK" NUMBER(2,0), 
    "VERIFY" VARCHAR2(256 BYTE), 
    "RESULT_ID" NUMBER(4,0), 
    "GOODS_ID" VARCHAR2(250 BYTE), 
    "SALES_LOG_STATUS" NUMBER(2,0) NOT NULL, 
    "SALES_COUNT" NUMBER(10,0) NOT NULL, 
    "PRICE" NUMBER(12,2) NOT NULL, 
    "TOTAL_PRICE" NUMBER(12,2) NOT NULL, 
    "REWARD_TYPE" NUMBER(1,0) NOT NULL, 
    "SALES_REWARD" NUMBER(12,2) NOT NULL, 
    "TOTAL_PRICE_REWARD" NUMBER(12,2) NOT NULL, 
    "COMMISSION_TYPE" NUMBER(1,0) NOT NULL, 
    "AT_COMMISSION" NUMBER(12,2) NOT NULL, 
    "AGENT_COMMISSION" NUMBER(12,2) NOT NULL, 
    "P_AGENT_COMMISSION" NUMBER(12,2) DEFAULT 0 NOT NULL, 
    "IP" VARCHAR2(256 BYTE), 
    "MEDIA_URL" VARCHAR2(512 BYTE), 
    "REFERER" VARCHAR2(2048 BYTE), 
    "REPEAT_COUNT" NUMBER(10,0), 
    "USER_AGENT" VARCHAR2(512 BYTE), 
    "REWARD_EDIT_DATE" DATE,
    "DEFAULT_SALES_COUNT" NUMBER(10,0) NOT NULL, 
    "DEFAULT_PRICE" NUMBER(10,0) NOT NULL, 
    "DEFAULT_RESULT_ID" NUMBER(4,0), 
    "LP_URL" VARCHAR2(512 BYTE), 
    "DEVICE_TYPE" NUMBER(2,0), 
    "POINTBACK_ID" VARCHAR2(64 BYTE), 
    "PB_ID_DUPLICATIVE_FLAG" NUMBER(1,0), 
    "PB_ID_OLDEST_SALES_DATE" DATE, 
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE, 
    "NEW_FLAG" NUMBER(1,0) DEFAULT 0, 
    "SESSION_ID" VARCHAR2(256 BYTE), 
    "CURRENCY_ID" NUMBER(4,0), 
    "UUID" VARCHAR2(64 BYTE) DEFAULT NULL, 
    "DEVICE_OS" NUMBER(2,0) DEFAULT 0 NOT NULL, 
    "CATEGORY_ID" VARCHAR2(250 BYTE), 
    "DISCOUNT" NUMBER(12,2) DEFAULT 0,
    "POSTBACK_STATUS" NUMBER(1, 0) DEFAULT 1 NOT NULL,
    "CUSTOMER_TYPE" VARCHAR2(64),
    "REDSHIFT_SYNC_REQUIRED" NUMBER(1,0) DEFAULT 0 NOT NULL
);

DROP TABLE IF EXISTS MONTHLY_CLOSING;
CREATE TABLE MONTHLY_CLOSING (
    "CLOSED_MONTH" CHAR(6 BYTE) NOT NULL, 
    "TARGET_MONTH" CHAR(6 BYTE) NOT NULL, 
    "TEMPORARY_CLOSING_FLAG" NUMBER(1,0) NOT NULL, 
    "CREATED_BY" VARCHAR2(256 BYTE), 
    "CREATED_ON" DATE, 
    "UPDATED_BY" VARCHAR2(256 BYTE), 
    "UPDATED_ON" DATE,
    "IMMEDIATE_CLOSURE_STATUS" NUMBER(1,0) DEFAULT 0 NOT NULL,
    "COUNTRY_CODE" VARCHAR2(256) NOT NULL
);

DROP TABLE IF EXISTS SYNCHRONIZATION_DATA;
CREATE TABLE SYNCHRONIZATION_DATA (
    "TABLE_NAME" VARCHAR2(64),
    "SYNC_START_TIME" DATE,
    "DATABASE_NAME" VARCHAR2(32),
    "CONVERSION_ID" NUMBER(10, 0),
    "COUNTRY_CODE" VARCHAR2(2),
    "SITE_ID" NUMBER(10, 0),
    "CLOSED_MONTH" DATE
);

DROP TABLE IF EXISTS COUNTRY;
CREATE TABLE COUNTRY (
    "CODE" CHAR(2),
    "ZONE_ID" VARCHAR2(40)
);
