/**
 * Copyright © 2019 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.module;

import javax.inject.Singleton;

import com.google.common.collect.ImmutableTable;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.name.Named;

import static java.lang.Integer.parseInt;
import static java.lang.System.getProperty;
import static jp.ne.interspace.taekkyeon.module.Country.INDONESIA;
import static jp.ne.interspace.taekkyeon.module.Country.THAILAND;
import static jp.ne.interspace.taekkyeon.module.Country.VIETNAM;
import static jp.ne.interspace.taekkyeon.module.Country.getCurrentCountry;
import static jp.ne.interspace.taekkyeon.module.DatabaseType.REDSHIFT;
import static jp.ne.interspace.taekkyeon.module.Environment.DEV;
import static jp.ne.interspace.taekkyeon.module.Environment.PRODUCTION;
import static jp.ne.interspace.taekkyeon.module.Environment.STAGING;
import static jp.ne.interspace.taekkyeon.module.Environment.getCurrentEnvironment;

/**
 * Taekkyeon module for redshift synchronization.
 *
 * <AUTHOR> Shin
 */
public class TaekkyeonRedshiftSyncModule extends AbstractModule {

    public static final String BIND_KEY_PARTITION_LIMITATION = "partition.limitation";
    public static final String BIND_KEY_REDSHIFT_CREDENTIALS = "redshift.credentials";
    public static final String BIND_KEY_BUCKET_NAME = "bucket.name";
    public static final String BIND_KEY_MINUTES_OFFSET = "time.offset.minutes";
    private static final String PARTITION_LIMITATION = "partitionLimitation";
    private static final String REDSHIFT_CREDENTIALS = "redshiftCredentials";
    private static final String TIME_OFFSET = "timeOffsetMinutes";

    private static final ImmutableTable<Country, Environment, String> BUCKET_NAMES =
            new ImmutableTable.Builder<Country, Environment, String>()
            .put(INDONESIA, STAGING, "indonesia.staging.redshift.synchronization")
            .put(THAILAND, STAGING, "thailand.staging.redshift.synchronization")
            .put(VIETNAM, STAGING, "vietnam.staging.redshift.synchronization")
            .put(INDONESIA, PRODUCTION, "indonesia.production.redshift.synchronization")
            .put(THAILAND, PRODUCTION, "thailand.production.redshift.synchronization")
            .put(VIETNAM, PRODUCTION, "vietnam.production.redshift.synchronization")
            .build();

    @Override
    protected void configure() {
        installDatabaseModule();
    }

    @Provides @Singleton @Named(BIND_KEY_PARTITION_LIMITATION)
    private int getPartitionLimitation() {
        return parseInt(getProperty(PARTITION_LIMITATION));
    }

    @Provides @Singleton @Named(BIND_KEY_MINUTES_OFFSET)
    private int getTimeMinutesOffset() {
        return parseInt(getProperty(TIME_OFFSET, "60"));
    }

    @Provides @Singleton @Named(BIND_KEY_BUCKET_NAME)
    private String getBucketName() {
        return BUCKET_NAMES.get(getCurrentCountry(), getCurrentEnvironment());
    }

    @Provides @Singleton @Named(BIND_KEY_REDSHIFT_CREDENTIALS)
    private String getRedshiftCredentials() {
        return getProperty(REDSHIFT_CREDENTIALS);
    }

    private void installDatabaseModule() {
        if (getCurrentEnvironment() == DEV) {
            install(new TaekkyeonHsqldbRedshiftModule());
        } else {
            install(new TaekkyeonMyBatisModule(REDSHIFT));
        }
    }
}
