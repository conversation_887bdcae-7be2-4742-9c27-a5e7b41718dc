/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.TestConversion;
import jp.ne.interspace.taekkyeon.multiline.Multiline;
import jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.UtcZonedDateTimeTypeHandler;

/**
 * MyBatis mapper of the integration test for the {@code SALES_LOG} table.
 *
 * <AUTHOR>
 */
public interface ConversionLogIntegrationTestMapper {

    /**
        SELECT
            seq_no id,
            sales_reward salesReward,
            total_price_reward transactionAmountReward,
            at_commission atCommission,
            agent_commission agentCommission,
            p_agent_commission publisherAgentCommission,
            updated_on latestUpdateTime
        FROM
            sales_log
        WHERE
            seq_no = #{conversionId}
    */
    @Multiline String SELECT_CONVERSION_BY_ID = "";

    @Select(SELECT_CONVERSION_BY_ID)
    @ConstructorArgs({ @Arg(column = "id", javaType = Long.class),
            @Arg(column = "salesReward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmountReward", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "agentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "latestUpdateTime", javaType = ZonedDateTime.class,
                    typeHandler = UtcZonedDateTimeTypeHandler.class) })
    TestConversion findConversion(long conversionId);

    /**
        SELECT
            redshift_sync_required
        FROM
            sales_log
        WHERE
            seq_no = #{conversionId}
    */
    @Multiline String SELECT_REDSHIFT_SYNC_REQUIRED_BY_CONVERSION_ID = "";

    @Select(SELECT_REDSHIFT_SYNC_REQUIRED_BY_CONVERSION_ID)
    Integer getRedshiftSyncRequiredByConversionId(long conversionId);
}
