/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Getter;

/**
 * DTO for holding publisher funnel details by time.
 *
 * <AUTHOR>
 */
@Getter
public class PublisherFunnelDetailsByTime extends PublisherFunnelDetails {

    private final LocalDateTime latestTime;

    /**
     * Constructor to initialize all fields of PublisherFunnelDetailsByTime.
     */
    public PublisherFunnelDetailsByTime(long accountId, String countryCode, Long referrerId,
            String utmSource, PublisherAccountType accountType, LocalDate registeredDate,
            LocalDate activatedDate, LocalDate firstApprovedSiteDate,
            LocalDate firstAffiliationDate,
            LocalDate firstApproveAffiliationDate, LocalDate firstImpressionOrClickDate,
            LocalDate firstConversionDate, LocalDate firstApprovedConversionDate,
            LocalDate firstPaymentDate, BigDecimal occurredSalesReward,
            BigDecimal occurredTransactionAmountReward,
            BigDecimal occurredAtCommission, BigDecimal occurredMerchantAgentCommission,
            BigDecimal occurredPublisherAgentCommission, BigDecimal approvedSalesReward,
            BigDecimal approvedTransactionAmountReward, BigDecimal approvedAtCommission,
            BigDecimal approvedMerchantAgentCommission, BigDecimal approvedPublisherAgentCommission,
            String registrationReferralUrl, String registrationReferralDomain, String utmMedium,
            String utmContent, String utmCampaign, String utmTerm, String email, LocalDateTime latestTime) {
        super(accountId, countryCode, referrerId, utmSource, accountType,
                registeredDate, activatedDate, firstApprovedSiteDate, firstAffiliationDate,
                firstApproveAffiliationDate, firstImpressionOrClickDate, firstConversionDate,
                firstApprovedConversionDate, firstPaymentDate, occurredSalesReward,
                occurredTransactionAmountReward, occurredAtCommission,
                occurredMerchantAgentCommission,
                occurredPublisherAgentCommission, approvedSalesReward,
                approvedTransactionAmountReward,
                approvedAtCommission, approvedMerchantAgentCommission,
                approvedPublisherAgentCommission,
                registrationReferralUrl, registrationReferralDomain,
                utmMedium, utmContent, utmCampaign,
                utmTerm, email);
        this.latestTime = latestTime;
    }
}
