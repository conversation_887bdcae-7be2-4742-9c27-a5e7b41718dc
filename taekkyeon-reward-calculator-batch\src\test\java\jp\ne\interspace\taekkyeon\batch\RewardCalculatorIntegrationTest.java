/**
 * Copyright © 2018 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.batch;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.google.common.collect.ImmutableMap;
import com.google.inject.Inject;

import org.apache.ibatis.session.SqlSessionManager;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import jp.ne.interspace.taekkyeon.batch.module.RewardCalculatorJunitModule;
import jp.ne.interspace.taekkyeon.batch.processor.RewardCalculatorRecordProcessor;
import jp.ne.interspace.taekkyeon.batch.reader.RewardCalculatorRecordReader;
import jp.ne.interspace.taekkyeon.batch.writer.RewardCalculatorRecordWriter;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonHsqldbOracleJunitModule;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonIntegrationTestHsqldbJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.TestConversion;
import jp.ne.interspace.taekkyeon.model.TestCreativeAccessLogItem;
import jp.ne.interspace.taekkyeon.model.TestCreativeAccessLogSummary;
import jp.ne.interspace.taekkyeon.module.OracleResolver;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogIntegrationTestMapper;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.CreativeAccessLogIntegrationTestMapper;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.ConversionUpdateQueue;
import jp.ne.interspace.taekkyeon.persist.aws.sqs.CreativeAccessLogUpdateQueue;
import jp.ne.interspace.taekkyeon.service.JsonSerializerService;

import static java.math.BigDecimal.valueOf;
import static java.time.temporal.ChronoUnit.SECONDS;
import static org.easybatch.core.job.JobBuilder.newJob;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * Integration test for the Reward calculator batch.
 *
 * <AUTHOR>
 */
@RunWith(TaekkyeonIntegrationTestHsqldbJunitRunner.class)
@TaekkyeonModules({ RewardCalculatorJunitModule.class,
        TaekkyeonPropertiesJunitModule.class, TaekkyeonHsqldbOracleJunitModule.class })
public class RewardCalculatorIntegrationTest {

    @Inject
    private RewardCalculatorRecordReader recordReader;

    @Inject
    private RewardCalculatorRecordProcessor recordProcessor;

    @Inject
    private RewardCalculatorRecordWriter recordWriter;

    @Inject
    private ConversionUpdateQueue conversionQueue;

    @Inject
    private CreativeAccessLogUpdateQueue creativeAccessLogQueue;

    @Inject
    private ConversionLogIntegrationTestMapper conversionLogMapper;

    @Inject
    private CreativeAccessLogIntegrationTestMapper creativeAccessLogMapper;

    @Inject
    private JsonSerializerService jsonSerializerService;

    @Inject
    @OracleResolver
    private SqlSessionManager sqlSessionManager;

    @Before
    public void setup() {
        conversionQueue.setUpForTest();
        conversionQueue.waitUntilAvailable();
        creativeAccessLogQueue.setUpForTest();
        creativeAccessLogQueue.waitUntilAvailable();
    }

    @After
    public void tearDown() {
        conversionQueue.tearDownForTestWithoutTestSeedReset();
        creativeAccessLogQueue.tearDownForTestWithoutTestSeedReset();
    }

    @Test
    public void testRewardCalculatorBatchShouldProcessConversionsAndCreativeAccessLogsCorrectly() throws Exception {
        // given
        List<Long> expectedConversionIds = Arrays.asList(1L, 10L, 11L, 13L, 30L, 31L);
        Map<Long, TestConversion> expectedConversions = prepareExpectedConversions();
        ZonedDateTime latestUpdateTime = ZonedDateTime.now();

        // when
        processAllRecords();

        // then
        verifyConversionsInSqsAndRds(expectedConversionIds, expectedConversions, latestUpdateTime);
        verifyCreativeAccessLogsInSqsAndRds();
        verifyRedshiftSyncRequiredIsSetForAllConversions(
                Arrays.asList(1L, 10L, 11L, 13L, 30L, 31L));
    }

    private Map<Long, TestConversion> prepareExpectedConversions() {
        return ImmutableMap.<Long, TestConversion>builder()
                .put(1L, new TestConversion(1L, valueOf(100), valueOf(200), valueOf(1),
                        valueOf(2), valueOf(3), null))
                .put(10L, new TestConversion(10L, valueOf(0), valueOf(180), valueOf(6),
                        valueOf(90), valueOf(66.0), null))
                .put(11L, new TestConversion(11L, valueOf(0), valueOf(30.74), valueOf(1.16),
                        valueOf(15.08), valueOf(11.02), null))
                .put(13L, new TestConversion(13L, valueOf(100), valueOf(200), valueOf(1),
                        valueOf(2), valueOf(0), null))
                .put(30L, new TestConversion(30L, valueOf(25), valueOf(20), valueOf(5),
                        valueOf(10), valueOf(5), null))
                .put(31L, new TestConversion(31L, valueOf(25), valueOf(20), valueOf(5),
                        valueOf(10), valueOf(5), null))
                .build();
    }

    private void verifyConversionsInSqsAndRds(List<Long> expectedConversionIds,
                                              Map<Long, TestConversion> expectedConversions,
                                              ZonedDateTime latestUpdateTime) throws Exception {
        List<Message> actualMessages = fetchMessagesFromQueue(conversionQueue, 10);
        assertNotNull(actualMessages);

        for (Message message : actualMessages) {
            TestConversion conversionInSqs = jsonSerializerService.fromJson(
                    message.getBody(), TestConversion.class);
            latestUpdateTime = conversionInSqs.getLatestUpdateTime();
            Long conversionId = conversionInSqs.getId();
            expectedConversionIds.remove(conversionId);

            TestConversion conversionInRds = conversionLogMapper.findConversion(conversionId);
            assertFields(conversionInRds, conversionInSqs.getId(), conversionInSqs.getSalesReward(),
                    conversionInSqs.getTransactionAmountReward(), conversionInSqs.getAtCommission(),
                    conversionInSqs.getAgentCommission(),
                    conversionInSqs.getPublisherAgentCommission());
            assertEquals(conversionInSqs.getLatestUpdateTime().truncatedTo(SECONDS).toEpochSecond(),
                    conversionInRds.getLatestUpdateTime().toEpochSecond());
        }

        for (Long conversionId : expectedConversionIds) {
            TestConversion conversionInRds = conversionLogMapper.findConversion(conversionId);
            TestConversion expectedConversion = expectedConversions.get(conversionId);
            assertFields(conversionInRds, expectedConversion.getId(),
                    expectedConversion.getSalesReward(),
                    expectedConversion.getTransactionAmountReward(),
                    expectedConversion.getAtCommission(),
                    expectedConversion.getAgentCommission(),
                    expectedConversion.getPublisherAgentCommission());
            assertEquals(latestUpdateTime.toLocalDate(), conversionInRds.getLatestUpdateTime().toLocalDate());
        }
    }

    private void verifyCreativeAccessLogsInSqsAndRds() throws Exception {
        List<Message> actualMessages = fetchMessagesFromQueue(creativeAccessLogQueue, 1, 20, 60);
        assertNotNull(actualMessages);

        Thread.sleep(30000);
        actualMessages.addAll(fetchMessagesFromQueue(creativeAccessLogQueue, 1, 20, 60));

        List<TestCreativeAccessLogSummary> creativeAccessLogsInSqs =
                getCreativeAccessLogs(actualMessages);
        for (TestCreativeAccessLogSummary creativeAccessLogInSqs : creativeAccessLogsInSqs) {
            findCreativeAccessLogInRdsAndVerifyIt(creativeAccessLogInSqs, "+08:00");
        }
    }

    private List<Message> fetchMessagesFromQueue(ConversionUpdateQueue queue, int maxMessages) {
        ReceiveMessageRequest request = new ReceiveMessageRequest(queue.getQueueUrl());
        request.setMaxNumberOfMessages(maxMessages);
        return queue.receiveMessage(request).getMessages();
    }

    private List<Message> fetchMessagesFromQueue(CreativeAccessLogUpdateQueue queue,
            int maxMessages, int waitTime, int visibilityTimeout) {
        ReceiveMessageRequest request = new ReceiveMessageRequest(queue.getQueueUrl());
        request.setMaxNumberOfMessages(maxMessages);
        request.setWaitTimeSeconds(waitTime);
        request.setVisibilityTimeout(visibilityTimeout);
        return queue.receiveMessage(request).getMessages();
    }

    private void processAllRecords() throws Exception {
        newJob(sqlSessionManager).named("RewardCalculatorIntegrationTest")
                .reader(recordReader).processor(recordProcessor).writer(recordWriter)
                .build().call();
    }

    private void assertFields(TestConversion actual, Long id, BigDecimal salesReward,
            BigDecimal transactionAmountReward, BigDecimal atCommission,
            BigDecimal agentCommission, BigDecimal publisherAgentCommission) {
        assertNotNull(actual);
        assertEquals(id, actual.getId());
        assertEquals(salesReward.doubleValue(), actual.getSalesReward().doubleValue(), 0);
        assertEquals(transactionAmountReward.doubleValue(),
                actual.getTransactionAmountReward().doubleValue(), 0);
        assertEquals(atCommission.doubleValue(), actual.getAtCommission().doubleValue(),
                0);
        assertEquals(agentCommission.doubleValue(),
                actual.getAgentCommission().doubleValue(), 0);
        assertEquals(publisherAgentCommission.doubleValue(),
                actual.getPublisherAgentCommission().doubleValue(), 0);
    }

    private List<TestCreativeAccessLogSummary> getCreativeAccessLogs(
            List<Message> sqsMessages) {
        return sqsMessages.stream()
                .map(message -> jsonSerializerService.fromJson(message.getBody(),
                        TestCreativeAccessLogSummary.class))
                .sorted((creativeAccessLog1, creativeAccessLog2) -> creativeAccessLog1
                        .getLogTime().compareTo(creativeAccessLog2.getLogTime()))
                .collect(Collectors.toList());
    }

    private void findCreativeAccessLogInRdsAndVerifyIt(
            TestCreativeAccessLogSummary creativeAccessLogInSqs,
            String expectedOffsetId) {
        List<TestCreativeAccessLogItem> creativeAccessLogs = creativeAccessLogMapper
                .findCreativeAccessLogSummary(creativeAccessLogInSqs);
        assertNotNull(creativeAccessLogs);
        assertEquals(1, creativeAccessLogs.size());
        TestCreativeAccessLogItem creativeAccessLogInRds = creativeAccessLogs.get(0);
        assertEquals(creativeAccessLogInSqs.getCreativeId(),
                creativeAccessLogInRds.getCreativeId().longValue());
        assertEquals(creativeAccessLogInSqs.getCampaignId(),
                creativeAccessLogInRds.getCampaignId().longValue());
        assertEquals(creativeAccessLogInSqs.getSiteId(),
                creativeAccessLogInRds.getSiteId().longValue());
        assertEquals(creativeAccessLogInSqs.getLogTime().toLocalDateTime(),
                creativeAccessLogInRds.getLogTime());
        assertEquals(expectedOffsetId,
                creativeAccessLogInSqs.getLogTime().getOffset().getId());
        assertEquals(creativeAccessLogInSqs.getDeviceType(),
                creativeAccessLogInRds.getDeviceType());
        assertEquals(creativeAccessLogInSqs.getDeviceOs(),
                creativeAccessLogInRds.getDeviceOs());
        assertEquals(creativeAccessLogInSqs.getProductId(),
                creativeAccessLogInRds.getProductId());
        assertEquals(creativeAccessLogInSqs.getClickReward(),
                creativeAccessLogInRds.getClickReward());
        assertEquals(creativeAccessLogInSqs.getAtCommission(),
                creativeAccessLogInRds.getAtCommission());
        assertEquals(creativeAccessLogInSqs.getAgentCommission(),
                creativeAccessLogInRds.getAgentCommission());
        assertEquals(creativeAccessLogInSqs.getPublisherAgentCommission(),
                creativeAccessLogInRds.getPublisherAgentCommission());
    }

    private void verifyRedshiftSyncRequiredIsSetForAllConversions(
            List<Long> conversionIds) {
        for (Long conversionId : conversionIds) {
            Integer redshiftSyncRequired = conversionLogMapper
                    .getRedshiftSyncRequiredByConversionId(conversionId);
            assertEquals(Integer.valueOf(1), redshiftSyncRequired);
        }
    }
}
