/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Options.FlushCachePolicy;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.GlobalConversionDetails;
import jp.ne.interspace.taekkyeon.model.GlobalConversionStatusSynchronizationData;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

/**
 * MyBatis mapper for the SALES_LOG table.
 *
 * <AUTHOR> Mayur
 */
public interface ConversionMapper {

    /**
    SELECT
        conversion.*
     */
    @Multiline String SELECT_CONVERSION = "";

    /**
    SELECT
        COUNT(*)
     */
    @Multiline String SELECT_CONVERSION_COUNT = "";

    /**
        FROM (
            SELECT
                seq_no AS conversionId,
                confirmed_date AS confirmationTime,
                sales_log_status AS status,
                transaction_id AS transactionId,
                merchant_campaign_no AS campaignId,
                updated_by AS updatedBy,
                1 AS shouldSyncStatus
            FROM
                sales_log
            WHERE
                sales_log_status IN (1,2)
            AND
            (
                confirmed_date > #{synchronizationData.syncStartTime}
            OR (
                confirmed_date = #{synchronizationData.syncStartTime}
                AND
                    seq_no > #{synchronizationData.conversionId}
            ))
            AND
                TRUNC(confirmed_date, 'MM') = #{synchronizationData.closedMonth, jdbcType=DATE,
                typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.TimestampYearMonthTypeHandler}
            AND
                partner_site_no = #{synchronizationData.siteId}
            ORDER BY
                confirmed_date, seq_no
        ) conversion
     */
    @Multiline String SELECT_APPROVED_AND_REJECTED_CONVERSIONS_FOR_SYNC = "";

    /**
        FROM (
            SELECT
                seq_no AS conversionId,
                confirmed_date AS confirmationTime,
                sales_log_status AS status,
                transaction_id AS transactionId,
                merchant_campaign_no AS campaignId,
                updated_by AS updatedBy,
                1 AS shouldSyncStatus
            FROM
                sales_log
            WHERE
                sales_log_status IN (1,2)
            AND
            (
                confirmed_date > #{synchronizationData.syncStartTime}
            OR (
                confirmed_date = #{synchronizationData.syncStartTime}
                AND
                    seq_no > #{synchronizationData.conversionId}
            ))
            AND
                TRUNC(confirmed_date, 'MM') = #{synchronizationData.closedMonth, jdbcType=DATE,
                typeHandler = jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.TimestampYearMonthTypeHandler}
            AND
                partner_site_no = #{synchronizationData.siteId}
            AND
                merchant_campaign_no = #{campaignId}
            ORDER BY
                confirmed_date, seq_no
        ) conversion
     */
    @Multiline String SELECT_APPROVED_AND_REJECTED_CONVERSIONS_WITH_CAMPAIGN_ID_FOR_SYNC = "";

    /**
        WHERE
            ROWNUM <= #{maxRecordCount}
     */
    @Multiline String WHERE_ROWNUM_CONDITION = "";

    /**
        UPDATE
            sales_log
        SET
            confirmed_date = #{conversion.confirmationTime},
            sales_log_status = #{conversion.status},
            redshift_sync_required = 1,
            updated_by = 'GLOBAL_CONVERSION_STATUS_SYNC_BATCH',
            updated_on = SYSDATE
        WHERE
            transaction_id = #{conversion.transactionId}
        AND
            sales_log_status != #{conversion.status}
        AND
            merchant_campaign_no = #{conversion.campaignId}
     */
    @Multiline String UPDATE_CONVERSION_FOR_SYNC = "";

    /**
     * Returns the conversions for synchronization data.
     *
     * @param synchronizationData
     *            the synchronization data criterion
     * @param maxRecordCount
     *            the maximum record count of search
     * @return the conversions for synchronization data
     */
    @Select(SELECT_CONVERSION + SELECT_APPROVED_AND_REJECTED_CONVERSIONS_FOR_SYNC
            + WHERE_ROWNUM_CONDITION)
    @Options(useCache = false, flushCache = FlushCachePolicy.TRUE)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "status", javaType = ConversionStatus.class),
            @Arg(column = "transactionId", javaType = String.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "updatedBy", javaType = String.class),
            @Arg(column = "shouldSyncStatus", javaType = boolean.class)})
    List<Conversion> findConversionsForSync(
            @Param("synchronizationData") GlobalConversionStatusSynchronizationData
            synchronizationData,
            @Param("maxRecordCount") int maxRecordCount);

    /**
     * Returns the conversions for synchronization data.
     *
     * @param synchronizationData
     *            the synchronization data criterion
     * @param maxRecordCount
     *            the maximum record count of search
     * @param campaignId
     *            the given campaign ID
     * @return the conversions for synchronization data
     */
    @Select(SELECT_CONVERSION
            + SELECT_APPROVED_AND_REJECTED_CONVERSIONS_WITH_CAMPAIGN_ID_FOR_SYNC
            + WHERE_ROWNUM_CONDITION)
    @Options(useCache = false, flushCache = FlushCachePolicy.TRUE)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "confirmationTime", javaType = LocalDateTime.class),
            @Arg(column = "status", javaType = ConversionStatus.class),
            @Arg(column = "transactionId", javaType = String.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "updatedBy", javaType = String.class),
            @Arg(column = "shouldSyncStatus", javaType = boolean.class) })
    List<Conversion> findConversionsWithCampaignIdForSync(
            @Param("synchronizationData") GlobalConversionStatusSynchronizationData synchronizationData,
            @Param("maxRecordCount") int maxRecordCount,
            @Param("campaignId") long campaignId);

    /**
     * Returns the conversions count for synchronization data.
     *
     * @param synchronizationData
     *            the synchronization data criterion
     * @return the conversions count for synchronization data
     */
    @Select(SELECT_CONVERSION_COUNT + SELECT_APPROVED_AND_REJECTED_CONVERSIONS_FOR_SYNC)
    long countConversionsforSync(
            @Param("synchronizationData") GlobalConversionStatusSynchronizationData
            synchronizationData);

    /**
     * Returns the conversions count for synchronization data.
     *
     * @param synchronizationData
     *            the synchronization data criterion
     * @param campaignId
     *            the given campaign ID
     * @return the conversions count for synchronization data
     */
    @Select(SELECT_CONVERSION_COUNT
            + SELECT_APPROVED_AND_REJECTED_CONVERSIONS_WITH_CAMPAIGN_ID_FOR_SYNC)
    long countConversionsWithCampaignIdForSync(
            @Param("synchronizationData") GlobalConversionStatusSynchronizationData synchronizationData,
            @Param("campaignId") long campaignId);

    /**
     * Updates a conversion data.
     *
     * @param conversion
     *          {@link Conversion} containing the information of conversion
     * @param countryCode
     *          {@link countryCode} containing the information of country code
     * @return the number of updated row
     */
    @Update(UPDATE_CONVERSION_FOR_SYNC)
    int update(@Param("conversion") Conversion conversion,
            @Param("countryCode") String countryCode);

    /**
        SELECT
            confirmed_date as confirmationDate,
            merchant_campaign_no as campaignId,
            sales_log_status as status
        FROM
            sales_log
        WHERE
            transaction_id = #{conversion.transactionId}
        AND
            merchant_campaign_no = (
                SELECT
                    campaign_no
                FROM
                    merchant_campaign
                WHERE
                    original_campaign_id = #{conversion.campaignId}
                AND
                    original_country_code = #{countryCode}
            )
        FETCH FIRST ROW ONLY
    */
@Multiline String SELECT_CONFIRMATION_TIME = "";

/**
 * Returns the global conversion details for a given conversion and country code.
 *
 * @param conversion
 *          {@link Conversion} containing the information of conversion
 * @param countryCode
 *          {@link countryCode} containing the information of country code
 * @return the {@link jp.ne.interspace.taekkyeon.model.GlobalConversionDetails} of the conversion
 */
@Select(SELECT_CONFIRMATION_TIME)
@ConstructorArgs({
        @Arg(column = "confirmationDate", javaType = LocalDateTime.class),
        @Arg(column = "campaignId", javaType = Long.class),
        @Arg(column = "status", javaType = ConversionStatus.class) })
GlobalConversionDetails findConfirmationTime(
        @Param("conversion") Conversion conversion,
        @Param("countryCode") String countryCode);
}
