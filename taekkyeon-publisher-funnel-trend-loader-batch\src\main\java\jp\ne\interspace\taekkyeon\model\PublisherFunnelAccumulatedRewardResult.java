/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Result class to hold both approved and occurred
 * accumulated reward funnel details.
 *
 * <AUTHOR>
 */
@AllArgsConstructor @Getter
public class PublisherFunnelAccumulatedRewardResult {

    private final Map<Long, ApprovedRewardFunnelDetails> approvedRewards;
    private final Map<Long, OccurredRewardFunnelDetails> occurredRewards;
}
