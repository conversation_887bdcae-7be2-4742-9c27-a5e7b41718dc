/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import jp.ne.interspace.taekkyeon.model.BonusStatus;
import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.ConversionStatus;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.IntegrationTestClickParameter;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversion;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversionBonus;
import jp.ne.interspace.taekkyeon.model.IntegrationTestConversionRankUpdatedHistory;
import jp.ne.interspace.taekkyeon.model.IntegrationTestFixedBonus;
import jp.ne.interspace.taekkyeon.model.IntegrationTestFixedBonusDetails;
import jp.ne.interspace.taekkyeon.model.IntegrationTestInvoicePaymentTax;
import jp.ne.interspace.taekkyeon.model.InvoiceAndPaidDate;
import jp.ne.interspace.taekkyeon.model.PostbackStatus;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.multiline.Multiline;
import jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.UtcZonedDateTimeTypeHandler;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEFAULT_ORACLE_FETCH_SIZE;

/**
 * MyBatis mapper for handling integration test data operations.
 *
 * <AUTHOR>
 */
public interface IntegrationTestMapper {

    String START_TAG_SCRIPT = "<script>";
    String END_TAG_SCRIPT = "</script>";

    /**
        SELECT
            seq_no conversionId,
            banner_id creativeId,
            merchant_campaign_no campaignId,
            click_date clickTime,
            sales_date conversionTime,
            log_date registrationTime,
            confirmed_date confirmationDate,
            transaction_id transactionId,
            internal_transaction_id internalTransactionId,
            partner_site_no siteId,
            rank rank,
            verify verify,
            result_id resultId,
            goods_id productId,
            sales_log_status status,
            sales_count productQuantity,
            price productUnitPrice,
            total_price transactionAmount,
            reward_type rewardType,
            sales_reward transactionReward,
            total_price_reward transactionTotalReward,
            commission_type commissionType,
            at_commission atCommission,
            agent_commission agentCommission,
            default_sales_count defaultProductQuantity,
            default_price defaultProductUnitPrice,
            default_result_id defaultResultId,
            device_type deviceType,
            pb_id_duplicative_flag postbackIdDuplicativeFlag,
            category_id categoryId,
            discount discount,
            created_by creator,
            created_on creationTime,
            updated_by updater,
            updated_on updatedDate,
            postback_status postbackStatus,
            customer_type customerType,
            reward_edit_date rewardEditDate,
            click_ip clickIp,
            language language,
            new_flag newFlag,
            bonus_setting_id bonusSettingId,
            publisher_bonus publisherBonus,
            publisher_agent_bonus publisherAgentBonus,
            merchant_agent_bonus merchantAgentBonus,
            at_bonus atBonus,
            publisher_bonus_in_usd publisherBonusInUsd,
            publisher_agent_bonus_in_usd publisherAgentBonusInUsd,
            merchant_agent_bonus_in_usd merchantAgentBonusInUsd,
            at_bonus_in_usd atBonusInUsd
        FROM
            sales_log
        WHERE
            seq_no IN
            <foreach item="item" index="index" collection="conversionIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        ORDER BY
            seq_no
     */
    @Multiline String SELECT_CONVERSIONS_BY_CONVERSION_IDS = "";

    /**
    SELECT
        seq_no conversionId,
        bonus_setting_id bonusSettingId,
        publisher_bonus publisherBonus,
        publisher_agent_bonus publisherAgentBonus,
        merchant_agent_bonus merchantAgentBonus,
        at_bonus atBonus,
        publisher_bonus_in_usd publisherBonusInUsd,
        publisher_agent_bonus_in_usd publisherAgentBonusInUsd,
        merchant_agent_bonus_in_usd merchantAgentBonusInUsd,
        at_bonus_in_usd atBonusInUsd,
        bonus_created_by bonusCreatedBy,
        updated_by updatedBy
    FROM
        sales_log
    WHERE
        seq_no IN
        <foreach item="item" index="index" collection="conversionIds"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    ORDER BY
        seq_no
     */
    @Multiline String SELECT_CONVERSIONS_BONUS_DETAILS_BY_CONVERSION_IDS = "";

    /**
        SELECT
            internal_transaction_id internalTransactionId,
            param_name paramName,
            param_value paramValue,
            created_by creator,
            created_on creationTime
        FROM
            click_parameters
        WHERE
            merchant_campaign_no = #{campaignId}
        ORDER BY
            param_name
     */
    @Multiline String SELECT_CLICK_PARAMETER_BY_CAMPAIGN_ID = "";

    /**
        SELECT
            COUNT(*)
        FROM
            sales_log s
        WHERE
            s.merchant_campaign_no = #{campaignId}
        AND
            s.sales_date BETWEEN #{from} AND #{to}
        AND
            s.reward_edit_date IS NULL;
     */
    @Multiline String COUNT_CONVERSION_WITH_NULL_REWARD_EDIT_DATE = "";

    /**
        SELECT
            COUNT(*)
        FROM
            publisher_account_payment_history pph
        WHERE
            pph.publisher_id = #{partnerAccount}
        AND
            pph.payment_state = 1
        AND
            pph.invoice_id IN
            <foreach item="item" index="index" collection="invoiceNumbers"
                open="(" separator="," close=")">
                #{item}
            </foreach>
     */
    @Multiline String COUNT_PAID_PAYMENT = "";

    /**
        SELECT
            campaign_id campaignId,
            site_id siteId,
            from_date fromDate,
            to_date toDate,
            old_rank oldRank,
            new_rank newRank,
            rank_updater rankUpdater,
            rank_update_date rankUpdateDate
        FROM
            conversion_rank_update_history
        WHERE
            campaign_id = #{campaignId}
        AND
            site_id = #{siteId}
     */
    @Multiline String SELECT_CONVERSION_RANK_UPDATED_HISTORY = "";

    /**
        SELECT
            seq_no
        FROM
            sales_log
        WHERE
            seq_no < #{conversionId}
        ORDER BY
            seq_no
     */
    @Multiline String SELECT_CONVERSION_IDS = "";

    /**
        DELETE FROM
            sales_log
        WHERE
            seq_no in
            <foreach item="item" index="index" collection="conversionIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
     */
    @Multiline String DELETE_CONVERSIONS_BY_CONVERSION_IDS = "";

    /**
        DELETE FROM
            click_parameters
     */
    @Multiline String DELETE_CONVERSION_PARAMETERS = "";

    /**
    SELECT
        id id,
        bonus_setting_id bonusSettingId,
        publisher_bonus publisherBonus,
        publisher_agent_bonus publisherAgentBonus,
        merchant_agent_bonus merchantAgentBonus,
        at_bonus atBonus,
        publisher_bonus_in_usd publisherBonusInUsd,
        publisher_agent_bonus_in_usd publisherAgentBonusInUsd,
        merchant_agent_bonus_in_usd merchantAgentBonusInUsd,
        at_bonus_in_usd atBonusInUsd,
        site_id siteId,
        campaign_id campaignId,
        status status,
        confirmed_date confirmedDate,
        created_by createdBy,
        updated_by updatedBy
    FROM
        bonus
    WHERE
        bonus_setting_id IN
        <foreach item="item" index="index" collection="bonusSettingIds"
            open="(" separator="," close=")">
            #{item}
        </foreach>
     */
    @Multiline String SELECT_FIXED_BONUS_BY_BONUS_SETTING_IDS = "";


    /**
    SELECT
        id id,
        bonus_id bonusId,
        conversion_id conversionId,
        created_by createdBy
    FROM
        fixed_bonus_details
    WHERE
        bonus_id IN
        <foreach item="item" index="index" collection="bonusIds"
            open="(" separator="," close=")">
            #{item}
        </foreach>
    ORDER BY
        id
     */
    @Multiline String SELECT_FIXED_BONUS_DETAILS_BY_BONUS_IDS = "";

    /**
        INSERT INTO
            conversion_update_request (
                file_name,
                error_count,
                campaign_id,
                staff_email,
                data_count)
        VALUES (
            #{fileName},
            #{errorCount},
            #{campaignId},
            #{staffEmail},
            #{dataCount}
        )
     */
    @Multiline String INSERT_CONVERSION_UPDATE_REQUEST = "";

    /**
        DELETE FROM
            conversion_update_request
     */
    @Multiline String DELETE_CONVERSION_UPDATE_REQUEST = "";

    /**
        SELECT
            redshift_sync_required as redshiftSyncRequired
        FROM
            sales_log
        WHERE
            seq_no = #{conversionId}
     */
    @Multiline String SELECT_REDSHIFT_SYNC_REQUIRED_BY_CONVERSION_ID = "";

    /**
     * Returns the conversions by given conversion IDs.
     *
     * @param conversionIds
     *            the IDs of given conversion
     * @return the conversions by given conversion IDs
     * @See SELECT_CONVERSIONS_BY_CONVERSION_IDS
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSIONS_BY_CONVERSION_IDS + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "creativeId", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "clickTime", javaType = LocalDateTime.class),
            @Arg(column = "conversionTime", javaType = LocalDateTime.class),
            @Arg(column = "registrationTime", javaType = LocalDateTime.class),
            @Arg(column = "confirmationDate", javaType = LocalDate.class),
            @Arg(column = "transactionId", javaType = String.class),
            @Arg(column = "internalTransactionId", javaType = String.class),
            @Arg(column = "siteId", javaType = Long.class),
            @Arg(column = "rank", javaType = Integer.class),
            @Arg(column = "verify", javaType = String.class),
            @Arg(column = "resultId", javaType = Integer.class),
            @Arg(column = "productId", javaType = String.class),
            @Arg(column = "status", javaType = ConversionStatus.class),
            @Arg(column = "productQuantity", javaType = Integer.class),
            @Arg(column = "productUnitPrice", javaType = BigDecimal.class),
            @Arg(column = "transactionAmount", javaType = BigDecimal.class),
            @Arg(column = "rewardType", javaType = RewardType.class),
            @Arg(column = "transactionReward", javaType = BigDecimal.class),
            @Arg(column = "transactionTotalReward", javaType = BigDecimal.class),
            @Arg(column = "commissionType", javaType = CommissionType.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "agentCommission", javaType = BigDecimal.class),
            @Arg(column = "defaultProductQuantity", javaType = Integer.class),
            @Arg(column = "defaultProductUnitPrice", javaType = BigDecimal.class),
            @Arg(column = "defaultResultId", javaType = Integer.class),
            @Arg(column = "deviceType", javaType = DeviceType.class),
            @Arg(column = "postbackIdDuplicativeFlag", javaType = Boolean.class),
            @Arg(column = "categoryId", javaType = String.class),
            @Arg(column = "discount", javaType = BigDecimal.class),
            @Arg(column = "creator", javaType = String.class),
            @Arg(column = "creationTime", javaType = LocalDateTime.class),
            @Arg(column = "updater", javaType = String.class),
            @Arg(column = "updatedDate", javaType = LocalDate.class),
            @Arg(column = "postbackStatus", javaType = PostbackStatus.class),
            @Arg(column = "customerType", javaType = String.class),
            @Arg(column = "rewardEditDate", javaType = LocalDateTime.class),
            @Arg(column = "clickIp", javaType = String.class),
            @Arg(column = "language", javaType = String.class),
            @Arg(column = "newFlag", javaType = Boolean.class),
            @Arg(column = "bonusSettingId", javaType = Long.class),
            @Arg(column = "publisherBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "atBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "atBonusInUsd", javaType = BigDecimal.class)})
    List<IntegrationTestConversion> findAllConversionsBy(
            @Param("conversionIds") List<Long> conversionIds);

    /**
     * Returns the {@link IntegrationTestClickParameter}s by given {@code campaignId}.
     *
     * @param campaignId
     *            the ID of given campaign
     * @return the {@link IntegrationTestClickParameter}s by given {@code campaignId}
     * @See SELECT_CLICK_PARAMETER_BY_CAMPAIGN_ID
     */
    @Select(SELECT_CLICK_PARAMETER_BY_CAMPAIGN_ID)
    @ConstructorArgs({ @Arg(column = "internalTransactionId", javaType = String.class),
            @Arg(column = "paramName", javaType = String.class),
            @Arg(column = "paramValue", javaType = String.class),
            @Arg(column = "creator", javaType = String.class),
            @Arg(column = "creationTime", javaType = LocalDateTime.class) })
    List<IntegrationTestClickParameter> findClickParametersBy(long campaignId);

    /**
     * Returns the number of conversions by given {@code campaignId}.
     *
     * @param campaignId
     *            the ID of given campaign
     * @param from
     *            start date of sale date
     * @param to
     *            end date of sale date
     * @return the number of conversions
     * @See SELECT_CLICK_PARAMETER_BY_CAMPAIGN_IDS
     */
    @Select(COUNT_CONVERSION_WITH_NULL_REWARD_EDIT_DATE)
    int countConversionsWithNullRewardEditDate(@Param("campaignId") long campaignId,
            @Param("from") LocalDateTime from, @Param("to") LocalDateTime to);

    /**
     * Returns the number of conversions by given {@code campaignId}.
     *
     * @param partnerAccount
     *            the ID of given account
     * @param invoiceNumbers
     *            the number of invoices
     * @return the number of paid payment
     * @See COUNT_PAID_PAYMENT
     */
    @Select(START_TAG_SCRIPT + COUNT_PAID_PAYMENT + END_TAG_SCRIPT)
    int countPaidPaymentBy(@Param("partnerAccount") long partnerAccount,
            @Param("invoiceNumbers") List<String> invoiceNumbers);

    /**
     * Return conversion rank updated history by given {campaignId} and {siteId}.
     * @param campaignId
     *              the id of merchant campaign.
     * @param siteId
     *              the id of partner site.
     * @return conversion rank updated history.
     * @see #SELECT_CONVERSION_RANK_UPDATED_HISTORY
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSION_RANK_UPDATED_HISTORY + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "campaignId", javaType = long.class),
            @Arg(column = "siteId", javaType = long.class),
            @Arg(column = "fromDate", javaType = LocalDateTime.class),
            @Arg(column = "toDate", javaType = LocalDateTime.class),
            @Arg(column = "oldRank", javaType = int.class),
            @Arg(column = "newRank", javaType = int.class),
            @Arg(column = "rankUpdater", javaType = String.class),
            @Arg(column = "rankUpdateDate", javaType = ZonedDateTime.class,
                typeHandler = UtcZonedDateTimeTypeHandler.class) })
    List<IntegrationTestConversionRankUpdatedHistory> findConversionRankUpdateHistoryBy(
            @Param("campaignId") long campaignId, @Param("siteId") long siteId);

    /**
     * Return the conversion IDs by {@code conversionId}.
     *
     * @param conversionId
     *            the given conversion Id
     * @return the conversion IDs by {@code conversionId}
     */
    @Select(SELECT_CONVERSION_IDS)
    List<Long> findCovnersionIds(@Param("conversionId") long conversionId);

    /**
     * Deletes the conversions by {conversionIds}.
     *
     * @param conversionIds
     *            the given conversion IDs
     * @return the number of entries deleted
     */
    @Delete(START_TAG_SCRIPT + DELETE_CONVERSIONS_BY_CONVERSION_IDS + END_TAG_SCRIPT)
    int deleteConversions(@Param("conversionIds") List<Long> conversionIds);

    /**
     * Deletes all conversion parameters.
     *
     * @return the number of entries deleted
     */
    @Delete(DELETE_CONVERSION_PARAMETERS)
    int deleteConversionParameters();

    /**
     * Returns the conversions bonus details by given conversion IDs.
     *
     * @param conversionIds
     *            the IDs of given conversion
     * @return the conversions bonus details by given conversion IDs
     * @See SELECT_CONVERSIONS_BONUS_DETAILS_BY_CONVERSION_IDS
     */
    @Select(START_TAG_SCRIPT + SELECT_CONVERSIONS_BONUS_DETAILS_BY_CONVERSION_IDS + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "conversionId", javaType = Long.class),
            @Arg(column = "bonusSettingId", javaType = Long.class),
            @Arg(column = "publisherBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "atBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "atBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "bonusCreatedBy", javaType = String.class),
            @Arg(column = "updatedBy", javaType = String.class)})
    List<IntegrationTestConversionBonus> findConversionsBonusDetailsBy(
            @Param("conversionIds") List<Long> conversionIds);

    /**
     * Returns the fixed bonus by given bonus setting IDs.
     *
     * @param bonusSettingIds
     *            the IDs of given bonus settings
     * @return the fixed bonus by given bonus setting IDs
     * @See SELECT_FIXED_BONUS_BY_BONUS_SETTING_IDS
     */
    @Select(START_TAG_SCRIPT + SELECT_FIXED_BONUS_BY_BONUS_SETTING_IDS + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = Long.class),
            @Arg(column = "bonusSettingId", javaType = Long.class),
            @Arg(column = "publisherBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonus", javaType = BigDecimal.class),
            @Arg(column = "atBonus", javaType = BigDecimal.class),
            @Arg(column = "publisherBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "merchantAgentBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "atBonusInUsd", javaType = BigDecimal.class),
            @Arg(column = "siteId", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "status", javaType = BonusStatus.class),
            @Arg(column = "confirmedDate", javaType = ZonedDateTime.class,
                    typeHandler = UtcZonedDateTimeTypeHandler.class),
            @Arg(column = "createdBy", javaType = String.class),
            @Arg(column = "updatedBy", javaType = String.class)})
    List<IntegrationTestFixedBonus> findFixedBonusBy(
            @Param("bonusSettingIds") List<Long> bonusSettingIds);

    /**
     * Returns the fixed bonus details by given bonus IDs.
     *
     * @param bonusIds
     *            the IDs of given bonus ids
     * @return the fixed bonus details by given bonus IDs
     * @See SELECT_FIXED_BONUS_DETAILS_BY_BONUS_IDS
     */
    @Select(START_TAG_SCRIPT + SELECT_FIXED_BONUS_DETAILS_BY_BONUS_IDS + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = Long.class),
            @Arg(column = "bonusId", javaType = Long.class),
            @Arg(column = "conversionId", javaType =  Long.class),
            @Arg(column = "createdBy", javaType = String.class)})
    List<IntegrationTestFixedBonusDetails> findFixedBonusDatailsBy(
            @Param("bonusIds") List<Long> bonusIds);

    /**
        SELECT
            invoice_id id,
            pay_date  paidDate
        FROM
            publisher_account_payment_history
        WHERE
            UPPER(invoice_id) IN
            <foreach item="item" index="index" collection="invoiceNumbers"
                open="(" separator="," close=")">
                UPPER(#{item})
            </foreach>
        AND
            payment_state IN (1, 3, 4)
     */
    @Multiline String SELECT_INVOICE_AND_PAID_DATE = "";

    /**
     * Gets invoice id and its paid date.
     *
     * @param invoices
     *      given invoices numbers
     * @return a map of invoice id and its paid date
     */
    @Select(START_TAG_SCRIPT + SELECT_INVOICE_AND_PAID_DATE + END_TAG_SCRIPT)
    @ConstructorArgs({ @Arg(column = "id", javaType = String.class),
            @Arg(column = "paidDate", javaType = LocalDate.class) })
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    List<InvoiceAndPaidDate> getPaidDateOfGivenInvoices(
            @Param("invoiceNumbers") List<String> invoices);

    /**
        SELECT
            id,
            publisher_id publisherId,
            invoice_id invoiceId,
            request_date requestDate,
            wht,
            vat,
            wht_in_usd whtInUsd,
            vat_in_usd vatInUsd,
            created_by creator,
            created_on creationDate,
            updated_by updater,
            updated_on updatedDate,
            note
        FROM
            invoice_payment_tax_calculation
        WHERE
            invoice_id = #{invoiceId}
        AND
            publisher_id = #{publisherId}
     */
    @Multiline String SELECT_INVOICE_PAYMENT_TAX_CALCULATION = "";

    /**
     * Returns the invoice payment tax.
     *
     * @param publisherId
     *            the ID of given publisher
     * @param invoiceId
     *            the ID of given invoice
     * @return the invoice payment tax
     * @See SELECT_INVOICE_PAYMENT_TAX_CALCULATION
     */
    @Select(SELECT_INVOICE_PAYMENT_TAX_CALCULATION)
    @ConstructorArgs({ @Arg(column = "id", javaType = Long.class),
            @Arg(column = "publisherId", javaType = Long.class),
            @Arg(column = "invoiceId", javaType =  String.class),
            @Arg(column = "requestDate", javaType = LocalDateTime.class),
            @Arg(column = "wht", javaType =  BigDecimal.class),
            @Arg(column = "vat", javaType =  BigDecimal.class),
            @Arg(column = "whtInUsd", javaType =  BigDecimal.class),
            @Arg(column = "vatInUsd", javaType =  BigDecimal.class),
            @Arg(column = "creator", javaType =  String.class),
            @Arg(column = "creationDate", javaType =  LocalDateTime.class),
            @Arg(column = "updater", javaType =  String.class),
            @Arg(column = "updatedDate", javaType =  LocalDateTime.class),
            @Arg(column = "note", javaType =  String.class),})
    List<IntegrationTestInvoicePaymentTax> findInvoicePaymentTax(
            @Param("publisherId") long publisherId, @Param("invoiceId") String invoiceId);

    /**
     * Inserts a conversion update request by {@code fileName} and {@code errorCount}.
     *
     * @param fileName
     *            given the file name
     * @param errorCount
     *            given the error count
     * @param campaignId
     *            given the campaign id
     * @param staffEmail
     *            given the staff member
     * @param dataCount
     *            given the data count
     * @return the number of entries inserted
     * @see #INSERT_CONVERSION_UPDATE_REQUEST
     */
    @Insert(INSERT_CONVERSION_UPDATE_REQUEST)
    int insertCounversionUpdateRequest(@Param("fileName") String fileName,
            @Param("errorCount") int errorCount, @Param("campaignId") long campaignId,
            @Param("staffEmail") String staffEmail, @Param("dataCount") long dataCount);

    /**
     * Deletes all conversion update request.
     *
     * @return the number of entries deleted
     */
    @Delete(DELETE_CONVERSION_UPDATE_REQUEST)
    int deleteCounversionUpdateRequest();

    /**
     * Returns the redshift_sync_required flag for a specific conversion.
     *
     * @param conversionId
     *            the ID of the conversion
     * @return the number of redshift sync required
     * @see #SELECT_REDSHIFT_SYNC_REQUIRED_BY_CONVERSION_ID
     */
    @Select(SELECT_REDSHIFT_SYNC_REQUIRED_BY_CONVERSION_ID)
    Integer getRedshiftSyncRequiredByConversionId(@Param("conversionId") long conversionId);
}
