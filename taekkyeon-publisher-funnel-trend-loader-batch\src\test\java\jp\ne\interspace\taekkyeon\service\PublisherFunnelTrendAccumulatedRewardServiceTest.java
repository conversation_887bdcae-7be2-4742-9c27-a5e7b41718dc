/**
 * Copyright © 2025 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableMap;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import jp.ne.interspace.taekkyeon.junit.TaekkyeonMockitoJunitRunner;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonModules;
import jp.ne.interspace.taekkyeon.junit.TaekkyeonPropertiesJunitModule;
import jp.ne.interspace.taekkyeon.model.ApprovedRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.OccurredRewardFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherAccountType;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelAccumulatedRewardResult;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsByTime;
import jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper.ConversionLogMapper;

import static java.util.Arrays.asList;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for {@link PublisherFunnelTrendAccumulatedRewardService}.
 *
 * <AUTHOR> Tran
 */
@RunWith(TaekkyeonMockitoJunitRunner.class)
@TaekkyeonModules(TaekkyeonPropertiesJunitModule.class)
public class PublisherFunnelTrendAccumulatedRewardServiceTest {

    private static final BigDecimal SALES_REWARD_100 = new BigDecimal("100.00");
    private static final BigDecimal TRANSACTION_AMOUNT_REWARD_200 = new BigDecimal("200.00");
    private static final BigDecimal AT_COMMISSION_50 = new BigDecimal("50.00");
    private static final BigDecimal MERCHANT_AGENT_COMMISSION_30 = new BigDecimal("30.00");
    private static final BigDecimal PUBLISHER_AGENT_COMMISSION_20 = new BigDecimal("20.00");

    private static final Long ACCOUNT_ID_1 = 100L;
    private static final Long ACCOUNT_ID_2 = 200L;
    private static final Long SITE_ID_1 = 1001L;
    private static final Long SITE_ID_2 = 1002L;
    private static final Long SITE_ID_3 = 2001L;
    private static final Long SITE_ID_4 = 2002L;

    @InjectMocks @Spy
    private PublisherFunnelTrendAccumulatedRewardService underTest;

    @Mock
    private ConversionLogMapper conversionsMapper;

    @Test
    public void testCollectDataForAccountIdsWithTimeRangeShouldReturnCorrectResultWhenValidDataProvided() {
        // given
        Map<LocalDateTime, List<Long>> accountIdsByLatestTime =
                createAccountIdsByLatestTime();
        LocalDateTime targetTimeEnd = LocalDateTime.of(2025, 1, 15, 12, 0);
        LocalDateTime latestTime1 = LocalDateTime.of(2025, 1, 15, 10, 0);
        LocalDateTime latestTime2 = LocalDateTime.of(2025, 1, 15, 11, 0);
        List<Long> accountList1 = asList(100L);
        List<Long> accountList2 = asList(200L);

        Map<Long, ApprovedRewardFunnelDetails> mockApprovedRewards1 = ImmutableMap.of(
                1001L, new ApprovedRewardFunnelDetails(
                1001L, SALES_REWARD_100, TRANSACTION_AMOUNT_REWARD_200,
                        AT_COMMISSION_50, MERCHANT_AGENT_COMMISSION_30,
                        PUBLISHER_AGENT_COMMISSION_20));
        Map<Long, ApprovedRewardFunnelDetails> mockApprovedRewards2 = ImmutableMap.of(
                2001L, new ApprovedRewardFunnelDetails(
                2001L, SALES_REWARD_100, TRANSACTION_AMOUNT_REWARD_200,
                        AT_COMMISSION_50, MERCHANT_AGENT_COMMISSION_30,
                        PUBLISHER_AGENT_COMMISSION_20));
        Map<Long, OccurredRewardFunnelDetails> mockOccurredRewards1 = ImmutableMap.of(
                1001L, new OccurredRewardFunnelDetails(
                1001L, SALES_REWARD_100, TRANSACTION_AMOUNT_REWARD_200,
                        AT_COMMISSION_50, MERCHANT_AGENT_COMMISSION_30,
                        PUBLISHER_AGENT_COMMISSION_20));
        Map<Long, OccurredRewardFunnelDetails> mockOccurredRewards2 = ImmutableMap.of(
                2001L, new OccurredRewardFunnelDetails(
                2001L, SALES_REWARD_100, TRANSACTION_AMOUNT_REWARD_200,
                        AT_COMMISSION_50, MERCHANT_AGENT_COMMISSION_30,
                        PUBLISHER_AGENT_COMMISSION_20));

        when(conversionsMapper.findApprovedRewardFunnelDetailsWithTimeRange(
                accountList1, latestTime1, targetTimeEnd))
                .thenReturn(mockApprovedRewards1);
        when(conversionsMapper.findApprovedRewardFunnelDetailsWithTimeRange(
                accountList2, latestTime2, targetTimeEnd))
                .thenReturn(mockApprovedRewards2);
        when(conversionsMapper.findOccurredRewardFunnelDetailsWithTimeRange(
                accountList1, latestTime1, targetTimeEnd))
                .thenReturn(mockOccurredRewards1);
        when(conversionsMapper.findOccurredRewardFunnelDetailsWithTimeRange(
                accountList2, latestTime2, targetTimeEnd))
                .thenReturn(mockOccurredRewards2);

        // when
        PublisherFunnelAccumulatedRewardResult actual = underTest
                .collectDataForAccountIdsWithTimeRange(
                accountIdsByLatestTime, targetTimeEnd);

        // then
        assertNotNull(actual);
        verify(conversionsMapper).findApprovedRewardFunnelDetailsWithTimeRange(
                accountList1, latestTime1, targetTimeEnd);
        verify(conversionsMapper).findApprovedRewardFunnelDetailsWithTimeRange(
                accountList2, latestTime2, targetTimeEnd);
        verify(conversionsMapper).findOccurredRewardFunnelDetailsWithTimeRange(
                accountList1, latestTime1, targetTimeEnd);
        verify(conversionsMapper).findOccurredRewardFunnelDetailsWithTimeRange(
                accountList2, latestTime2, targetTimeEnd);
    }

    @Test
    public void testCollectDataForAccountIdsWithTimeRangeShouldHandleEmptyAccountIdsByLatestTime() {
        // given
        Map<LocalDateTime, List<Long>> emptyAccountIdsByLatestTime = new HashMap<>();
        LocalDateTime targetTimeEnd = LocalDateTime.of(2025, 1, 15, 12, 0);

        // when
        PublisherFunnelAccumulatedRewardResult actual =
                underTest.collectDataForAccountIdsWithTimeRange(
                        emptyAccountIdsByLatestTime, targetTimeEnd);

        // then
        assertNotNull(actual);
        verify(conversionsMapper, times(0))
                .findApprovedRewardFunnelDetailsWithTimeRange(
                        anyList(), any(LocalDateTime.class), any(LocalDateTime.class));
        verify(conversionsMapper, times(0))
                .findOccurredRewardFunnelDetailsWithTimeRange(
                        anyList(), any(LocalDateTime.class), any(LocalDateTime.class));
    }

    private List<PublisherFunnelDetailsByTime> createPublisherFunnelDetailsByTimes() {
        LocalDateTime latestTime1 = LocalDateTime.of(2025, 1, 15, 10, 0);
        LocalDateTime latestTime2 = LocalDateTime.of(2025, 1, 15, 11, 0);

        PublisherFunnelDetailsByTime details1 = createPublisherFunnelDetailsByTime(
                ACCOUNT_ID_1, latestTime1);
        PublisherFunnelDetailsByTime details2 = createPublisherFunnelDetailsByTime(
                ACCOUNT_ID_2, latestTime2);

        return asList(details1, details2);
    }

    private PublisherFunnelDetailsByTime createPublisherFunnelDetailsByTime(
            long accountId, LocalDateTime latestTime) {

        return new PublisherFunnelDetailsByTime(
                accountId, "JP", null, "test_source", PublisherAccountType.INDIVIDUAL,
                LocalDate.of(2024, 1, 1), LocalDate.of(2024, 1, 2),
                LocalDate.of(2024, 1, 3), LocalDate.of(2024, 1, 4),
                LocalDate.of(2024, 1, 5), LocalDate.of(2024, 1, 6),
                LocalDate.of(2024, 1, 7), LocalDate.of(2024, 1, 8),
                LocalDate.of(2024, 1, 9),
                SALES_REWARD_100, TRANSACTION_AMOUNT_REWARD_200, AT_COMMISSION_50,
                MERCHANT_AGENT_COMMISSION_30, PUBLISHER_AGENT_COMMISSION_20,
                SALES_REWARD_100, TRANSACTION_AMOUNT_REWARD_200, AT_COMMISSION_50,
                MERCHANT_AGENT_COMMISSION_30, PUBLISHER_AGENT_COMMISSION_20,
                "http://test.com", "test.com", "test_medium", "test_content",
                "test_campaign", "test_term", "<EMAIL>", latestTime);
    }

    private Map<LocalDateTime, List<Long>> createAccountIdsByLatestTime() {
        Map<LocalDateTime, List<Long>> accountIdsByLatestTime = new HashMap<>();
        LocalDateTime latestTime1 = LocalDateTime.of(2025, 1, 15, 10, 0);
        LocalDateTime latestTime2 = LocalDateTime.of(2025, 1, 15, 11, 0);

        accountIdsByLatestTime.put(latestTime1, asList(ACCOUNT_ID_1));
        accountIdsByLatestTime.put(latestTime2, asList(ACCOUNT_ID_2));

        return accountIdsByLatestTime;
    }
}
