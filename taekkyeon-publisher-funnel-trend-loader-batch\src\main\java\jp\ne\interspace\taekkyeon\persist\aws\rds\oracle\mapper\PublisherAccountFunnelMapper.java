/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import jp.ne.interspace.taekkyeon.model.FirstApprovedAffiliationDetails;
import jp.ne.interspace.taekkyeon.model.PublisherAccountFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherAccountType;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetails;
import jp.ne.interspace.taekkyeon.model.PublisherFunnelDetailsByTime;
import jp.ne.interspace.taekkyeon.model.Site;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEFAULT_ORACLE_FETCH_SIZE;

/**
 * Mybatis mapper for handling publisher funnel.
 *
 * <AUTHOR> Van Nguyen
 */
public interface PublisherAccountFunnelMapper {

    String START_TAG_SCRIPT = "<script>";
    String END_TAG_SCRIPT = "</script>";

    /**
        WHERE
            pa.account_no
        IN
            <foreach item="item" index="index" collection="publisherIds"
                open="(" separator="," close=")">
              #{item}
            </foreach>
    */
    @Multiline String WHERE_PUBLISHER_ID_IN = "";

    /**
        SELECT
            pa.account_no                    accountId,
            pa.country_code                  countryCode,
            ra.partner_account_no            referrerId,
            pa.media_source                  utmSource,
            pa.utm_medium                    utmMedium,
            pa.utm_content                   utmContent,
            pa.utm_campaign                  utmCampaign,
            pa.utm_term                      utmTerm,
            partner_type_id                  accountType,
            pa.created_on                    registeredDate,
            pa.first_activation_time         activatedDate,
            MIN(ps.first_approved_time)      firstApprovedSiteDate,
            MIN(a.created_on)                firstAffiliationDate,
            TRUNC(MIN(paph.pay_date), 'DD')  firstPaymentDate,
            pa.registration_referral_url     registrationReferralUrl,
            pa.email                         email
        FROM
            partner_account pa
        LEFT JOIN
            partner_site ps
        ON
            pa.account_no = ps.account_no
        LEFT JOIN
            affiliation a
        ON
            ps.site_no = a.partner_site_no
        LEFT JOIN
            publisher_account_payment_history paph
        ON
            ps.account_no = paph.publisher_id
        AND
            paph.payment_state IN (1, 3)
        LEFT JOIN
            referral_account ra
        ON
            pa.account_no = ra.referral_account_no
    */
    @Multiline String SELECT_PUBLISHER_FUNNEL_DETAILS = "";

    /**
         GROUP BY
             pa.account_no,
             pa.country_code,
             ra.partner_account_no,
             pa.media_source,
             pa.utm_medium,
             pa.utm_content,
             pa.utm_campaign,
             pa.utm_term,
             partner_type_id,
             pa.created_on,
             pa.first_activation_time,
             pa.registration_referral_url,
             pa.email
    */
    @Multiline String GROUP_BY_PUBLISHER_FUNNEL_DETAILS = "";

    /**
        SELECT
            pft.*
        FROM
            publisher_funnel_trend pft
        WHERE
            pft.account_id
        IN
        <foreach item="item" index="index" collection="publisherIds"
                open="(" separator="," close=")">
            #{item}
        </foreach>
     */
    @Multiline String SELECT_PUBLISHER_FUNNEL_DETAILS_BY_TIME = "";

    /**
        MERGE INTO
            publisher_funnel_trend tgt
        USING (
            <foreach collection="items" item="item" separator="UNION ALL">
                SELECT
                    #{item.accountId} AS account_id,
                    #{item.accountType} AS account_type,
                    #{item.registeredDate, jdbcType=DATE} AS registered_date,
                    #{item.activatedDate, jdbcType=DATE} AS activated_date,
                    #{item.firstApprovedSiteDate, jdbcType=DATE} AS first_approved_site_date,
                    #{item.firstAffiliationDate, jdbcType=DATE} AS first_affiliation_date,
                    #{item.firstApproveAffiliationDate, jdbcType=DATE} AS first_approve_affiliation_date,
                    #{item.firstImpressionOrClickDate, jdbcType=DATE} AS first_impression_or_click_date,
                    #{item.firstConversionDate, jdbcType=DATE} AS first_conversion_date,
                    #{item.firstApprovedConversionDate, jdbcType=DATE} AS first_approved_conversion_date,
                    #{item.firstPaymentDate, jdbcType=DATE} AS first_payment_date,
                    #{item.referrerId, jdbcType=BIGINT} AS referrer_id,
                    #{item.utmSource, jdbcType=VARCHAR} AS utm_source,
                    #{item.registrationReferralUrl, jdbcType=VARCHAR} AS registration_referral_url,
                    #{item.registrationReferralDomain, jdbcType=VARCHAR} AS registration_referral_domain,
                    #{item.utmMedium, jdbcType=VARCHAR} AS utm_medium,
                    #{item.utmContent, jdbcType=VARCHAR} AS utm_content,
                    #{item.utmCampaign, jdbcType=VARCHAR} AS utm_campaign,
                    #{item.utmTerm, jdbcType=VARCHAR} AS utm_term,
                    #{item.email, jdbcType=VARCHAR} AS email,
                    #{item.countryCode} AS country_code,
                    #{item.occurredSalesReward} AS occurred_sales_reward,
                    #{item.occurredTransactionAmountReward} AS occurred_transaction_amount_reward,
                    #{item.occurredAtCommission} AS occurred_at_commission,
                    #{item.occurredMerchantAgentCommission} AS occurred_merchant_agent_commission,
                    #{item.occurredPublisherAgentCommission} AS occurred_publisher_agent_commission,
                    #{item.approvedSalesReward} AS approved_sales_reward,
                    #{item.approvedTransactionAmountReward} AS approved_transaction_amount_reward,
                    #{item.approvedAtCommission} AS approved_at_commission,
                    #{item.approvedMerchantAgentCommission} AS approved_merchant_agent_commission,
                    #{item.approvedPublisherAgentCommission} AS approved_publisher_agent_commission,
                    #{targetTimeEnd, jdbcType=DATE} AS latest_updated_time
                FROM
                    dual
            </foreach>
        ) src
        ON
            (tgt.ACCOUNT_ID = src.ACCOUNT_ID)
        WHEN MATCHED THEN
            UPDATE SET
                tgt.occurred_sales_reward = src.occurred_sales_reward,
                tgt.occurred_transaction_amount_reward = src.occurred_transaction_amount_reward,
                tgt.occurred_at_commission = src.occurred_at_commission,
                tgt.occurred_merchant_agent_commission = src.occurred_merchant_agent_commission,
                tgt.occurred_publisher_agent_commission = src.occurred_publisher_agent_commission,
                tgt.approved_sales_reward = src.approved_sales_reward,
                tgt.approved_transaction_amount_reward = src.approved_transaction_amount_reward,
                tgt.approved_at_commission = src.approved_at_commission,
                tgt.approved_merchant_agent_commission = src.approved_merchant_agent_commission,
                tgt.approved_publisher_agent_commission = src.approved_publisher_agent_commission,
                tgt.latest_updated_time = src.latest_updated_time
        WHEN NOT MATCHED THEN
            INSERT (
                account_id, account_type, registered_date, activated_date,
                first_approved_site_date, first_affiliation_date, first_approve_affiliation_date,
                first_impression_or_click_date, first_conversion_date, first_approved_conversion_date,
                first_payment_date, referrer_id, utm_source, registration_referral_url,
                registration_referral_domain, utm_medium, utm_content, utm_campaign, utm_term,
                email, country_code, occurred_sales_reward, occurred_transaction_amount_reward,
                occurred_at_commission, occurred_merchant_agent_commission, occurred_publisher_agent_commission,
                approved_sales_reward, approved_transaction_amount_reward, approved_at_commission,
                approved_merchant_agent_commission, approved_publisher_agent_commission,
                latest_updated_time
            )
            VALUES (
                src.account_id, src.account_type, src.registered_date, src.activated_date,
                src.first_approved_site_date, src.first_affiliation_date, src.first_approve_affiliation_date,
                src.first_impression_or_click_date, src.first_conversion_date, src.first_approved_conversion_date,
                src.first_payment_date, src.referrer_id, src.utm_source, src.registration_referral_url,
                src.registration_referral_domain, src.utm_medium, src.utm_content, src.utm_campaign, src.utm_term,
                src.email, src.country_code, src.occurred_sales_reward, src.occurred_transaction_amount_reward,
                src.occurred_at_commission, src.occurred_merchant_agent_commission, src.occurred_publisher_agent_commission,
                src.approved_sales_reward, src.approved_transaction_amount_reward, src.approved_at_commission,
                src.approved_merchant_agent_commission, src.approved_publisher_agent_commission,
                src.latest_updated_time
            )
     */
    @Multiline String UPSERT_PUBLISHER_FUNNEL_TREND = "";

    /**
        SELECT
            pa.account_no accountId,
            TRUNC(MIN(a.approve_date)) firstApprovedAffiliationDate
        FROM
            partner_account pa
        INNER JOIN
            partner_site ps
        ON
            pa.account_no = ps.account_no
        INNER JOIN
            affiliation a
        ON
            ps.site_no = a.partner_site_no
        AND
            a.affiliation_status = 2
    */
    @Multiline String SELECT_FIRST_APPROVED_AFFILIATION_DATE = "";

    /**
        GROUP BY pa.account_no
    */
    @Multiline String GROUP_BY_ACCOUNT_ID = "";

    /**
        SELECT
            ps.account_no publisherId,
            ps.site_no siteId
        FROM
            partner_site ps
        WHERE
            ps.site_state = 1
        AND
            ps.account_no
        IN
            <foreach item="item" index="index" collection="publisherIds"
                open="(" separator="," close=")">
              #{item}
            </foreach>
    */
    @Multiline String SELECT_ACTIVATED_SITE_IDS_BY_PUBLISHER_IDS = "";

    /**
     * Returns registered publisher details by given target publisher IDs.
     *
     * @param publisherIds
     *          given publisher IDs
     * @return registered publisher details by given publisher IDs
     *
     * @see #SELECT_PUBLISHER_FUNNEL_DETAILS
     */
    @Select(START_TAG_SCRIPT + SELECT_PUBLISHER_FUNNEL_DETAILS + WHERE_PUBLISHER_ID_IN
                    + GROUP_BY_PUBLISHER_FUNNEL_DETAILS + END_TAG_SCRIPT)
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    @ConstructorArgs({ @Arg(column = "countryCode", javaType = String.class),
            @Arg(column = "accountId", javaType = long.class),
            @Arg(column = "referrerId", javaType = Long.class),
            @Arg(column = "utmSource", javaType = String.class),
            @Arg(column = "utmMedium", javaType = String.class),
            @Arg(column = "utmContent", javaType = String.class),
            @Arg(column = "utmCampaign", javaType = String.class),
            @Arg(column = "utmTerm", javaType = String.class),
            @Arg(column = "accountType", javaType = PublisherAccountType.class),
            @Arg(column = "registeredDate", javaType = LocalDate.class),
            @Arg(column = "activatedDate", javaType = LocalDate.class),
            @Arg(column = "firstApprovedSiteDate", javaType = LocalDate.class),
            @Arg(column = "firstAffiliationDate", javaType = LocalDate.class),
            @Arg(column = "firstPaymentDate", javaType = LocalDate.class),
            @Arg(column = "registrationReferralUrl", javaType = String.class),
            @Arg(column = "email", javaType = String.class)})
    @MapKey("accountId")
    Map<Long, PublisherAccountFunnelDetails> findPublisherAccountFunnelDetailsBy(
            @Param("publisherIds") List<Long> publisherIds);

    /**
     * Returns publisher funnel details by given publisher IDs.
     *
     * @param publisherIds
     *          given publisher IDs
     * @return list of publisher funnel details by given publisher IDs
     *
     * @see #SELECT_PUBLISHER_FUNNEL_DETAILS_BY_TIME
     */
    @Select(START_TAG_SCRIPT + SELECT_PUBLISHER_FUNNEL_DETAILS_BY_TIME + END_TAG_SCRIPT)
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    @ConstructorArgs({ @Arg(column = "account_id", javaType = long.class),
            @Arg(column = "country_code", javaType = String.class),
            @Arg(column = "referrer_id", javaType = Long.class),
            @Arg(column = "utm_source", javaType = String.class),
            @Arg(column = "account_type", javaType = PublisherAccountType.class),
            @Arg(column = "registered_date", javaType = LocalDate.class),
            @Arg(column = "activated_date", javaType = LocalDate.class),
            @Arg(column = "first_approved_site_date", javaType = LocalDate.class),
            @Arg(column = "first_affiliation_date", javaType = LocalDate.class),
            @Arg(column = "first_approve_affiliation_date", javaType = LocalDate.class),
            @Arg(column = "first_impression_or_click_date", javaType = LocalDate.class),
            @Arg(column = "first_conversion_date", javaType = LocalDate.class),
            @Arg(column = "first_approved_conversion_date", javaType = LocalDate.class),
            @Arg(column = "first_payment_date", javaType = LocalDate.class),
            @Arg(column = "occurred_sales_reward", javaType = BigDecimal.class),
            @Arg(column = "occurred_transaction_amount_reward", javaType = BigDecimal.class),
            @Arg(column = "occurred_at_commission", javaType = BigDecimal.class),
            @Arg(column = "occurred_merchant_agent_commission", javaType = BigDecimal.class),
            @Arg(column = "occurred_publisher_agent_commission", javaType = BigDecimal.class),
            @Arg(column = "approved_sales_reward", javaType = BigDecimal.class),
            @Arg(column = "approved_transaction_amount_reward", javaType = BigDecimal.class),
            @Arg(column = "approved_at_commission", javaType = BigDecimal.class),
            @Arg(column = "approved_merchant_agent_commission", javaType = BigDecimal.class),
            @Arg(column = "approved_publisher_agent_commission", javaType = BigDecimal.class),
            @Arg(column = "registration_referral_url", javaType = String.class),
            @Arg(column = "registration_referral_domain", javaType = String.class),
            @Arg(column = "utm_medium", javaType = String.class),
            @Arg(column = "utm_content", javaType = String.class),
            @Arg(column = "utm_campaign", javaType = String.class),
            @Arg(column = "utm_term", javaType = String.class),
            @Arg(column = "email", javaType = String.class),
            @Arg(column = "latest_updated_time", javaType = LocalDateTime.class)})
    List<PublisherFunnelDetailsByTime> findPublisherFunnelDetailsByTime(
            @Param("publisherIds") List<Long> publisherIds);

    /**
     * Upserts publisher funnel trend summary by given publisher funnel details.
     *
     * @param publisherFunnelTrendSummaries
     *          list of publisher funnel details to be upserted
     * @param targetTimeEnd
     *          target time end
     * @return number of rows affected
     *
     * @see #UPSERT_PUBLISHER_FUNNEL_TREND
     */
    @Update(START_TAG_SCRIPT + UPSERT_PUBLISHER_FUNNEL_TREND + END_TAG_SCRIPT)
    int upsertPublisherFunnelTrendSummary(
            @Param("items") List<PublisherFunnelDetails> publisherFunnelTrendSummaries,
            @Param("targetTimeEnd") LocalDateTime targetTimeEnd);

    /**
     * Returns first approved affiliation details by given publisher IDs.
     *
     *  @param publisherIds
     *          given publisher IDs
     * @return first affiliation details by given publisher IDs
     *
     * @see #SELECT_FIRST_APPROVED_AFFILIATION_DATE
     */
    @Select(START_TAG_SCRIPT + SELECT_FIRST_APPROVED_AFFILIATION_DATE
                    + WHERE_PUBLISHER_ID_IN + GROUP_BY_ACCOUNT_ID + END_TAG_SCRIPT)
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    @ConstructorArgs({ @Arg(column = "accountId", javaType = long.class),
             @Arg(column = "firstApprovedAffiliationDate", javaType = LocalDate.class) })
    @MapKey("accountId")
    Map<Long, FirstApprovedAffiliationDetails> findFirstApprovedAffiliationDetailsBy(
            @Param("publisherIds") List<Long> publisherIds);

    /**
     * Returns {@link List} of activated site by given publisher ID list.
     *
     * @param publisherIds
     *      given publisherIds
     * @return {@link List} of activated site by given publisher ID list.
     *
     * @see #SELECT_ACTIVATED_SITE_IDS_BY_PUBLISHER_IDS
     */
    @Select(START_TAG_SCRIPT + SELECT_ACTIVATED_SITE_IDS_BY_PUBLISHER_IDS
            + END_TAG_SCRIPT)
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    @ConstructorArgs({ @Arg(column = "publisherId", javaType = long.class),
            @Arg(column = "siteId", javaType = long.class) })
    List<Site> findActivatedSiteIdsBy(
            @Param("publisherIds") List<Long> publisherIds);
}
