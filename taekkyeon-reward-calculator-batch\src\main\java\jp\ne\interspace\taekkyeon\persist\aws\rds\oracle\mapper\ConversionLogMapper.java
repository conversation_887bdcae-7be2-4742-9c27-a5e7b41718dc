/**
 * Copyright © 2017 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.persist.aws.rds.oracle.mapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Arg;
import org.apache.ibatis.annotations.ConstructorArgs;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import jp.ne.interspace.taekkyeon.model.CommissionType;
import jp.ne.interspace.taekkyeon.model.Conversion;
import jp.ne.interspace.taekkyeon.model.DeviceType;
import jp.ne.interspace.taekkyeon.model.PublisherAgencyCommissionPolicy;
import jp.ne.interspace.taekkyeon.model.RewardType;
import jp.ne.interspace.taekkyeon.model.UpdateConversionRequest;
import jp.ne.interspace.taekkyeon.multiline.Multiline;

import static jp.ne.interspace.taekkyeon.common.TaekkyeonConstants.DEFAULT_ORACLE_FETCH_SIZE;

/**
 * Mybatis mapper for handling conversion database operations.
 *
 * <AUTHOR> Shin
 */
public interface ConversionLogMapper {

    /**
        <script>
        SELECT
            sl.seq_no id,
            sl.merchant_campaign_no campaignId,
            sl.sales_date conversionTime,
            sl.transaction_id transactionId,
            sl.internal_transaction_id internalTransactionId,
            sl.rank rank,
            sl.result_id resultId,
            sl.device_type deviceType,
            sl.goods_id productId,
            sl.category_id categoryId,
            sl.reward_type rewardType,
            sl.commission_type commissionType,
            sl.sales_count salesCount,
            sl.price price,
            sl.sales_reward reward,
            sl.total_price_reward transactionAmountReward,
            sl.at_commission atCommission,
            sl.agent_commission agentCommission,
            sl.p_agent_commission publisherAgentCommission,
            NVL(pag.commission_policy, 0) publisherAgencyPolicy,
            sl.customer_type customerType,
            sl.discount discount,
            ma.country_code merchantCountryCode,
            sl.total_price transactionAmount
        FROM
            sales_log sl
        INNER JOIN
            merchant_campaign mc
        ON
            sl.merchant_campaign_no = mc.campaign_no
        INNER JOIN
            merchant_account ma
        ON
            ma.account_no = mc.account_no
        LEFT JOIN
            partner_site ps
        ON
            sl.partner_site_no = ps.site_no
        LEFT JOIN
            partner_account pa
        ON
            ps.account_no = pa.account_no
        LEFT JOIN
            publisher_agency pag
        ON
            pa.agency_id = pag.id
        WHERE
            sl.reward_edit_date IS NULL
        AND
            ma.country_code = #{targetCountryCode}
        <![CDATA[
          AND
            ROWNUM <= #{maxRecordCount}
        ]]>
        <if test = "!targetCampaignIds.isEmpty()">
          AND
            sl.merchant_campaign_no IN
            <foreach item="item" index="index" collection="targetCampaignIds"
                    open="(" separator="," close=")">
               #{item}
            </foreach>
        </if>
        <if test = "!excludedCampaignIds.isEmpty()">
          AND
            sl.merchant_campaign_no NOT IN
            <foreach item="item" index="index" collection="excludedCampaignIds"
                    open="(" separator="," close=")">
               #{item}
            </foreach>
        </if>
        </script>
     */
    @Multiline String SELECT_CONVERSIONS_WITHOUT_A_REWARD_EDIT_DATE = "";

    /**
        SELECT
            transaction_id transactionId
        FROM
            sales_log
        WHERE
            internal_transaction_id = #{internalTransactionId}
        AND
            reward_edit_date IS NULL
     */
    @Multiline String SELECT_TRANSACTION_IDS_WITHOUT_A_REWARD_EDIT_DATE = "";

    /**
        <script>
        SELECT
            SUM(sales_reward + total_price_reward + at_commission + agent_commission
                + p_agent_commission) totalReward
        FROM
            sales_log
        WHERE
            internal_transaction_id = #{internalTransactionId}
        AND
            transaction_id NOT IN
            <foreach item="item" index="index" collection="transactionIds"
                    open="(" separator="," close=")">
             #{item}
            </foreach>
        <if test = "categoryId != null">
        AND category_id = #{categoryId}
        </if>
        </script>
     */
    @Multiline
    String SELECT_TOTAL_REWARD_EXCLUDING_TRANSACTION_IDS = "";

    /**
        UPDATE
            sales_log
        SET
            reward_type = #{rewardType},
            sales_reward = #{salesReward},
            total_price_reward = #{transactionAmountReward},
            commission_type = #{commissionType},
            at_commission = #{atCommission},
            agent_commission = #{agentCommission},
            p_agent_commission = #{publisherAgentCommission},
            reward_edit_date = #{latestUpdateTime, jdbcType=DATE,
               typeHandler=jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.UtcZonedDateTimeTypeHandler},
            updated_by = 'RewardCalculatorBatch',
            updated_on = #{latestUpdateTime, jdbcType=DATE,
               typeHandler=jp.ne.interspace.taekkyeon.persist.aws.rdbms.type.handler.UtcZonedDateTimeTypeHandler},
            at_commission_in_usd = #{atCommissionInUsd},
            publisher_reward_in_usd = #{publisherRewardInUsd},
            publisher_agent_commission_in_usd = #{publisherAgentCommissionInUsd},
            merchant_agent_commission_in_usd = #{merchantAgentCommissionInUsd},
            transaction_amount_in_usd = #{transactionAmountInUsd},
            discount_amount_in_usd = #{discountInUsd},
            unit_price_in_usd = #{unitPriceInUsd},
            redshift_sync_required = 1
        WHERE
            seq_no = #{id}
        AND
            reward_edit_date IS NULL
     */
    @Multiline String UPDATE_CONVERSION_REWARDS = "";

    /**
     * Returns the conversions for calculating reward.
     *
     * @param maxRecordCount
     *          maximum record count of search
     * @param targetCountryCode
     *          target country code for calculation
     * @param targetCampaignIds
     *          target campaign IDs for calculation
     * @param excludedCampaignIds
     *          excluded campaign IDs for calculation
     * @return the conversions for calculating reward
     * @see #SELECT_CONVERSIONS_WITHOUT_A_REWARD_EDIT_DATE
     */
    @Select(SELECT_CONVERSIONS_WITHOUT_A_REWARD_EDIT_DATE)
    @Options(fetchSize = DEFAULT_ORACLE_FETCH_SIZE)
    @ConstructorArgs({ @Arg(column = "id", javaType = Long.class),
            @Arg(column = "campaignId", javaType = Long.class),
            @Arg(column = "conversionTime", javaType = LocalDateTime.class),
            @Arg(column = "transactionId", javaType = String.class),
            @Arg(column = "internalTransactionId", javaType = String.class),
            @Arg(column = "rank", javaType = Integer.class),
            @Arg(column = "resultId", javaType = Integer.class),
            @Arg(column = "deviceType", javaType = DeviceType.class),
            @Arg(column = "productId", javaType = String.class),
            @Arg(column = "categoryId", javaType = String.class),
            @Arg(column = "rewardType", javaType = RewardType.class),
            @Arg(column = "commissionType", javaType = CommissionType.class),
            @Arg(column = "salesCount", javaType = BigDecimal.class),
            @Arg(column = "price", javaType = BigDecimal.class),
            @Arg(column = "reward", javaType = BigDecimal.class),
            @Arg(column = "transactionAmountReward", javaType = BigDecimal.class),
            @Arg(column = "atCommission", javaType = BigDecimal.class),
            @Arg(column = "agentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgentCommission", javaType = BigDecimal.class),
            @Arg(column = "publisherAgencyPolicy",
                    javaType = PublisherAgencyCommissionPolicy.class),
            @Arg(column = "customerType", javaType = String.class),
            @Arg(column = "discount", javaType = BigDecimal.class),
            @Arg(column = "merchantCountryCode", javaType = String.class),
            @Arg(column = "transactionAmount", javaType = BigDecimal.class) })
    List<Conversion> findAllWithoutRewardEditDate(
            @Param("maxRecordCount") int maxRecordCount,
            @Param("targetCountryCode") String targetCountryCode,
            @Param("targetCampaignIds") List<Long> targetCampaignIds,
            @Param("excludedCampaignIds") List<Long> excludedCampaignIds);

    /**
     * Returns conversion transaction IDs by the given internal transaction ID.
     *
     * @param internalTransactionId
     *            internal transaction ID of the given conversions
     * @return conversion transaction IDs by the given internal transaction ID
     */
    @Select(SELECT_TRANSACTION_IDS_WITHOUT_A_REWARD_EDIT_DATE)
    Set<String> findTransactionIdsWithoutRewardEditDateBy(String internalTransactionId);

    /**
     * Returns the total reward by the given internal transaction ID,
     * excluding the given transaction IDs.
     *
     * @param internalTransactionId
     *          internal transaction ID of the given conversions
     * @param categoryId
     *          internal  ID of the given conversions
     * @param excludingTransactionIds
     *          transaction IDs of the given conversions
     * @return the total reward by the given internal transaction ID,
     *          excluding the given transaction IDs
     */
    @Select(SELECT_TOTAL_REWARD_EXCLUDING_TRANSACTION_IDS)
    BigDecimal findTotalRewardBy(
            @Param("internalTransactionId") String internalTransactionId,
            @Param("categoryId") String categoryId,
            @Param("transactionIds") List<String> excludingTransactionIds);

    /**
     * Updates the conversions by the given {@link UpdateConversionRequest}.
     *
     * @param conversionRequest
     *          the details of calculated conversion
     * @return the number of conversions updated by the given
     *              {@link UpdateConversionRequest}
     * @see #UPDATE_CONVERSION_REWARDS
     */
    @Update(UPDATE_CONVERSION_REWARDS)
    int updateRewardsBy(UpdateConversionRequest conversionRequest);
}
