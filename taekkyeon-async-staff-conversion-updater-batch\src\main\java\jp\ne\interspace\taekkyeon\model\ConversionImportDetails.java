/**
 * Copyright © 2020 Interspace Co., Ltd. All rights reserved.
 *
 * Licensed under the Interspace's License,
 * you may not use this file except in compliance with the License.
 */
package jp.ne.interspace.taekkyeon.model;

import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * Conversion details for importing.
 *
 * <AUTHOR>
 */
@Getter @AllArgsConstructor @ToString
public class ConversionImportDetails implements ConversionUpdateDetails {

    private final long campaignId;
    private final long clickFromCampaignId;
    private final String campaignName;
    private final LocalDate confirmationDate;
    private final List<ConversionRegistrationDetails> conversions;

    @Override
    public boolean shouldSendSqsMessages() {
        return true;
    }
}
