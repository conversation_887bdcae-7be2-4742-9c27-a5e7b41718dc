SET DATABASE SQL SYNTAX ORA TRUE;

DROP TABLE IF EXISTS PARTNER_SITE;
CREATE TABLE PARTNER_SITE
(
  SITE_NO                NUMBER(10),
  ACCOUNT_NO             NUMBER(10),
  SITE_NAME              VARCHAR2(1024),
  URL                    VARCHAR2(1024),
  DESCRIPTION            VARCHAR2(2000),
  PR                     VARCHAR2(2000),
  CATEGORY_LOW_ID1       NUMBER(10),
  CATEGORY_LOW_ID2       NUMBER(10),
  CATEGORY_LOW_ID3       NUMBER(10),
  SITE_TYPE              NUMBER(2),
  SITE_OPEN_DATE         VARCHAR2(128),
  SITE_STATE             NUMBER(1),
  MAIN_SITE_FLAG         NUMBER(1),
  DEVICE_TYPE            NUMBER(1),
  POINTBACK_FLAG         NUMBER(1),
  CREATED_BY             VARCHAR2(256),
  CREATED_ON             DATE,
  UPDATED_BY             VARCHAR2(256),
  UPDATED_ON             DATE,
  POST<PERSON><PERSON>K_URL           VARCHAR2(1024),
  ALL_BANNERS_FLG        NUMBER(1)  DEFAULT 0,
  FIRST_APPROVED_TIME    DATE,
  SITE_TRAFFIC           NUMBER(2)  DEFAULT 0,
  SITE_LEAD_GENERATION   NUMBER(2)  DEFAULT 0
);

DROP TABLE IF EXISTS PARTNER_ACCOUNT;
CREATE TABLE PARTNER_ACCOUNT
(
  ACCOUNT_NO                     NUMBER(10),
  ACCOUNT_TYPE_ID                NUMBER(1),
  PARTNER_TYPE_ID                NUMBER(2),
  CORPORATE_NAME                 VARCHAR2(128),
  SECTION_NAME                   VARCHAR2(128),
  POST_NAME                      VARCHAR2(128),
  LASTNAME                       VARCHAR2(64),
  FIRSTNAME                      VARCHAR2(64),
  EMAIL                          VARCHAR2(64),
  ZIP_CODE                       VARCHAR2(8),
  PREFECTURE                     VARCHAR2(128),
  CITY                           VARCHAR2(128),
  ADDRESS                        VARCHAR2(512),
  ADDRESS2                       VARCHAR2(512),
  PHONE                          VARCHAR2(32),
  BIRTHDAY                       DATE,
  SEX                            NUMBER(1),
  IDENTIFICATION_NUMBER          VARCHAR2(128),
  BANK_ID                        VARCHAR2(5),
  BANK_NAME                      VARCHAR2(128),
  BANK_BRANCH_ID                 VARCHAR2(8),
  BANK_BRANCH_NAME               VARCHAR2(128),
  BANK_ACCOUNT_TYPE_ID           NUMBER(2),
  BANK_ACCOUNT_NUMBER            VARCHAR2(30),
  BANK_ACCOUNT_OWNER_LASTNAME    VARCHAR2(128),
  BANK_ACCOUNT_OWNER_FIRSTNAME   VARCHAR2(128),
  URL                            VARCHAR2(256),
  LOGIN_NAME                     VARCHAR2(64),
  LOGIN_PASSWORD                 VARCHAR2(32),
  ACCOUNT_STATE                  NUMBER(1),
  APPLIED_DATE                   DATE,
  QUIT_DATE                      DATE,
  OEM_FLAG                       NUMBER(1),
  CREATED_BY                     VARCHAR2(256),
  CREATED_ON                     DATE,
  UPDATED_BY                     VARCHAR2(256),
  UPDATED_ON                     DATE,
  BLACKLIST_FLAG                 NUMBER(1),
  U_ID                           CHAR(32),
  MEDIA_SOURCE                   VARCHAR2(1024),
  COMMERCIAL_REGISTRATION_NUMBER VARCHAR2(128),
  VAT_NUMBER                     VARCHAR2(128),
  AGENCY_ID                      NUMBER(10),
  COUNTRY_CODE                   VARCHAR2(2),
  FIRST_ACTIVATION_TIME          DATE,
  ONBOARDING_STATUS              NUMBER(2),
  REGISTRATION_REFERRAL_URL      VARCHAR2(1024),
  UTM_MEDIUM                     VARCHAR2(1024),
  UTM_CONTENT                    VARCHAR2(1024),
  UTM_CAMPAIGN                   VARCHAR2(1024),
  UTM_TERM                   	 VARCHAR2(1024)
)

DROP TABLE IF EXISTS PUBLISHER_ACCOUNT_PAYMENT_HISTORY;
CREATE TABLE PUBLISHER_ACCOUNT_PAYMENT_HISTORY (
    "ID" NUMBER(10,0) NOT NULL,
    "PUBLISHER_ID" NUMBER(10,0) NOT NULL,
    "CAMPAIGN_CLOSURE_ID" NUMBER(10, 0) DEFAULT 0 NOT NULL,
    "PAY_DATE" DATE,
    "REWARD_MONTH" DATE NOT NULL,
    "AMOUNT" NUMBER(15,2),
    "PAYMENT_STATE" NUMBER(2,0),
    "REQUEST_DATE" DATE,
    "CREATED_BY" VARCHAR2(128),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(128),
    "UPDATED_ON" DATE,
    "INVOICE_ID" VARCHAR2(17),
    "AMOUNT_IN_USD" NUMBER(20,2)
);

DROP TABLE IF EXISTS AFFILIATION_RANK_HISTORY;
CREATE TABLE AFFILIATION_RANK_HISTORY
(
  PARTNER_SITE_NO      NUMBER(10),
  MERCHANT_CAMPAIGN_NO NUMBER(10),
  TARGET_MONTH         DATE,
  RANK                 NUMBER(2),
  CREATED_BY           VARCHAR2(256),
  CREATED_ON           DATE,
  UPDATED_BY           VARCHAR2(256),
  UPDATED_ON           DATE
);

DROP TABLE IF EXISTS AFFILIATION;
CREATE TABLE AFFILIATION
(
  PARTNER_SITE_NO       NUMBER(10),
  MERCHANT_CAMPAIGN_NO  NUMBER(10),
  AFFILIATION_STATUS    NUMBER(2),
  REGISTRATION_DATE     DATE,
  COMPLETION_DATE       DATE,
  RANK                  NUMBER(2),
  NEXT_RANK             NUMBER(2),
  RANK_EDIT_DATE        DATE,
  CREATED_BY            VARCHAR2(256),
  CREATED_ON            DATE,
  UPDATED_BY            VARCHAR2(256),
  UPDATED_ON            DATE,
  APPROVE_DATE          DATE,
  REJECT_DATE           DATE,
  SYNC_STATUS           NUMBER(1)
);

DROP TABLE IF EXISTS REFERRAL_ACCOUNT;
CREATE TABLE REFERRAL_ACCOUNT
(
  PARTNER_ACCOUNT_NO  NUMBER(10)           NOT NULL,
  REFERRAL_ACCOUNT_NO NUMBER(10)           NOT NULL,
  REFERRAL_START_DATE DATE DEFAULT SYSDATE NOT NULL,
  REFERRAL_STATUS     NUMBER(1) DEFAULT 0  NOT NULL,
  CREATED_BY          VARCHAR2(256),
  CREATED_ON          DATE,
  UPDATED_BY          VARCHAR2(256),
  UPDATED_ON          DATE,
  REFERRAL_END_DATE   DATE
);

DROP TABLE IF EXISTS SALES_LOG;
CREATE TABLE SALES_LOG (
    "SEQ_NO" NUMBER(10,0),
    "BANNER_ID" NUMBER(10,0) NOT NULL,
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "NODE_ID" VARCHAR2(11 BYTE),
    "CLICK_DATE" DATE,
    "SALES_DATE" DATE NOT NULL,
    "LOG_DATE" DATE NOT NULL,
    "CONFIRMED_DATE" DATE,
    "TRANSACTION_ID" VARCHAR2(256 BYTE) NOT NULL,
    "INTERNAL_TRANSACTION_ID" VARCHAR2(512 BYTE),
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL,
    "RANK" NUMBER(2,0),
    "VERIFY" VARCHAR2(256 BYTE),
    "RESULT_ID" NUMBER(4,0),
    "GOODS_ID" VARCHAR2(256 BYTE),
    "SALES_LOG_STATUS" NUMBER(2,0) NOT NULL,
    "SALES_COUNT" NUMBER(10,0) NOT NULL,
    "PRICE" NUMBER(12,2) NOT NULL,
    "TOTAL_PRICE" NUMBER(12,2) NOT NULL,
    "REWARD_TYPE" NUMBER(1,0) NOT NULL,
    "SALES_REWARD" NUMBER(12,2) NOT NULL,
    "TOTAL_PRICE_REWARD" NUMBER(12,2) NOT NULL,
    "COMMISSION_TYPE" NUMBER(1,0) NOT NULL,
    "AT_COMMISSION" NUMBER(12,2) NOT NULL,
    "AGENT_COMMISSION" NUMBER(12,2) NOT NULL,
    "P_AGENT_COMMISSION" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "IP" VARCHAR2(32 BYTE),
    "CLICK_IP" VARCHAR2(32 BYTE),
    "MEDIA_URL" VARCHAR2(512 BYTE),
    "REFERER" VARCHAR2(2048 BYTE),
    "REPEAT_COUNT" NUMBER(10,0),
    "USER_AGENT" VARCHAR2(512 BYTE),
    "REWARD_EDIT_DATE" DATE,
    "TRACKING_TYPE" NUMBER(2,0),
    "DEFAULT_SALES_COUNT" NUMBER(10,0) NOT NULL,
    "DEFAULT_PRICE" NUMBER(12,2) NOT NULL,
    "DEFAULT_RESULT_ID" NUMBER(4,0),
    "LP_URL" VARCHAR2(512 BYTE),
    "DEVICE_TYPE" NUMBER(2,0),
    "POINTBACK_ID" VARCHAR2(64 BYTE),
    "PB_ID_DUPLICATIVE_FLAG" NUMBER(1,0),
    "PB_ID_OLDEST_SALES_DATE" DATE,
    "CREATED_BY" VARCHAR2(256 BYTE),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256 BYTE),
    "UPDATED_ON" DATE,
    "NEW_FLAG" NUMBER(1,0) DEFAULT 0,
    "SESSION_ID" VARCHAR2(256 BYTE),
    "UUID" VARCHAR2(80 BYTE) DEFAULT NULL,
    "DEVICE_OS" NUMBER(2,0) DEFAULT 0 NOT NULL,
    "CATEGORY_ID" VARCHAR2(256 BYTE),
    "DISCOUNT" NUMBER(12,2) DEFAULT 0,
    "ORIGINAL_CURRENCY_TOTAL_PRICE" NUMBER(12,2),
    "ORIGINAL_CURRENCY" VARCHAR2(3 BYTE),
    "CLICK_REFERER" VARCHAR2(2048 BYTE),
    "CLICK_URL" VARCHAR2(2048 BYTE),
    "CLICK_USER_AGENT" VARCHAR2(2048 BYTE),
    "POSTBACK_STATUS" NUMBER(1,0) DEFAULT 1 NOT NULL,
    "POSTBACK_ERROR_COUNT" NUMBER(1,0) DEFAULT 0 NOT NULL,
    "LATEST_POSTBACK_TIME" DATE,
    "CUSTOMER_TYPE" VARCHAR2(64 BYTE),
    "POSTBACK_URL" VARCHAR2(2048),
    "POSTBACK_ERROR_DETAILS" VARCHAR2(2048),
    "POSTBACK_ERROR_RESPONSE_CODE" NUMBER(3),
    "AT_COMMISSION_IN_USD" NUMBER(12,2),
    "PUBLISHER_REWARD_IN_USD" NUMBER(12,2),
    "PUBLISHER_AGENT_COMMISSION_IN_USD" NUMBER(12,2),
    "MERCHANT_AGENT_COMMISSION_IN_USD" NUMBER(12,2),
    "TRANSACTION_AMOUNT_IN_USD" NUMBER(12,2),
    "DISCOUNT_AMOUNT_IN_USD" NUMBER(12,2),
    "UNIT_PRICE_IN_USD" NUMBER(12,2),
    "LANGUAGE" VARCHAR2(40),
    "BONUS_SETTING_ID" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "PUBLISHER_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "PUBLISHER_AGENT_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "MERCHANT_AGENT_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "AT_BONUS" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "PUBLISHER_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "PUBLISHER_AGENT_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "MERCHANT_AGENT_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "AT_BONUS_IN_USD" NUMBER(12,2) DEFAULT 0 NOT NULL,
    "BONUS_CREATED_BY" VARCHAR2(256),
    "BONUS_CREATED_ON" DATE
);

DROP TABLE IF EXISTS BANNER_ACCESS_LOG_SUMMARY;
CREATE TABLE BANNER_ACCESS_LOG_SUMMARY (
    "BANNER_ID" NUMBER(10,0) NOT NULL,
    "MERCHANT_CAMPAIGN_NO" NUMBER(10,0) NOT NULL,
    "LOG_DATE" DATE NOT NULL,
    "PARTNER_SITE_NO" NUMBER(10,0) NOT NULL,
    "RANK" NUMBER(2,0),
    "RESULT_ID" NUMBER(4,0) NOT NULL,
    "TRACKING_SESSION_ID" VARCHAR2(40),
    "IMPRESSION_COUNT" NUMBER(10,0) NOT NULL,
    "CLICK_COUNT" NUMBER(10,0) NOT NULL,
    "REWARD_TYPE" NUMBER(1,0) NOT NULL,
    "CLICK_REWARD" NUMBER(10,2) NOT NULL,
    "COMMISSION_TYPE" NUMBER(1,0) NOT NULL,
    "AT_COMMISSION" NUMBER(10,2) NOT NULL,
    "AGENT_COMMISSION" NUMBER(10,2) NOT NULL,
    "REWARD_EDIT_DATE" DATE,
    "DEVICE_TYPE" NUMBER(2,0) NOT NULL,
    "DEVICE_OS" NUMBER(2,0) DEFAULT 0 NOT NULL,
    "CREATED_BY" VARCHAR2(256),
    "CREATED_ON" DATE,
    "UPDATED_BY" VARCHAR2(256),
    "UPDATED_ON" DATE,
    "P_AGENT_COMMISSION" NUMBER(10,2) DEFAULT 0 NOT NULL,
    "GOODS_ID" VARCHAR2(256) DEFAULT ' ',
    "CATEGORY_ID" VARCHAR2(250)
);

DROP TABLE IF EXISTS PUBLISHER_FUNNEL_TREND;
CREATE TABLE PUBLISHER_FUNNEL_TREND
(
  ACCOUNT_ID                     NUMBER(10),
  ACCOUNT_TYPE                	 NUMBER(2),
  REGISTERED_DATE                DATE,
  ACTIVATED_DATE          		 DATE,
  FIRST_APPROVED_SITE_DATE       DATE,
  FIRST_AFFILIATION_DATE		 DATE,
  FIRST_APPROVE_AFFILIATION_DATE DATE,
  FIRST_IMPRESSION_OR_CLICK_DATE DATE,
  FIRST_CONVERSION_DATE		 	 DATE,
  FIRST_APPROVED_CONVERSION_DATE DATE,
  FIRST_PAYMENT_DATE		 	 DATE,
  REFERRER_ID  			 		 NUMBER(10),
  UTM_SOURCE                     VARCHAR2(1024),
  REGISTRATION_REFERRAL_URL      VARCHAR2(1024),
  REGISTRATION_REFERRAL_DOMAIN   VARCHAR2(32),
  UTM_MEDIUM                     VARCHAR2(1024),
  UTM_CONTENT                    VARCHAR2(1024),
  UTM_CAMPAIGN                   VARCHAR2(1024),
  UTM_TERM                   	 VARCHAR2(1024),
  EMAIL                          VARCHAR2(64),
  COUNTRY_CODE					 VARCHAR2(2),
  OCCURRED_SALES_REWARD			 NUMBER(12,2),
  OCCURRED_TRANSACTION_AMOUNT_REWARD NUMBER(12,2),
  OCCURRED_AT_COMMISSION NUMBER(12,2),
  OCCURRED_MERCHANT_AGENT_COMMISSION NUMBER(12,2),
  OCCURRED_PUBLISHER_AGENT_COMMISSION NUMBER(12,2),
  APPROVED_SALES_REWARD NUMBER(12,2),
  APPROVED_TRANSACTION_AMOUNT_REWARD NUMBER(12,2),
  APPROVED_AT_COMMISSION NUMBER(12,2),
  APPROVED_MERCHANT_AGENT_COMMISSION NUMBER(12,2),
  APPROVED_PUBLISHER_AGENT_COMMISSION NUMBER(12,2),
  LATEST_UPDATED_TIME DATE
);